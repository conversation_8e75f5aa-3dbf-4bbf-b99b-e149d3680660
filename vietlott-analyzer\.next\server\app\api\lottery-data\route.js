(()=>{var e={};e.id=338,e.ids=[338],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4039:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>f});var o={};r.r(o),r.d(o,{GET:()=>c});var a=r(6559),s=r(8088),n=r(7719),i=r(2190);let l=new Map;async function p(e="power655"){try{let t=await fetch(`https://raw.githubusercontent.com/vietvudanh/vietlott-data/master/data/${"power655"===e?"power655.jsonl":"power645.jsonl"}`,{headers:{"User-Agent":"Vietlott-Analyzer/1.0"}});if(!t.ok)throw Error(`GitHub API error: ${t.status}`);let r=(await t.text()).trim().split("\n"),o=[];for(let t of r)try{let r=JSON.parse(t);if(r.result&&Array.isArray(r.result)&&r.result.length>=6){let t="power655"===e?55:45;r.result.slice(0,6).every(e=>"number"==typeof e&&e>=1&&e<=t)&&r.date&&r.id&&o.push({id:r.id,date:r.date,result:r.result.slice(0,6),powerNumber:"power655"===e&&r.result[6]&&r.result[6]>=1&&r.result[6]<=55?r.result[6]:void 0,processTime:r.process_time})}}catch(e){console.warn("Failed to parse line:",t)}return o.sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())}catch(e){throw console.error("Error fetching from GitHub:",e),e}}async function u(){try{return console.log("Fallback scraping not implemented yet"),[]}catch(e){return console.error("Error in fallback scraping:",e),[]}}async function c(e){try{let{searchParams:t}=new URL(e.url),r=t.get("type")||"power655",o=Date.now(),a=l.get(r);if(a&&o-a.timestamp<3e5)return i.NextResponse.json(a.data);let s=[];try{s=await p(r),console.log(`Fetched ${s.length} results from GitHub for ${r}`)}catch(e){console.error("GitHub fetch failed:",e);try{s=await u(),console.log(`Fetched ${s.length} results from fallback scraping`)}catch(e){console.error("Fallback scraping failed:",e),console.log("Using mock data as fallback"),s=function(e="power655"){let t=[],r=new Date,o="power655"===e?55:45;for(let a=0;a<100;a++){let s=new Date(r);s.setDate(s.getDate()-2*a);let n=[],i=new Set;for(;n.length<6;){let e=Math.floor(Math.random()*o)+1;i.has(e)||(n.push(e),i.add(e))}n.sort((e,t)=>e-t),t.push({id:String(1200-a).padStart(5,"0"),date:s.toISOString().split("T")[0],result:n,powerNumber:"power655"===e?Math.floor(55*Math.random())+1:void 0,processTime:s.toISOString(),lotteryType:e})}return t}(r)}}return s=s.map(e=>({...e,lotteryType:r})),l.set(r,{data:s,timestamp:o}),i.NextResponse.json(s)}catch(e){return console.error("API error:",e),i.NextResponse.json({error:"Failed to fetch lottery data"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/lottery-data/route",pathname:"/api/lottery-data",filename:"route",bundlePath:"app/api/lottery-data/route"},resolvedPagePath:"C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\api\\lottery-data\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:h,workUnitAsyncStorage:f,serverHooks:m}=d;function w(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:f})}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,580],()=>r(4039));module.exports=o})();