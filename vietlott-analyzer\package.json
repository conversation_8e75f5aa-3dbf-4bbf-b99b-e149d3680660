{"name": "vietlott-analyzer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export"}, "dependencies": {"@headlessui/react": "^2.2.4", "axios": "^1.9.0", "chart.js": "^4.4.9", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}