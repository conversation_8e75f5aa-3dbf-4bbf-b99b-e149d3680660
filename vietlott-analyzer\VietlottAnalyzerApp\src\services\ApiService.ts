import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  LotteryResult, 
  LotteryType, 
  StatisticsData, 
  PredictionAlgorithm,
  ApiResponse,
  LotteryDataResponse,
  StatisticsResponse,
  PredictionResponse
} from '../types/lottery';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    // Use your deployed web app URL
    this.baseURL = 'https://vietlott-analyzer.vercel.app';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'VietlottAnalyzer-Mobile/1.0.0',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(this.handleError(error));
      }
    );
  }

  private handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || error.response.statusText;
      return new Error(`API Error (${error.response.status}): ${message}`);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network Error: Unable to connect to server');
    } else {
      // Something else happened
      return new Error(`Request Error: ${error.message}`);
    }
  }

  // Fetch lottery data
  async fetchLotteryData(lotteryType: LotteryType = 'power655'): Promise<LotteryResult[]> {
    try {
      const response = await this.api.get<LotteryResult[]>('/api/lottery-data', {
        params: { type: lotteryType }
      });
      
      return response.data.map(result => ({
        ...result,
        lotteryType,
      }));
    } catch (error) {
      console.error('Failed to fetch lottery data:', error);
      throw error;
    }
  }

  // Fetch latest results (last 5)
  async fetchLatestResults(lotteryType: LotteryType = 'power655'): Promise<LotteryResult[]> {
    try {
      const allResults = await this.fetchLotteryData(lotteryType);
      return allResults.slice(0, 5);
    } catch (error) {
      console.error('Failed to fetch latest results:', error);
      throw error;
    }
  }

  // Generate predictions (using existing web app logic)
  async generatePredictions(lotteryType: LotteryType = 'power655'): Promise<PredictionAlgorithm[]> {
    try {
      // Since the web app doesn't have a predictions endpoint, 
      // we'll fetch data and generate predictions locally
      const data = await this.fetchLotteryData(lotteryType);
      
      // Import the analysis logic from web app
      const { LotteryDataService } = await import('./LotteryDataService');
      const dataService = new LotteryDataService();
      
      const predictions: PredictionAlgorithm[] = [
        {
          id: 'hot_numbers',
          name: 'Hot Numbers',
          description: 'Numbers that appear most frequently in recent draws',
          confidence: 75,
          numbers: dataService.getHotNumbers(data, 6, lotteryType === 'power655' ? 55 : 45),
          powerNumber: lotteryType === 'power655' ? dataService.getHotNumbers(data, 1, 55)[0] : undefined,
          lastUpdated: new Date().toISOString(),
        },
        {
          id: 'cold_numbers',
          name: 'Cold Numbers',
          description: 'Numbers that haven\'t appeared recently and are "due"',
          confidence: 65,
          numbers: dataService.getColdNumbers(data, 6, lotteryType === 'power655' ? 55 : 45),
          powerNumber: lotteryType === 'power655' ? dataService.getColdNumbers(data, 1, 55)[0] : undefined,
          lastUpdated: new Date().toISOString(),
        },
        {
          id: 'balanced',
          name: 'Balanced Mix',
          description: 'Combination of hot and cold numbers for balanced approach',
          confidence: 80,
          numbers: dataService.getBalancedNumbers(data, lotteryType === 'power655' ? 55 : 45),
          powerNumber: lotteryType === 'power655' ? Math.floor(Math.random() * 55) + 1 : undefined,
          lastUpdated: new Date().toISOString(),
        },
        {
          id: 'ai_ensemble',
          name: 'AI Ensemble',
          description: 'Advanced AI algorithm combining multiple prediction methods',
          confidence: 85,
          numbers: dataService.getEnsembleNumbers(data),
          powerNumber: lotteryType === 'power655' ? dataService.getSmartFrequencyNumbers(data)[0] : undefined,
          lastUpdated: new Date().toISOString(),
        },
      ];

      return predictions;
    } catch (error) {
      console.error('Failed to generate predictions:', error);
      throw error;
    }
  }

  // Fetch statistics
  async fetchStatistics(lotteryType: LotteryType = 'power655'): Promise<StatisticsData> {
    try {
      const data = await this.fetchLotteryData(lotteryType);
      
      const { LotteryDataService } = await import('./LotteryDataService');
      const dataService = new LotteryDataService();
      
      return dataService.calculateStatistics(data);
    } catch (error) {
      console.error('Failed to fetch statistics:', error);
      throw error;
    }
  }

  // Check if API is available
  async checkApiHealth(): Promise<boolean> {
    try {
      const response = await this.api.get('/api/lottery-data?type=power655');
      return response.status === 200;
    } catch (error) {
      console.error('API health check failed:', error);
      return false;
    }
  }

  // Get base URL for sharing
  getWebAppUrl(): string {
    return this.baseURL;
  }
}

export default new ApiService();
