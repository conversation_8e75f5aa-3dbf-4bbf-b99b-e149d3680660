import type { Metadata } from "next";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Vietlott Power 6/55 Analyzer",
  description:
    "Historical analysis and number suggestions for Vietnam Vietlott Power 6/55 lottery. Get insights from past draws and generate smart number suggestions.",
  keywords:
    "vietlott, lottery, power 6/55, vietnam, analysis, statistics, number suggestions",
  authors: [{ name: "Vietlott Analyzer" }],
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
