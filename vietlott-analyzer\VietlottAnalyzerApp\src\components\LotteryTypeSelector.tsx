import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { LotteryType, LOTTERY_CONFIGS } from '../types/lottery';

interface LotteryTypeSelectorProps {
  selectedType: LotteryType;
  onTypeChange: (type: LotteryType) => void;
  style?: any;
}

const LotteryTypeSelector: React.FC<LotteryTypeSelectorProps> = ({
  selectedType,
  onTypeChange,
  style,
}) => {
  const renderOption = (type: LotteryType) => {
    const config = LOTTERY_CONFIGS[type];
    const isSelected = selectedType === type;

    return (
      <TouchableOpacity
        key={type}
        style={[
          styles.option,
          isSelected && styles.selectedOption,
          { borderColor: config.color },
          isSelected && { backgroundColor: config.color },
        ]}
        onPress={() => onTypeChange(type)}
        activeOpacity={0.7}
      >
        <Text style={styles.icon}>{config.icon}</Text>
        <Text
          style={[
            styles.optionText,
            isSelected && styles.selectedOptionText,
          ]}
        >
          {config.name}
        </Text>
        <Text
          style={[
            styles.description,
            isSelected && styles.selectedDescription,
          ]}
        >
          {config.description}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>Chọn loại xổ số</Text>
      <View style={styles.optionsContainer}>
        {(Object.keys(LOTTERY_CONFIGS) as LotteryType[]).map(renderOption)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    margin: 10,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  option: {
    flex: 1,
    padding: 15,
    borderRadius: 10,
    borderWidth: 2,
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  selectedOption: {
    backgroundColor: '#FF6B6B',
  },
  icon: {
    fontSize: 24,
    marginBottom: 8,
  },
  optionText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
    textAlign: 'center',
  },
  selectedOptionText: {
    color: 'white',
  },
  description: {
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
    lineHeight: 12,
  },
  selectedDescription: {
    color: 'rgba(255, 255, 255, 0.9)',
  },
});

export default LotteryTypeSelector;
