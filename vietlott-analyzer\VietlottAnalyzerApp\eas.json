{"cli": {"version": ">= 12.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk"}}, "production": {"android": {"buildType": "aab", "gradleCommand": ":app:bundleRelease"}}, "production-apk": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal", "releaseStatus": "draft"}}}}