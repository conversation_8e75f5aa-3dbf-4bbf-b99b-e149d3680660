(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2328:(e,t,r)=>{Promise.resolve().then(r.bind(r,4178))},4178:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>L});var s=r(5155),a=r(2115),n=r(6408),i=r(3904),l=r(3311),c=r(1539),o=r(299),d=r(1284);class m{static getInstance(){return m.instance||(m.instance=new m),m.instance}getLotteryConfigs(){return[{type:"power655",name:"Power 6/55",description:"Pick 6 numbers from 1-55 + 1 power number",maxNumber:55,numbersCount:6,hasPowerNumber:!0,icon:"⚡",color:"from-blue-500 to-indigo-600"},{type:"mega645",name:"Mega 6/45",description:"Pick 6 numbers from 1-45",maxNumber:45,numbersCount:6,hasPowerNumber:!1,icon:"\uD83D\uDC8E",color:"from-purple-500 to-pink-600"}]}getCurrentConfig(){let e=this.getLotteryConfigs();return e.find(e=>e.type===this.currentLotteryType)||e[0]}setCurrentLotteryType(e){this.currentLotteryType=e}getCurrentLotteryType(){return this.currentLotteryType}getPower655Rules(){return["Select 6 different numbers from 1 to 55","Select 1 power number from 1 to 55","No duplicate numbers allowed in main selection","Power number can be same as main numbers","Draws held twice weekly (Tuesday & Friday)","Minimum jackpot: 12 billion VND"]}getMega645Rules(){return["Select 6 different numbers from 1 to 45","No power number required","No duplicate numbers allowed","Draws held twice weekly (Wednesday & Saturday)","Minimum jackpot: 15 billion VND","Better odds than Power 6/55"]}getRulesForCurrentLottery(){return"power655"===this.currentLotteryType?this.getPower655Rules():this.getMega645Rules()}getOdds(e){return"power655"===e?{jackpot:"1 in 139,838,160",match5:"1 in 2,542,512",match4:"1 in 47,415",match3:"1 in 1,235"}:{jackpot:"1 in 8,145,060",match5:"1 in 34,808",match4:"1 in 733",match3:"1 in 45"}}getPrizeStructure(e){return"power655"===e?[{level:"Jackpot",condition:"6 numbers + Power",prize:"Jackpot (min 12B VND)"},{level:"Prize 1",condition:"6 numbers",prize:"40M - 60M VND"},{level:"Prize 2",condition:"5 numbers + Power",prize:"10M - 20M VND"},{level:"Prize 3",condition:"5 numbers",prize:"500K - 1M VND"},{level:"Prize 4",condition:"4 numbers + Power",prize:"200K - 400K VND"},{level:"Prize 5",condition:"4 numbers",prize:"50K - 100K VND"},{level:"Prize 6",condition:"3 numbers + Power",prize:"20K - 50K VND"}]:[{level:"Jackpot",condition:"6 numbers",prize:"Jackpot (min 15B VND)"},{level:"Prize 1",condition:"5 numbers",prize:"10M - 30M VND"},{level:"Prize 2",condition:"4 numbers",prize:"300K - 800K VND"},{level:"Prize 3",condition:"3 numbers",prize:"30K - 80K VND"}]}constructor(){this.currentLotteryType="power655"}}function u(e){let{children:t,className:r="",hover:a=!0,gradient:i=!1,delay:l=0}=e,c="\n    rounded-xl shadow-lg backdrop-blur-sm border border-white/10\n    ".concat(i?"bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-800/90 dark:to-gray-900/70":"bg-white/80 dark:bg-gray-800/80","\n    ").concat(r,"\n  ");return(0,s.jsx)(n.P.div,{className:c,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:l},...a?{whileHover:{scale:1.02,y:-4,transition:{duration:.2}},whileTap:{scale:.98}}:{},children:t})}function h(e){let{children:t,onClick:r,variant:a="primary",size:i="md",disabled:l=!1,loading:c=!1,icon:o,className:d=""}=e,m="\n    ".concat("\n    inline-flex items-center justify-center font-medium rounded-lg\n    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\n    disabled:opacity-50 disabled:cursor-not-allowed\n  ","\n    ").concat({sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[i],"\n    ").concat({primary:"\n      bg-gradient-to-r from-blue-600 to-blue-700 text-white\n      hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500\n      shadow-lg shadow-blue-500/25\n    ",secondary:"\n      bg-gradient-to-r from-gray-600 to-gray-700 text-white\n      hover:from-gray-700 hover:to-gray-800 focus:ring-gray-500\n      shadow-lg shadow-gray-500/25\n    ",success:"\n      bg-gradient-to-r from-green-600 to-green-700 text-white\n      hover:from-green-700 hover:to-green-800 focus:ring-green-500\n      shadow-lg shadow-green-500/25\n    ",danger:"\n      bg-gradient-to-r from-red-600 to-red-700 text-white\n      hover:from-red-700 hover:to-red-800 focus:ring-red-500\n      shadow-lg shadow-red-500/25\n    ",ghost:"\n      bg-transparent text-gray-700 border border-gray-300\n      hover:bg-gray-50 focus:ring-gray-500\n    "}[a],"\n    ").concat(d,"\n  ");return(0,s.jsxs)(n.P.button,{className:m,onClick:r,disabled:l||c,whileHover:{scale:l||c?1:1.02},whileTap:{scale:l||c?1:.98},transition:{duration:.1},children:[c&&(0,s.jsx)(n.P.div,{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),o&&!c&&(0,s.jsx)("span",{className:"mr-2",children:o}),t]})}function x(e){let{currentType:t,onTypeChange:r}=e,[i,l]=(0,a.useState)(!1),x=m.getInstance(),g=x.getLotteryConfigs(),b=g.find(e=>e.type===t)||g[0],p=e=>{r(e),x.setCurrentLotteryType(e)},y=e=>"power655"===e?(0,s.jsx)(c.A,{size:20}):(0,s.jsx)(o.A,{size:20});return(0,s.jsx)(u,{className:"p-6 mb-6",gradient:!0,children:(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-gray-800 mb-4 flex items-center",children:[y(t),(0,s.jsx)("span",{className:"ml-2",children:"Lottery Type Selection"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:g.map(e=>(0,s.jsxs)(n.P.button,{onClick:()=>p(e.type),className:"p-4 rounded-xl border-2 transition-all duration-300 text-left ".concat(t===e.type?"border-blue-500 bg-blue-50 shadow-lg":"border-gray-200 bg-white hover:border-gray-300 hover:shadow-md"),whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-2",children:e.icon}),(0,s.jsx)("h3",{className:"font-bold text-lg",children:e.name})]}),t===e.type&&(0,s.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,s.jsxs)("span",{children:["Numbers: 1-",e.maxNumber]}),(0,s.jsx)("span",{children:e.hasPowerNumber?"With Power":"No Power"})]})]},e.type))}),(0,s.jsxs)(n.P.div,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("h4",{className:"font-semibold text-blue-800 flex items-center",children:[y(t),(0,s.jsxs)("span",{className:"ml-2",children:["Current: ",b.name]})]}),(0,s.jsxs)(h,{onClick:()=>l(!i),variant:"ghost",size:"sm",icon:(0,s.jsx)(d.A,{size:16}),children:[i?"Hide":"Show"," Rules"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2 text-sm",children:[(0,s.jsxs)("div",{className:"text-center p-2 bg-white rounded border",children:[(0,s.jsxs)("div",{className:"font-bold text-blue-600",children:["1-",b.maxNumber]}),(0,s.jsx)("div",{className:"text-gray-600",children:"Number Range"})]}),(0,s.jsxs)("div",{className:"text-center p-2 bg-white rounded border",children:[(0,s.jsx)("div",{className:"font-bold text-blue-600",children:b.numbersCount}),(0,s.jsx)("div",{className:"text-gray-600",children:"Numbers to Pick"})]}),(0,s.jsxs)("div",{className:"text-center p-2 bg-white rounded border",children:[(0,s.jsx)("div",{className:"font-bold text-blue-600",children:b.hasPowerNumber?"Yes":"No"}),(0,s.jsx)("div",{className:"text-gray-600",children:"Power Number"})]}),(0,s.jsxs)("div",{className:"text-center p-2 bg-white rounded border",children:[(0,s.jsx)("div",{className:"font-bold text-blue-600",children:x.getOdds(t).jackpot.split(" ")[2]}),(0,s.jsx)("div",{className:"text-gray-600",children:"Jackpot Odds"})]})]})]}),i&&(0,s.jsxs)(n.P.div,{className:"mt-4 p-4 bg-gray-50 rounded-xl border border-gray-200",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:[(0,s.jsxs)("h5",{className:"font-semibold text-gray-800 mb-3",children:[b.name," Rules & Information"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h6",{className:"font-medium text-gray-700 mb-2",children:"Game Rules:"}),(0,s.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:x.getRulesForCurrentLottery().map((e,t)=>(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"}),e]},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h6",{className:"font-medium text-gray-700 mb-2",children:"Winning Odds:"}),(0,s.jsx)("div",{className:"text-sm text-gray-600 space-y-1",children:Object.entries(x.getOdds(t)).map(e=>{let[t,r]=e;return(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{className:"capitalize",children:[t,":"]}),(0,s.jsx)("span",{className:"font-medium",children:r})]},t)})})]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("h6",{className:"font-medium text-gray-700 mb-2",children:"Prize Structure:"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-xs",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"bg-gray-100",children:[(0,s.jsx)("th",{className:"p-2 text-left",children:"Level"}),(0,s.jsx)("th",{className:"p-2 text-left",children:"Condition"}),(0,s.jsx)("th",{className:"p-2 text-left",children:"Prize"})]})}),(0,s.jsx)("tbody",{children:x.getPrizeStructure(t).map((e,t)=>(0,s.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,s.jsx)("td",{className:"p-2 font-medium",children:e.level}),(0,s.jsx)("td",{className:"p-2",children:e.condition}),(0,s.jsx)("td",{className:"p-2 text-green-600 font-medium",children:e.prize})]},t))})]})})]})]})]})})}var g=r(8186),b=r(9074);function p(e){let{number:t,size:r="md",variant:a="primary",delay:i=0,animate:l=!0}=e,c="\n    rounded-full flex items-center justify-center text-white font-bold\n    border-2 shadow-lg backdrop-blur-sm\n    ".concat({xs:"w-6 h-6 text-xs",sm:"w-8 h-8 text-sm",md:"w-10 h-10 text-sm",lg:"w-12 h-12 text-base",xl:"w-16 h-16 text-lg"}[r],"\n    ").concat({primary:"bg-gradient-to-br from-blue-500 to-blue-600 border-blue-400 shadow-blue-500/25",secondary:"bg-gradient-to-br from-gray-500 to-gray-600 border-gray-400 shadow-gray-500/25",power:"bg-gradient-to-br from-red-500 to-red-600 border-red-400 shadow-red-500/25",hot:"bg-gradient-to-br from-orange-500 to-red-500 border-orange-400 shadow-orange-500/25",cold:"bg-gradient-to-br from-cyan-500 to-blue-500 border-cyan-400 shadow-cyan-500/25",suggested:"bg-gradient-to-br from-green-500 to-emerald-600 border-green-400 shadow-green-500/25"}[a],"\n  ");return(0,s.jsx)(n.P.div,{className:c,...l?{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0,transition:{type:"spring",stiffness:260,damping:20,delay:i}},whileHover:{scale:1.1,transition:{duration:.2}}}:{},children:t})}function y(e){let{data:t}=e,r=t.slice(0,5),a=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),i=e=>{let t=new Date(e),r=Math.ceil(Math.abs(new Date().getTime()-t.getTime())/864e5);return 1===r?"Yesterday":r<7?"".concat(r," days ago"):r<30?"".concat(Math.ceil(r/7)," weeks ago"):a(e)};return r.length?(0,s.jsxs)(u,{className:"p-6",gradient:!0,children:[(0,s.jsxs)(n.P.h2,{className:"text-xl font-bold text-gray-800 mb-6 flex items-center",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},children:[(0,s.jsx)(g.A,{className:"mr-2 text-blue-600",size:24}),"Latest Results"]}),(0,s.jsx)("div",{className:"space-y-4",children:r.map((e,t)=>(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*(t+1)},className:"\n              p-4 rounded-xl border-2 transition-all duration-300\n              ".concat(0===t?"border-blue-300 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg":"border-gray-200 bg-white/50 hover:bg-white/80","\n            "),children:[(0,s.jsx)("div",{className:"flex justify-between items-start mb-4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"font-semibold text-gray-800 flex items-center",children:["Draw #",e.id,0===t&&(0,s.jsx)(n.P.span,{className:"ml-2 px-2 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full shadow-lg",initial:{scale:0},animate:{scale:1},transition:{delay:.5,type:"spring"},children:"Latest"})]}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mt-1",children:[(0,s.jsx)(b.A,{size:14,className:"mr-1"}),(0,s.jsx)("span",{children:a(e.date)}),(0,s.jsx)("span",{className:"mx-2",children:"•"}),(0,s.jsx)("span",{className:"text-blue-600 font-medium",children:i(e.date)})]})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,s.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),"Numbers:"]}),(0,s.jsx)("div",{className:"flex space-x-2",children:e.result.map((e,t)=>(0,s.jsx)(p,{number:e,variant:"primary",delay:.1*(t+1),size:"sm"},t))})]}),e.powerNumber&&(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,s.jsx)(c.A,{size:14,className:"mr-2 text-red-500"}),"Power:"]}),(0,s.jsx)(p,{number:e.powerNumber,variant:"power",delay:.7,size:"sm"})]})]},e.id))}),(0,s.jsx)(n.P.div,{className:"mt-6 pt-4 border-t border-gray-200",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:(0,s.jsxs)("p",{className:"text-xs text-gray-500 text-center",children:["Showing ",r.length," most recent draws"]})})]}):(0,s.jsxs)(u,{className:"p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-gray-800 mb-4 flex items-center",children:[(0,s.jsx)(g.A,{className:"mr-2 text-blue-600",size:24}),"Latest Results"]}),(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-gray-400 text-4xl mb-2",children:"\uD83C\uDFB1"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No lottery results available"})]})]})}var f=r(2713),N=r(3109),j=r(2502),v=r(4065);class w{static getInstance(){return w.instance||(w.instance=new w),w.instance}async fetchLotteryData(){let e="lottery-data",t=this.cache.get(e);if(t&&Date.now()-t.timestamp<this.CACHE_TTL)return t.data;try{let t=await fetch("/api/lottery-data");if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let r=await t.json();return this.cache.set(e,{data:r,timestamp:Date.now()}),r}catch(e){throw console.error("Failed to fetch lottery data:",e),e}}calculateNumberFrequency(e){let t={},r=6*e.length;e.forEach(e=>{e.result.forEach(e=>{t[e]=(t[e]||0)+1})});let s=[];for(let e=1;e<=55;e++){let a=t[e]||0;s.push({number:e,count:a,percentage:r>0?a/r*100:0})}return s.sort((e,t)=>t.count-e.count)}calculateStatistics(e){let t=this.calculateNumberFrequency(e),r=new Date,s=t=>e.filter(e=>{let s=new Date(e.date);return Math.ceil((r.getTime()-s.getTime())/864e5)<=t});return{totalDraws:e.length,mostFrequent:t.slice(0,10),leastFrequent:t.slice(-10).reverse(),numberDistribution:t,recentTrends:{last30Days:this.calculateNumberFrequency(s(30)),last60Days:this.calculateNumberFrequency(s(60)),last90Days:this.calculateNumberFrequency(s(90))}}}getHotNumbers(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:55,s=this.calculateNumberFrequency(e).slice(0,t).map(e=>e.number);return this.ensureUniqueNumbers(s,r)}getColdNumbers(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:55,s=this.calculateNumberFrequency(e).slice(-t).map(e=>e.number).reverse();return this.ensureUniqueNumbers(s,r)}getBalancedNumbers(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:55,r=[...this.getHotNumbers(e,3,t),...this.getColdNumbers(e,3,t)];return this.ensureUniqueNumbers(r,t)}getRecentTrendNumbers(e){return this.calculateStatistics(e).recentTrends.last30Days.slice(0,6).map(e=>e.number)}getRandomNumbers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:55,t=[],r=new Set;for(;t.length<6;){let s=Math.floor(Math.random()*e)+1;r.has(s)||(t.push(s),r.add(s))}return t.sort((e,t)=>e-t)}ensureUniqueNumbers(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:55,r=[...new Set(e)];for(;r.length<6;){let e=Math.floor(Math.random()*t)+1;r.includes(e)||r.push(e)}return r.slice(0,6).sort((e,t)=>e-t)}getMathematicalPatternNumbers(e){let t=this.calculateNumberFrequency(e),r=t.reduce((e,t)=>e+t.count,0)/t.length,s=t.filter(e=>Math.abs(e.count-r)<=.2*r);return s.length>=6?s.slice(0,6).map(e=>e.number):t.slice(0,6).map(e=>e.number)}calculateConfidence(e,t){let r=this.calculateStatistics(t),s=r.numberDistribution.reduce((e,t)=>e+t.count,0)/r.numberDistribution.length,a=e.map(e=>{var t;return(null==(t=r.numberDistribution.find(t=>t.number===e))?void 0:t.count)||0});return Math.min(100,Math.max(0,a.reduce((e,t)=>e+t,0)/a.length/s*50))}getSmartFrequencyNumbers(e){let t=e.slice(0,50),r=e.slice(0,200),s=this.calculateNumberFrequency(t),a=this.calculateNumberFrequency(r);return s.map(e=>{let t=a.find(t=>t.number===e.number);return{number:e.number,score:.7*e.percentage+.3*((null==t?void 0:t.percentage)||0)}}).sort((e,t)=>t.score-e.score).slice(0,6).map(e=>e.number).sort((e,t)=>e-t)}getGapAnalysisNumbers(e){let t={};for(let r=1;r<=55;r++){let s=[],a=-1;e.forEach((e,t)=>{e.result.includes(r)&&(-1!==a&&s.push(t-a),a=t)});let n=s.length>0?s.reduce((e,t)=>e+t,0)/s.length:0,i=-1===a?e.length:a;t[r]={gaps:s,avgGap:n,currentGap:i}}let r=Object.entries(t).map(e=>{let[t,r]=e;return{number:parseInt(t),priority:r.currentGap>=r.avgGap?r.currentGap/r.avgGap:0}}).filter(e=>e.priority>1.2).sort((e,t)=>t.priority-e.priority).slice(0,6).map(e=>e.number);if(r.length<6){let t=this.getHotNumbers(e,6-r.length);r.push(...t.filter(e=>!r.includes(e)))}return r.slice(0,6).sort((e,t)=>e-t)}getPatternBasedNumbers(e){let t=e.slice(0,30),r={evenOdd:this.analyzeEvenOddPattern(t),sumRange:this.analyzeSumPattern(t),consecutive:this.analyzeConsecutivePattern(t),endDigits:this.analyzeEndDigitPattern(t)},s=[],a=new Set,n=r.evenOdd.optimalEven,i=6-n,l=0,c=0,o=this.calculateNumberFrequency(e.slice(0,100)).sort((e,t)=>t.count-e.count);for(let e of o){if(s.length>=6)break;let t=e.number%2==0;(t&&l<n||!t&&c<i)&&!a.has(e.number)&&(s.push(e.number),a.add(e.number),t?l++:c++)}for(;s.length<6;)for(let e of o){if(s.length>=6)break;a.has(e.number)||(s.push(e.number),a.add(e.number))}return s.slice(0,6).sort((e,t)=>e-t)}getEnsembleNumbers(e){let t=[{name:"hot",numbers:this.getHotNumbers(e),weight:.25},{name:"smart",numbers:this.getSmartFrequencyNumbers(e),weight:.25},{name:"gap",numbers:this.getGapAnalysisNumbers(e),weight:.25},{name:"pattern",numbers:this.getPatternBasedNumbers(e),weight:.25}],r={};for(let e=1;e<=55;e++)r[e]=0;return t.forEach(e=>{e.numbers.forEach((t,s)=>{r[t]+=e.weight*(6-s)})}),Object.entries(r).sort((e,t)=>{let[,r]=e,[,s]=t;return s-r}).slice(0,6).map(e=>{let[t]=e;return parseInt(t)}).sort((e,t)=>e-t)}analyzeEvenOddPattern(e){let t=e.map(e=>{let t=e.result.filter(e=>e%2==0).length;return{even:t,odd:6-t}});return{optimalEven:Math.round(t.reduce((e,t)=>e+t.even,0)/t.length)}}analyzeSumPattern(e){let t=e.map(e=>e.result.reduce((e,t)=>e+t,0));return{optimalSum:Math.round(t.reduce((e,t)=>e+t,0)/t.length),range:{min:Math.min(...t),max:Math.max(...t)}}}analyzeConsecutivePattern(e){let t=e.map(e=>{let t=[...e.result].sort((e,t)=>e-t),r=0;for(let e=0;e<t.length-1;e++)t[e+1]===t[e]+1&&r++;return r}),r=t.reduce((e,t)=>e+t,0)/t.length;return{hasConsecutive:r>.5,avgConsecutive:r}}analyzeEndDigitPattern(e){let t={};return e.forEach(e=>{e.result.forEach(e=>{let r=e%10;t[r]=(t[r]||0)+1})}),t}clearCache(){this.cache.clear()}constructor(){this.cache=new Map,this.CACHE_TTL=3e5}}j.t1.register(j.PP,j.kc,j.E8,j.No,j.FN,j.hE,j.m_,j.s$);let P=(0,a.memo)(function(e){var t,r;let{data:i}=e,[l,c]=(0,a.useState)("frequency"),o=w.getInstance(),d=(0,a.useMemo)(()=>0===i.length?null:o.calculateStatistics(i),[i,o]),m=(0,a.useMemo)(()=>d?{labels:d.numberDistribution.map(e=>e.number.toString()),datasets:[{label:"Frequency",data:d.numberDistribution.map(e=>e.count),backgroundColor:d.numberDistribution.map((e,t)=>t<10?"rgba(239, 68, 68, 0.8)":t>=d.numberDistribution.length-10?"rgba(59, 130, 246, 0.8)":"rgba(156, 163, 175, 0.8)"),borderColor:d.numberDistribution.map((e,t)=>t<10?"rgba(239, 68, 68, 1)":t>=d.numberDistribution.length-10?"rgba(59, 130, 246, 1)":"rgba(156, 163, 175, 1)"),borderWidth:1}]}:null,[d]),x=(0,a.useMemo)(()=>d?{labels:d.numberDistribution.slice(0,20).map(e=>e.number.toString()),datasets:[{label:"All Time",data:d.numberDistribution.slice(0,20).map(e=>e.count),borderColor:"rgba(75, 192, 192, 1)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1},{label:"Last 30 Days",data:d.recentTrends.last30Days.slice(0,20).map(e=>e.count),borderColor:"rgba(255, 99, 132, 1)",backgroundColor:"rgba(255, 99, 132, 0.2)",tension:.1},{label:"Last 90 Days",data:d.recentTrends.last90Days.slice(0,20).map(e=>e.count),borderColor:"rgba(54, 162, 235, 1)",backgroundColor:"rgba(54, 162, 235, 0.2)",tension:.1}]}:null,[d]);if(!d||!i.length)return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"Statistics & Analysis"}),(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-gray-400 text-4xl mb-2",children:"\uD83D\uDCCA"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No data available for analysis"})]})]});let g={responsive:!0,plugins:{legend:{position:"top"},title:{display:!0,text:"frequency"===l?"Number Frequency Distribution":"Frequency Trends Comparison"},tooltip:{callbacks:{label:function(e){var t;let r=null==(t=d.numberDistribution.find(t=>t.number.toString()===e.label))?void 0:t.percentage.toFixed(2);return"".concat(e.dataset.label,": ").concat(e.parsed.y," (").concat(r,"%)")}}}},scales:{x:{title:{display:!0,text:"Numbers"}},y:{title:{display:!0,text:"Frequency"},beginAtZero:!0}}};return(0,s.jsxs)(u,{className:"p-6",gradient:!0,children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsxs)(n.P.h2,{className:"text-xl font-bold text-gray-800 flex items-center",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},children:[(0,s.jsx)(f.A,{className:"mr-2 text-blue-600",size:24}),"Statistics & Analysis"]}),(0,s.jsxs)(n.P.div,{className:"flex space-x-2",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,s.jsx)(h,{onClick:()=>c("frequency"),variant:"frequency"===l?"primary":"ghost",size:"sm",icon:(0,s.jsx)(f.A,{size:16}),children:"Frequency"}),(0,s.jsx)(h,{onClick:()=>c("trends"),variant:"trends"===l?"primary":"ghost",size:"sm",icon:(0,s.jsx)(N.A,{size:16}),children:"Trends"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:d.totalDraws}),(0,s.jsx)("div",{className:"text-sm text-blue-800",children:"Total Draws"})]}),(0,s.jsxs)("div",{className:"bg-red-50 rounded-lg p-3 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:null==(t=d.mostFrequent[0])?void 0:t.number}),(0,s.jsx)("div",{className:"text-sm text-red-800",children:"Hottest Number"})]}),(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:null==(r=d.leastFrequent[0])?void 0:r.number}),(0,s.jsx)("div",{className:"text-sm text-blue-800",children:"Coldest Number"})]}),(0,s.jsxs)("div",{className:"bg-green-50 rounded-lg p-3 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:Math.round(d.numberDistribution.reduce((e,t)=>e+t.count,0)/55)}),(0,s.jsx)("div",{className:"text-sm text-green-800",children:"Avg Frequency"})]})]}),(0,s.jsx)("div",{className:"h-96",children:"frequency"===l?m&&(0,s.jsx)(v.yP,{data:m,options:g}):x&&(0,s.jsx)(v.N1,{data:x,options:g})}),(0,s.jsxs)("div",{className:"mt-4 flex justify-center space-x-6 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-red-500 rounded mr-2"}),(0,s.jsx)("span",{children:"Hot Numbers (Top 10)"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded mr-2"}),(0,s.jsx)("span",{children:"Cold Numbers (Bottom 10)"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-gray-400 rounded mr-2"}),(0,s.jsx)("span",{children:"Normal Numbers"})]})]})]})});var C=r(760),S=r(463),D=r(4229),z=r(4357),M=r(5196),k=r(6785);class T{static getInstance(){return T.instance||(T.instance=new T),T.instance}getNeuralPatternNumbers(e){let t=e.slice(0,50),r=this.analyzePatterns(t),s={frequency:.3,recency:.25,gaps:.2,evenOdd:.15},a={};for(let e=1;e<=55;e++)a[e]=0;this.calculateFrequency(t).forEach(e=>{a[e.number]+=e.percentage*s.frequency}),t.slice(0,10).forEach((e,t)=>{e.result.forEach(e=>{a[e]+=(10-t)*s.recency})}),this.analyzeGaps(e).forEach(e=>{e.expectedNext&&(a[e.number]+=e.probability*s.gaps)});let n=this.getOptimalEvenOddBalance(r);for(let e=1;e<=55;e++){let t=e%2==0;(t&&"even"===n.needMore||!t&&"odd"===n.needMore)&&(a[e]+=s.evenOdd)}let i=this.getOptimalSum(t);return this.generateCandidatesForSum(a,i).slice(0,6)}getFibonacciPatternNumbers(e){let t=this.generateFibonacci(55),r=this.calculateFrequency(e.slice(0,100)),s=t.map(e=>{var t;return{number:e,score:(null==(t=r.find(t=>t.number===e))?void 0:t.percentage)||0}}).sort((e,t)=>t.score-e.score).slice(0,3),a=this.getComplementaryNumbers(s.map(e=>e.number),e,3);return[...s.map(e=>e.number),...a].sort((e,t)=>e-t)}getMLWeightedNumbers(e){let t=this.extractFeatures(e),r=this.calculateMLWeights(t),s=[];for(let e=1;e<=55;e++){let a;a=0+r.frequency*(t.frequency[e]||0)+r.recency*(t.recency[e]||0)+r.pattern*(t.pattern[e]||0)+r.position*(t.position[e]||0),s.push({number:e,weight:a})}return s.sort((e,t)=>t.weight-e.weight).slice(0,6).map(e=>e.number).sort((e,t)=>e-t)}getChaosTheoryNumbers(e){let t=e.slice(0,30),r=this.calculateChaosFactors(t).map(e=>Math.sin(e*Math.PI)),s=[],a=new Set;for(let e=0;e<6;e++){let t=Math.floor(55*Math.abs(r[e%r.length]))+1;for(;a.has(t);)t=t%55+1;s.push(t),a.add(t)}return s.sort((e,t)=>e-t)}storePrediction(e,t,r){let s={id:this.generateId(),date:new Date().toISOString().split("T")[0],predictedNumbers:e,algorithm:t,confidence:r,timestamp:Date.now()};return this.predictions.push(s),this.savePredictions(),s.id}comparePrediction(e,t){let r=this.predictions.find(t=>t.id===e);if(!r)return;let s=r.predictedNumbers.filter(e=>t.result.includes(e)).length;r.actualNumbers=t.result,r.powerNumber=t.powerNumber,r.matches=s,r.accuracy=s/6*100,this.savePredictions()}getAlgorithmPerformance(){return[...new Set(this.predictions.map(e=>e.algorithm))].map(e=>{let t=this.predictions.filter(t=>t.algorithm===e&&void 0!==t.matches);if(0===t.length)return{algorithmName:e,totalPredictions:0,averageMatches:0,bestMatch:0,accuracy:0,confidenceScore:0,lastUpdated:new Date().toISOString()};let r=t.reduce((e,t)=>e+(t.matches||0),0)/t.length,s=Math.max(...t.map(e=>e.matches||0)),a=t.reduce((e,t)=>e+(t.accuracy||0),0)/t.length,n=t.reduce((e,t)=>e+t.confidence,0)/t.length;return{algorithmName:e,totalPredictions:t.length,averageMatches:Math.round(100*r)/100,bestMatch:s,accuracy:Math.round(100*a)/100,confidenceScore:Math.round(100*n)/100,lastUpdated:new Date().toISOString()}})}getPredictions(){return[...this.predictions].sort((e,t)=>t.timestamp-e.timestamp)}analyzePatterns(e){let t=e.flatMap(e=>e.result),r=this.findConsecutivePatterns(t),s=this.analyzeEvenOdd(t),a=e.map(e=>e.result.reduce((e,t)=>e+t,0));return{consecutive:r,evenOdd:s,sumRange:{min:Math.min(...a),max:Math.max(...a),average:a.reduce((e,t)=>e+t,0)/a.length},gaps:this.analyzeNumberGaps(e),repeats:this.findRepeatingPatterns(e)}}calculateFrequency(e){let t={},r=6*e.length;return e.forEach(e=>{e.result.forEach(e=>{t[e]=(t[e]||0)+1})}),Object.entries(t).map(e=>{let[t,s]=e;return{number:parseInt(t),percentage:s/r*100}})}analyzeGaps(e){let t={};for(let r=1;r<=55;r++){t[r]=[];let s=-1;e.forEach((e,a)=>{e.result.includes(r)&&(-1!==s&&t[r].push(a-s),s=a)})}return Object.entries(t).map(t=>{let[r,s]=t,a=s.length>0?s.reduce((e,t)=>e+t,0)/s.length:0,n=this.getCurrentGap(parseInt(r),e),i=n>=a?Math.min(n/a,2):0;return{number:parseInt(r),gap:n,probability:i,expectedNext:i>1.2}})}getCurrentGap(e,t){for(let r=0;r<t.length;r++)if(t[r].result.includes(e))return r;return t.length}generateFibonacci(e){let t=[1,1];for(;t[t.length-1]<e;)t.push(t[t.length-1]+t[t.length-2]);return t.filter(t=>t<=e)}getComplementaryNumbers(e,t,r){return this.calculateFrequency(t).filter(t=>!e.includes(t.number)).sort((e,t)=>t.percentage-e.percentage).slice(0,r).map(e=>e.number)}extractFeatures(e){return{frequency:this.calculateFrequency(e.slice(0,100)),recency:this.calculateRecency(e.slice(0,20)),pattern:this.calculatePatternScores(e.slice(0,50)),position:this.calculatePositionScores(e.slice(0,30))}}calculateMLWeights(e){return{frequency:.4,recency:.3,pattern:.2,position:.1}}calculateChaosFactors(e){return e.map((e,t)=>e.result.reduce((e,t)=>e+t,0)*this.calculateVariance(e.result)/(t+1))}calculateVariance(e){let t=e.reduce((e,t)=>e+t,0)/e.length;return e.reduce((e,r)=>e+Math.pow(r-t,2),0)/e.length}generateId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}loadPredictions(){{let e=localStorage.getItem(this.STORAGE_KEY);e&&(this.predictions=JSON.parse(e))}}savePredictions(){localStorage.setItem(this.STORAGE_KEY,JSON.stringify(this.predictions))}findConsecutivePatterns(e){return[]}analyzeEvenOdd(e){let t=e.filter(e=>e%2==0).length;return{even:t,odd:e.length-t}}analyzeNumberGaps(e){return[]}findRepeatingPatterns(e){return[]}getOptimalEvenOddBalance(e){return{needMore:e.evenOdd.even>e.evenOdd.odd?"odd":"even"}}getOptimalSum(e){let t=e.map(e=>e.result.reduce((e,t)=>e+t,0));return t.reduce((e,t)=>e+t,0)/t.length}generateCandidatesForSum(e,t){return Object.entries(e).sort((e,t)=>{let[,r]=e,[,s]=t;return s-r}).slice(0,12).map(e=>{let[t]=e;return parseInt(t)})}calculateRecency(e){return{}}calculatePatternScores(e){return{}}calculatePositionScores(e){return{}}constructor(){this.predictions=[],this.STORAGE_KEY="vietlott-predictions",this.loadPredictions()}}let A=[{name:"Smart Frequency",description:"Recent + historical frequency analysis",icon:"\uD83E\uDDE0",color:"from-purple-500 to-indigo-500"},{name:"Gap Analysis",description:"Numbers due based on gap patterns",icon:"\uD83D\uDCCA",color:"from-blue-500 to-cyan-500"},{name:"Pattern Recognition",description:"Even/odd, sum, and sequence patterns",icon:"\uD83D\uDD0D",color:"from-green-500 to-emerald-500"},{name:"Ensemble Method",description:"Weighted combination of all algorithms",icon:"\uD83C\uDFAF",color:"from-orange-500 to-red-500"},{name:"Neural Pattern",description:"AI-inspired pattern recognition",icon:"\uD83E\uDD16",color:"from-pink-500 to-purple-500"},{name:"Fibonacci Sequence",description:"Mathematical Fibonacci patterns",icon:"\uD83C\uDF00",color:"from-teal-500 to-blue-500"},{name:"ML Weighted",description:"Machine learning weighted selection",icon:"⚡",color:"from-yellow-500 to-orange-500"},{name:"Chaos Theory",description:"Chaos theory-based prediction",icon:"\uD83C\uDF2A️",color:"from-gray-500 to-slate-600"}];function F(e){let{data:t}=e,[r,c]=(0,a.useState)(A[0].name),[o,d]=(0,a.useState)(null),[m,x]=(0,a.useState)(!1),[g,b]=(0,a.useState)(!1),[y,f]=(0,a.useState)(!1),[j,v]=(0,a.useState)([]),[P,F]=(0,a.useState)([]),[q,E]=(0,a.useState)(""),[L,O]=(0,a.useState)(null),I=w.getInstance(),R=T.getInstance();(0,a.useEffect)(()=>{t.length>0&&G(r)},[t,r]);let G=async e=>{if(0===t.length)return;x(!0),await new Promise(e=>setTimeout(e,800));let r=[];switch(e){case"Smart Frequency":r=I.getSmartFrequencyNumbers(t);break;case"Gap Analysis":r=I.getGapAnalysisNumbers(t);break;case"Pattern Recognition":r=I.getPatternBasedNumbers(t);break;case"Ensemble Method":default:r=I.getEnsembleNumbers(t);break;case"Neural Pattern":r=R.getNeuralPatternNumbers(t);break;case"Fibonacci Sequence":r=R.getFibonacciPatternNumbers(t);break;case"ML Weighted":r=R.getMLWeightedNumbers(t);break;case"Chaos Theory":r=R.getChaosTheoryNumbers(t)}let s=Math.round(I.calculateConfidence(r,t)),a=A.find(t=>t.name===e);d({numbers:r,confidence:s,reasoning:(null==a?void 0:a.description)||""}),x(!1)},H=e=>{c(e)},B=async()=>{o&&(await navigator.clipboard.writeText(o.numbers.join(", ")),b(!0),setTimeout(()=>b(!1),2e3))},V=()=>{v(R.getPredictions())},K=()=>{F(R.getAlgorithmPerformance())};return((0,a.useEffect)(()=>{V(),K()},[]),t.length)?(0,s.jsxs)(u,{className:"p-6",gradient:!0,children:[(0,s.jsxs)(n.P.h2,{className:"text-xl font-bold text-gray-800 mb-6 flex items-center",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},children:[(0,s.jsx)(l.A,{className:"mr-2 text-green-600",size:24}),"Number Suggestions"]}),(0,s.jsxs)(n.P.div,{className:"mb-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3 flex items-center",children:[(0,s.jsx)(S.A,{className:"mr-2",size:16}),"Choose Algorithm:"]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:A.map((e,t)=>(0,s.jsxs)(n.P.button,{onClick:()=>H(e.name),className:"\n                p-3 rounded-lg border-2 transition-all duration-200 text-left\n                ".concat(r===e.name?"border-blue-500 bg-gradient-to-r ".concat(e.color," text-white shadow-lg"):"border-gray-200 bg-white hover:border-gray-300 hover:shadow-md","\n              "),initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.1*t},whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsxs)("div",{className:"flex items-center mb-1",children:[(0,s.jsx)("span",{className:"text-lg mr-2",children:e.icon}),(0,s.jsx)("span",{className:"font-medium",children:e.name})]}),(0,s.jsx)("p",{className:"text-xs ".concat(r===e.name?"text-white/80":"text-gray-500"),children:e.description})]},e.name))})]}),(0,s.jsx)(C.N,{mode:"wait",children:m?(0,s.jsxs)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-center py-8",children:[(0,s.jsx)(n.P.div,{className:"w-16 h-16 border-4 border-green-200 border-t-green-600 rounded-full mx-auto mb-4",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),(0,s.jsx)("p",{className:"text-gray-600 font-medium",children:"Generating suggestions..."})]},"generating"):o?(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{delay:.3},children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsxs)("h3",{className:"font-semibold text-gray-800 flex items-center",children:[(0,s.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),"Your Lucky Numbers:"]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(h,{onClick:()=>{o&&(O(R.storePrediction(o.numbers,r,o.confidence)),V())},variant:"success",size:"sm",icon:(0,s.jsx)(D.A,{size:16}),disabled:!o||null!==L,children:L?"Saved":"Save"}),(0,s.jsx)(h,{onClick:()=>{G(r)},variant:"secondary",size:"sm",icon:(0,s.jsx)(i.A,{size:16}),loading:m,children:"Refresh"})]})]}),(0,s.jsx)("div",{className:"flex justify-center space-x-3 mb-6",children:o.numbers.map((e,t)=>(0,s.jsx)(p,{number:e,variant:"suggested",size:"lg",delay:.1*t},t))})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white/60 rounded-xl p-4 border border-white/20",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,s.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),"Confidence Level:"]}),(0,s.jsxs)("span",{className:"text-lg font-bold text-gray-800",children:[o.confidence,"%"]})]}),(0,s.jsx)(e=>{let{confidence:t}=e;return(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 overflow-hidden",children:(0,s.jsx)(n.P.div,{className:"h-3 rounded-full ".concat(t>=70?"bg-gradient-to-r from-green-400 to-green-600":t>=40?"bg-gradient-to-r from-yellow-400 to-yellow-600":"bg-gradient-to-r from-red-400 to-red-600"),initial:{width:0},animate:{width:"".concat(t,"%")},transition:{duration:1,delay:.5}})})},{confidence:o.confidence}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Based on ",t.length," historical draws analysis"]})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200",children:[(0,s.jsxs)("h4",{className:"font-medium text-gray-800 mb-2 flex items-center",children:[(0,s.jsx)("span",{className:"text-blue-600 mr-2",children:"ℹ️"}),"Algorithm Insights:"]}),(0,s.jsx)("p",{className:"text-sm text-gray-700 mb-3",children:o.reasoning}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"w-1 h-1 bg-blue-500 rounded-full mr-2"}),t.length," draws analyzed"]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"w-1 h-1 bg-blue-500 rounded-full mr-2"}),"Numbers sorted ascending"]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"w-1 h-1 bg-blue-500 rounded-full mr-2"}),"Frequency-based confidence"]})]})]})]}),(0,s.jsx)(n.P.div,{className:"mt-6 pt-4 border-t border-gray-200",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600 flex items-center",children:[(0,s.jsx)(z.A,{size:14,className:"mr-2"}),"Quick copy:"]}),(0,s.jsx)(h,{onClick:B,variant:"ghost",size:"sm",icon:g?(0,s.jsx)(M.A,{size:16,className:"text-green-600"}):(0,s.jsx)(z.A,{size:16}),className:g?"text-green-600 border-green-300":"",children:g?"Copied!":o.numbers.join(", ")})]})}),L&&(0,s.jsxs)(n.P.div,{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-xl",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{delay:.5},children:[(0,s.jsxs)("h4",{className:"font-medium text-yellow-800 mb-3 flex items-center",children:[(0,s.jsx)(k.A,{className:"mr-2",size:16}),"Compare with Actual Result"]}),(0,s.jsx)("p",{className:"text-sm text-yellow-700 mb-3",children:"Enter the actual lottery result to track algorithm performance:"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("input",{type:"text",value:q,onChange:e=>E(e.target.value),placeholder:"e.g., 09 37 42 45 46 50 14",className:"flex-1 px-3 py-2 border border-yellow-300 rounded-lg text-sm focus:ring-2 focus:ring-yellow-500 focus:border-transparent"}),(0,s.jsx)(h,{onClick:()=>{if(q&&L)try{let e=q.trim().split(/\s+/),t=e.slice(0,6).map(e=>parseInt(e)),r=e.length>6?parseInt(e[6]):void 0,s={id:"manual",date:new Date().toISOString().split("T")[0],result:t,powerNumber:r};R.comparePrediction(L,s),V(),K(),E(""),O(null)}catch(e){console.error("Error parsing actual result:",e)}},variant:"primary",size:"sm",disabled:!q.trim(),children:"Compare"})]})]}),(0,s.jsx)(n.P.div,{className:"mt-6 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.7},children:(0,s.jsxs)(h,{onClick:()=>f(!y),variant:"ghost",size:"sm",icon:(0,s.jsx)(N.A,{size:16}),children:[y?"Hide":"Show"," Algorithm Performance"]})}),(0,s.jsx)(C.N,{children:y&&(0,s.jsxs)(n.P.div,{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:[(0,s.jsxs)("h4",{className:"font-medium text-blue-800 mb-4 flex items-center",children:[(0,s.jsx)(N.A,{className:"mr-2",size:16}),"Algorithm Performance Comparison"]}),P.length>0?(0,s.jsx)("div",{className:"space-y-3",children:P.sort((e,t)=>t.averageMatches-e.averageMatches).map((e,t)=>(0,s.jsxs)("div",{className:"p-3 rounded-lg border ".concat(e.algorithmName===r?"border-blue-400 bg-blue-100":"border-blue-200 bg-white"),children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsxs)("span",{className:"font-medium text-blue-900",children:["#",t+1," ",e.algorithmName]}),(0,s.jsxs)("span",{className:"text-sm text-blue-600",children:[e.totalPredictions," predictions"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-xs",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-blue-600",children:"Avg Matches:"}),(0,s.jsx)("span",{className:"font-bold ml-1",children:e.averageMatches})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-blue-600",children:"Best:"}),(0,s.jsxs)("span",{className:"font-bold ml-1",children:[e.bestMatch,"/6"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-blue-600",children:"Accuracy:"}),(0,s.jsxs)("span",{className:"font-bold ml-1",children:[e.accuracy,"%"]})]})]})]},e.algorithmName))}):(0,s.jsx)("p",{className:"text-blue-600 text-sm text-center py-4",children:"No performance data yet. Save predictions and compare with actual results to see algorithm performance."})]})}),j.length>0&&(0,s.jsxs)(n.P.div,{className:"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-xl",initial:{opacity:0},animate:{opacity:1},transition:{delay:.9},children:[(0,s.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"Recent Predictions"}),(0,s.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:j.slice(0,5).map(e=>(0,s.jsxs)("div",{className:"flex justify-between items-center p-2 bg-white rounded border text-xs",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:e.algorithm}),(0,s.jsx)("span",{className:"text-gray-500 ml-2",children:e.date})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-gray-600",children:e.predictedNumbers.join(", ")}),void 0!==e.matches&&(0,s.jsxs)("span",{className:"px-2 py-1 rounded text-xs font-bold ".concat(e.matches>=3?"bg-green-100 text-green-800":e.matches>=1?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:[e.matches,"/6"]})]})]},e.id))})]})]},"suggestion"):null})]}):(0,s.jsxs)(u,{className:"p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-gray-800 mb-4 flex items-center",children:[(0,s.jsx)(l.A,{className:"mr-2 text-green-600",size:24}),"Number Suggestions"]}),(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-gray-400 text-4xl mb-2",children:"\uD83C\uDFB2"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No data available for suggestions"})]})]})}function q(e){let{data:t}=e,[r,n]=(0,a.useState)(1),[i,l]=(0,a.useState)(""),[c,o]=(0,a.useState)("date"),[d,m]=(0,a.useState)("desc"),u=[...t.filter(e=>e.id.toLowerCase().includes(i.toLowerCase())||e.date.includes(i)||e.result.some(e=>e.toString().includes(i)))].sort((e,t)=>{let r=0;return r="date"===c?new Date(e.date).getTime()-new Date(t.date).getTime():e.id.localeCompare(t.id),"asc"===d?r:-r}),h=Math.ceil(u.length/10),x=(r-1)*10,g=u.slice(x,x+10),b=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),p=e=>{let{number:t,size:r="sm"}=e;return(0,s.jsx)("div",{className:"\n        rounded-full bg-blue-500 text-white font-bold flex items-center justify-center\n        ".concat("sm"===r?"w-8 h-8 text-sm":"w-6 h-6 text-xs","\n      "),children:t})},y=e=>{c===e?m("asc"===d?"desc":"asc"):(o(e),m("desc")),n(1)};return t.length?(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-gray-800 mb-4 flex items-center",children:[(0,s.jsx)("span",{className:"mr-2",children:"\uD83D\uDCCB"}),"Historical Data"]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("input",{type:"text",placeholder:"Search by draw ID, date, or numbers...",value:i,onChange:e=>{l(e.target.value),n(1)},className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("button",{onClick:()=>y("date"),className:"px-3 py-2 rounded text-sm transition-colors flex items-center ".concat("date"===c?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["Date ","date"===c&&("asc"===d?"↑":"↓")]}),(0,s.jsxs)("button",{onClick:()=>y("id"),className:"px-3 py-2 rounded text-sm transition-colors flex items-center ".concat("id"===c?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["Draw ID ","id"===c&&("asc"===d?"↑":"↓")]})]})]}),(0,s.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:["Showing ",x+1,"-",Math.min(x+10,u.length)," of ",u.length," results",i&&" (filtered from ".concat(t.length," total)")]}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,s.jsx)("th",{className:"text-left py-3 px-2 font-semibold text-gray-700",children:"Draw ID"}),(0,s.jsx)("th",{className:"text-left py-3 px-2 font-semibold text-gray-700",children:"Date"}),(0,s.jsx)("th",{className:"text-left py-3 px-2 font-semibold text-gray-700",children:"Numbers"}),(0,s.jsx)("th",{className:"text-left py-3 px-2 font-semibold text-gray-700",children:"Power"})]})}),(0,s.jsx)("tbody",{children:g.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors ".concat(t%2==0?"bg-white":"bg-gray-50"),children:[(0,s.jsx)("td",{className:"py-3 px-2 font-mono text-sm",children:e.id}),(0,s.jsx)("td",{className:"py-3 px-2 text-sm",children:b(e.date)}),(0,s.jsx)("td",{className:"py-3 px-2",children:(0,s.jsx)("div",{className:"flex space-x-1",children:e.result.map((e,t)=>(0,s.jsx)(p,{number:e,size:"xs"},t))})}),(0,s.jsx)("td",{className:"py-3 px-2",children:e.powerNumber&&(0,s.jsx)("div",{className:"w-6 h-6 rounded-full bg-red-500 text-white font-bold flex items-center justify-center text-xs",children:e.powerNumber})})]},e.id))})]})}),h>1&&(0,s.jsxs)("div",{className:"flex justify-center items-center space-x-2 mt-6",children:[(0,s.jsx)("button",{onClick:()=>n(Math.max(1,r-1)),disabled:1===r,className:"px-3 py-1 rounded text-sm bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,s.jsx)("div",{className:"flex space-x-1",children:Array.from({length:Math.min(5,h)},(e,t)=>{let a;return a=h<=5||r<=3?t+1:r>=h-2?h-4+t:r-2+t,(0,s.jsx)("button",{onClick:()=>n(a),className:"px-3 py-1 rounded text-sm transition-colors ".concat(r===a?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:a},a)})}),(0,s.jsx)("button",{onClick:()=>n(Math.min(h,r+1)),disabled:r===h,className:"px-3 py-1 rounded text-sm bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"Historical Data"}),(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-gray-400 text-4xl mb-2",children:"\uD83D\uDCCB"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No historical data available"})]})]})}function E(e){let{size:t="md",color:r="blue-600",text:a="Loading..."}=e;return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-4",children:[(0,s.jsx)(n.P.div,{className:"".concat({sm:"w-8 h-8",md:"w-12 h-12",lg:"w-16 h-16"}[t]," border-4 border-gray-200 border-t-").concat(r," rounded-full"),animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),(0,s.jsx)(n.P.p,{className:"".concat({sm:"text-sm",md:"text-base",lg:"text-lg"}[t]," text-gray-600 font-medium"),initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:a})]})}function L(){let[e,t]=(0,a.useState)([]),[r,c]=(0,a.useState)(!0),[o,d]=(0,a.useState)(null),[u,g]=(0,a.useState)(!1),[b,p]=(0,a.useState)("power655"),f=w.getInstance(),N=m.getInstance(),j=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],r=arguments.length>1?arguments[1]:void 0;try{c(!e),g(e),d(null),e&&f.clearCache();let s=r||b,a=await fetch("/api/lottery-data?type=".concat(s));if(!a.ok)throw Error("HTTP error! status: ".concat(a.status));let n=await a.json();t(n)}catch(e){d(e instanceof Error?e.message:"An error occurred")}finally{c(!1),g(!1)}};return((0,a.useEffect)(()=>{j()},[]),r)?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center",children:(0,s.jsx)(E,{size:"lg",text:"Loading lottery data..."})}):o?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-white to-pink-50 flex items-center justify-center",children:(0,s.jsxs)(n.P.div,{className:"text-center max-w-md mx-auto p-8",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},children:[(0,s.jsx)(n.P.div,{className:"text-red-500 text-6xl mb-4",animate:{rotate:[0,-10,10,-10,0]},transition:{duration:.5,delay:.2},children:"⚠️"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Error Loading Data"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:o}),(0,s.jsx)(h,{onClick:()=>j(!0),variant:"primary",icon:(0,s.jsx)(i.A,{size:16}),loading:u,children:"Retry"})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,s.jsx)(n.P.header,{className:"bg-white/80 backdrop-blur-sm shadow-lg border-b border-white/20",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(n.P.h1,{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent flex items-center",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,s.jsx)(l.A,{className:"mr-3 text-blue-600",size:32}),"Vietlott Analyzer"]}),(0,s.jsx)(n.P.p,{className:"text-gray-600 mt-2 text-lg",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.3},children:"AI-powered analysis for Power 6/55 & Mega 6/45"})]}),(0,s.jsxs)(n.P.div,{className:"flex items-center space-x-4",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.4},children:[(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Total Draws"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:e.length})]}),(0,s.jsx)(h,{onClick:()=>j(!0),variant:"secondary",size:"sm",icon:(0,s.jsx)(i.A,{size:16}),loading:u,children:"Refresh Data"})]})]})})}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,s.jsx)(x,{currentType:b,onTypeChange:e=>{p(e),N.setCurrentLotteryType(e),j(!0,e)}})}),(0,s.jsxs)(n.P.div,{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,s.jsx)(y,{data:e}),(0,s.jsx)(F,{data:e})]}),(0,s.jsx)(n.P.div,{className:"mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:(0,s.jsx)(P,{data:e})}),(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.9},children:(0,s.jsx)(q,{data:e})})]}),(0,s.jsx)(n.P.footer,{className:"bg-white/60 backdrop-blur-sm border-t border-white/20 mt-16",initial:{opacity:0},animate:{opacity:1},transition:{delay:1.1},children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,s.jsxs)("div",{className:"text-center text-gray-600",children:[(0,s.jsx)("p",{className:"text-sm",children:"⚠️ This application is for educational and entertainment purposes only."}),(0,s.jsx)("p",{className:"text-xs mt-1",children:"Lottery numbers are random. Past results do not guarantee future outcomes. Please gamble responsibly."})]})})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[647,588,441,684,358],()=>t(2328)),_N_E=e.O()}]);