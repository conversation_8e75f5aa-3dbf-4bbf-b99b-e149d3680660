{"version": 3, "names": ["React", "NativeModules", "Platform", "Switch", "NativeSwitch", "getSwitchColor", "useInternalTheme", "version", "PlatformConstants", "reactNativeVersion", "undefined", "value", "disabled", "onValueChange", "color", "theme", "themeOverrides", "rest", "checkedColor", "onTintColor", "thumbTintColor", "props", "major", "minor", "OS", "activeTrackColor", "thumbColor", "activeThumbColor", "trackColor", "true", "false", "createElement", "_extends"], "sourceRoot": "../../../../src", "sources": ["components/Switch/Switch.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,aAAa,EACbC,QAAQ,EAERC,MAAM,IAAIC,YAAY,QAEjB,cAAc;AAErB,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,gBAAgB,QAAQ,oBAAoB;AAGrD,MAAMC,OAAO,GAAGN,aAAa,CAACO,iBAAiB,GAC3CP,aAAa,CAACO,iBAAiB,CAACC,kBAAkB,GAClDC,SAAS;AA0Bb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMP,MAAM,GAAGA,CAAC;EACdQ,KAAK;EACLC,QAAQ;EACRC,aAAa;EACbC,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,KAAK;EACX,MAAMF,KAAK,GAAGT,gBAAgB,CAACU,cAAc,CAAC;EAC9C,MAAM;IAAEE,YAAY;IAAEC,WAAW;IAAEC;EAAe,CAAC,GAAGf,cAAc,CAAC;IACnEU,KAAK;IACLH,QAAQ;IACRD,KAAK;IACLG;EACF,CAAC,CAAC;EAEF,MAAMO,KAAK,GACTd,OAAO,IAAIA,OAAO,CAACe,KAAK,KAAK,CAAC,IAAIf,OAAO,CAACgB,KAAK,IAAI,EAAE,GACjD;IACEJ,WAAW;IACXC;EACF,CAAC,GACDlB,QAAQ,CAACsB,EAAE,KAAK,KAAK,GACrB;IACEC,gBAAgB,EAAEN,WAAW;IAC7BO,UAAU,EAAEN,cAAc;IAC1BO,gBAAgB,EAAET;EACpB,CAAC,GACD;IACEQ,UAAU,EAAEN,cAAc;IAC1BQ,UAAU,EAAE;MACVC,IAAI,EAAEV,WAAW;MACjBW,KAAK,EAAEX;IACT;EACF,CAAC;EAEP,oBACEnB,KAAA,CAAA+B,aAAA,CAAC3B,YAAY,EAAA4B,QAAA;IACXrB,KAAK,EAAEA,KAAM;IACbC,QAAQ,EAAEA,QAAS;IACnBC,aAAa,EAAED,QAAQ,GAAGF,SAAS,GAAGG;EAAc,GAChDQ,KAAK,EACLJ,IAAI,CACT,CAAC;AAEN,CAAC;AAED,eAAed,MAAM", "ignoreList": []}