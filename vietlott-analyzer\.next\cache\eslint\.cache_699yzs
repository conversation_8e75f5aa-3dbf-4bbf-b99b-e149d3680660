[{"C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\api\\lottery-data\\route.ts": "1", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\layout.tsx": "2", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\page.tsx": "3", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\HistoricalData.tsx": "4", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\LatestResults.tsx": "5", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\LotteryTypeSelector.tsx": "6", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\NumberSuggestion.tsx": "7", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\StatisticsChart.tsx": "8", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\ui\\Button.tsx": "9", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\ui\\Card.tsx": "10", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\ui\\LoadingSpinner.tsx": "11", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\ui\\NumberBall.tsx": "12", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\services\\LotteryConfigService.ts": "13", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\services\\LotteryDataService.ts": "14", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\services\\PredictionService.ts": "15", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\types\\lottery.ts": "16", "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\utils\\dataAnalysis.ts": "17"}, {"size": 5469, "mtime": 1748570800907, "results": "18", "hashOfConfig": "19"}, {"size": 1047, "mtime": 1748511551091, "results": "20", "hashOfConfig": "19"}, {"size": 7708, "mtime": 1748569660452, "results": "21", "hashOfConfig": "19"}, {"size": 8023, "mtime": 1748511404948, "results": "22", "hashOfConfig": "19"}, {"size": 5298, "mtime": 1748512033648, "results": "23", "hashOfConfig": "19"}, {"size": 7957, "mtime": 1748569560633, "results": "24", "hashOfConfig": "19"}, {"size": 23082, "mtime": 1748568799063, "results": "25", "hashOfConfig": "19"}, {"size": 8395, "mtime": 1748571266696, "results": "26", "hashOfConfig": "19"}, {"size": 2554, "mtime": 1748511991275, "results": "27", "hashOfConfig": "19"}, {"size": 1043, "mtime": 1748511944650, "results": "28", "hashOfConfig": "19"}, {"size": 1032, "mtime": 1748511975271, "results": "29", "hashOfConfig": "19"}, {"size": 1843, "mtime": 1748511964069, "results": "30", "hashOfConfig": "19"}, {"size": 4527, "mtime": 1748570429988, "results": "31", "hashOfConfig": "19"}, {"size": 13262, "mtime": 1748569341285, "results": "32", "hashOfConfig": "19"}, {"size": 13434, "mtime": 1748568614442, "results": "33", "hashOfConfig": "19"}, {"size": 1922, "mtime": 1748569356735, "results": "34", "hashOfConfig": "19"}, {"size": 5628, "mtime": 1748511275790, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "sdl14z", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\api\\lottery-data\\route.ts", ["87"], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\layout.tsx", [], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\page.tsx", ["88", "89", "90"], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\HistoricalData.tsx", [], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\LatestResults.tsx", [], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\LotteryTypeSelector.tsx", ["91"], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\NumberSuggestion.tsx", ["92", "93"], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\StatisticsChart.tsx", ["94", "95", "96", "97"], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\ui\\Button.tsx", [], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\ui\\Card.tsx", [], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\components\\ui\\NumberBall.tsx", [], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\services\\LotteryConfigService.ts", [], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\services\\LotteryDataService.ts", ["98"], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\services\\PredictionService.ts", ["99", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112", "113"], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\types\\lottery.ts", [], [], "C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\utils\\dataAnalysis.ts", [], [], {"ruleId": "114", "severity": 1, "message": "115", "line": 67, "column": 16, "nodeType": null, "messageId": "116", "endLine": 67, "endColumn": 26}, {"ruleId": "114", "severity": 1, "message": "117", "line": 5, "column": 20, "nodeType": null, "messageId": "116", "endLine": 5, "endColumn": 30}, {"ruleId": "114", "severity": 1, "message": "118", "line": 5, "column": 32, "nodeType": null, "messageId": "116", "endLine": 5, "endColumn": 40}, {"ruleId": "119", "severity": 1, "message": "120", "line": 61, "column": 6, "nodeType": "121", "endLine": 61, "endColumn": 8, "suggestions": "122"}, {"ruleId": "114", "severity": 1, "message": "123", "line": 6, "column": 23, "nodeType": null, "messageId": "116", "endLine": 6, "endColumn": 36}, {"ruleId": "119", "severity": 1, "message": "124", "line": 114, "column": 6, "nodeType": "121", "endLine": 114, "endColumn": 31, "suggestions": "125"}, {"ruleId": "119", "severity": 1, "message": "126", "line": 235, "column": 6, "nodeType": "121", "endLine": 235, "endColumn": 8, "suggestions": "127"}, {"ruleId": "114", "severity": 1, "message": "128", "line": 3, "column": 20, "nodeType": null, "messageId": "116", "endLine": 3, "endColumn": 29}, {"ruleId": "114", "severity": 1, "message": "129", "line": 5, "column": 33, "nodeType": null, "messageId": "116", "endLine": 5, "endColumn": 41}, {"ruleId": "114", "severity": 1, "message": "130", "line": 5, "column": 43, "nodeType": null, "messageId": "116", "endLine": 5, "endColumn": 46}, {"ruleId": "131", "severity": 1, "message": "132", "line": 150, "column": 37, "nodeType": "133", "messageId": "134", "endLine": 150, "endColumn": 40, "suggestions": "135"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 9, "column": 30, "nodeType": "133", "messageId": "134", "endLine": 9, "endColumn": 33, "suggestions": "136"}, {"ruleId": "114", "severity": 1, "message": "137", "line": 6, "column": 3, "nodeType": null, "messageId": "116", "endLine": 6, "endColumn": 18}, {"ruleId": "131", "severity": 1, "message": "132", "line": 332, "column": 51, "nodeType": "133", "messageId": "134", "endLine": 332, "endColumn": 54, "suggestions": "138"}, {"ruleId": "114", "severity": 1, "message": "139", "line": 342, "column": 30, "nodeType": null, "messageId": "116", "endLine": 342, "endColumn": 38}, {"ruleId": "131", "severity": 1, "message": "132", "line": 342, "column": 40, "nodeType": "133", "messageId": "134", "endLine": 342, "endColumn": 43, "suggestions": "140"}, {"ruleId": "131", "severity": 1, "message": "132", "line": 342, "column": 46, "nodeType": "133", "messageId": "134", "endLine": 342, "endColumn": 49, "suggestions": "141"}, {"ruleId": "114", "severity": 1, "message": "142", "line": 385, "column": 35, "nodeType": null, "messageId": "116", "endLine": 385, "endColumn": 42}, {"ruleId": "114", "severity": 1, "message": "143", "line": 390, "column": 29, "nodeType": null, "messageId": "116", "endLine": 390, "endColumn": 33}, {"ruleId": "114", "severity": 1, "message": "143", "line": 391, "column": 33, "nodeType": null, "messageId": "116", "endLine": 391, "endColumn": 37}, {"ruleId": "114", "severity": 1, "message": "144", "line": 399, "column": 71, "nodeType": null, "messageId": "116", "endLine": 399, "endColumn": 80}, {"ruleId": "114", "severity": 1, "message": "143", "line": 405, "column": 28, "nodeType": null, "messageId": "116", "endLine": 405, "endColumn": 32}, {"ruleId": "131", "severity": 1, "message": "132", "line": 405, "column": 52, "nodeType": "133", "messageId": "134", "endLine": 405, "endColumn": 55, "suggestions": "145"}, {"ruleId": "114", "severity": 1, "message": "143", "line": 406, "column": 34, "nodeType": null, "messageId": "116", "endLine": 406, "endColumn": 38}, {"ruleId": "131", "severity": 1, "message": "132", "line": 406, "column": 58, "nodeType": "133", "messageId": "134", "endLine": 406, "endColumn": 61, "suggestions": "146"}, {"ruleId": "114", "severity": 1, "message": "143", "line": 407, "column": 35, "nodeType": null, "messageId": "116", "endLine": 407, "endColumn": 39}, {"ruleId": "131", "severity": 1, "message": "132", "line": 407, "column": 59, "nodeType": "133", "messageId": "134", "endLine": 407, "endColumn": 62, "suggestions": "147"}, "@typescript-eslint/no-unused-vars", "'parseError' is defined but never used.", "unusedVar", "'TrendingUp' is defined but never used.", "'Database' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["148"], "'LotteryConfig' is defined but never used.", "React Hook useEffect has a missing dependency: 'generateSuggestion'. Either include it or remove the dependency array.", ["149"], "React Hook useEffect has missing dependencies: 'loadPerformance' and 'loadPredictions'. Either include them or remove the dependency array.", ["150"], "'useEffect' is defined but never used.", "'Activity' is defined but never used.", "'Eye' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["151", "152"], ["153", "154"], "'AdvancedPattern' is defined but never used.", ["155", "156"], "'features' is defined but never used.", ["157", "158"], ["159", "160"], "'numbers' is defined but never used.", "'data' is defined but never used.", "'targetSum' is defined but never used.", ["161", "162"], ["163", "164"], ["165", "166"], {"desc": "167", "fix": "168"}, {"desc": "169", "fix": "170"}, {"desc": "171", "fix": "172"}, {"messageId": "173", "fix": "174", "desc": "175"}, {"messageId": "176", "fix": "177", "desc": "178"}, {"messageId": "173", "fix": "179", "desc": "175"}, {"messageId": "176", "fix": "180", "desc": "178"}, {"messageId": "173", "fix": "181", "desc": "175"}, {"messageId": "176", "fix": "182", "desc": "178"}, {"messageId": "173", "fix": "183", "desc": "175"}, {"messageId": "176", "fix": "184", "desc": "178"}, {"messageId": "173", "fix": "185", "desc": "175"}, {"messageId": "176", "fix": "186", "desc": "178"}, {"messageId": "173", "fix": "187", "desc": "175"}, {"messageId": "176", "fix": "188", "desc": "178"}, {"messageId": "173", "fix": "189", "desc": "175"}, {"messageId": "176", "fix": "190", "desc": "178"}, {"messageId": "173", "fix": "191", "desc": "175"}, {"messageId": "176", "fix": "192", "desc": "178"}, "Update the dependencies array to be: [fetchData]", {"range": "193", "text": "194"}, "Update the dependencies array to be: [data, generateSuggestion, selectedAlgorithm]", {"range": "195", "text": "196"}, "Update the dependencies array to be: [loadPerformance, loadPredictions]", {"range": "197", "text": "198"}, "suggestUnknown", {"range": "199", "text": "200"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "201", "text": "202"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "203", "text": "200"}, {"range": "204", "text": "202"}, {"range": "205", "text": "200"}, {"range": "206", "text": "202"}, {"range": "207", "text": "200"}, {"range": "208", "text": "202"}, {"range": "209", "text": "200"}, {"range": "210", "text": "202"}, {"range": "211", "text": "200"}, {"range": "212", "text": "202"}, {"range": "213", "text": "200"}, {"range": "214", "text": "202"}, {"range": "215", "text": "200"}, {"range": "216", "text": "202"}, [2225, 2227], "[fetchData]", [3022, 3047], "[data, generateSuggestion, selectedAlgorithm]", [6473, 6475], "[loadPerformance, loadPredictions]", [4317, 4320], "unknown", [4317, 4320], "never", [203, 206], [203, 206], [10550, 10553], [10550, 10553], [10921, 10924], [10921, 10924], [10927, 10930], [10927, 10930], [13260, 13263], [13260, 13263], [13336, 13339], [13336, 13339], [13413, 13416], [13413, 13416]]