{"version": 3, "names": ["React", "View", "StyleSheet", "PortalManager", "PureComponent", "state", "portals", "mount", "key", "children", "setState", "update", "map", "item", "unmount", "filter", "render", "createElement", "collapsable", "pointerEvents", "style", "absoluteFill"], "sourceRoot": "../../../../src", "sources": ["components/Portal/PortalManager.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,EAAEC,UAAU,QAAQ,cAAc;AAS/C;AACA;AACA;AACA,eAAe,MAAMC,aAAa,SAASH,KAAK,CAACI,aAAa,CAAY;EACxEC,KAAK,GAAU;IACbC,OAAO,EAAE;EACX,CAAC;EAEDC,KAAK,GAAGA,CAACC,GAAW,EAAEC,QAAyB,KAAK;IAClD,IAAI,CAACC,QAAQ,CAAEL,KAAK,KAAM;MACxBC,OAAO,EAAE,CAAC,GAAGD,KAAK,CAACC,OAAO,EAAE;QAAEE,GAAG;QAAEC;MAAS,CAAC;IAC/C,CAAC,CAAC,CAAC;EACL,CAAC;EAEDE,MAAM,GAAGA,CAACH,GAAW,EAAEC,QAAyB,KAC9C,IAAI,CAACC,QAAQ,CAAEL,KAAK,KAAM;IACxBC,OAAO,EAAED,KAAK,CAACC,OAAO,CAACM,GAAG,CAAEC,IAAI,IAAK;MACnC,IAAIA,IAAI,CAACL,GAAG,KAAKA,GAAG,EAAE;QACpB,OAAO;UAAE,GAAGK,IAAI;UAAEJ;QAAS,CAAC;MAC9B;MACA,OAAOI,IAAI;IACb,CAAC;EACH,CAAC,CAAC,CAAC;EAELC,OAAO,GAAIN,GAAW,IACpB,IAAI,CAACE,QAAQ,CAAEL,KAAK,KAAM;IACxBC,OAAO,EAAED,KAAK,CAACC,OAAO,CAACS,MAAM,CAAEF,IAAI,IAAKA,IAAI,CAACL,GAAG,KAAKA,GAAG;EAC1D,CAAC,CAAC,CAAC;EAELQ,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACX,KAAK,CAACC,OAAO,CAACM,GAAG,CAAC,CAAC;MAAEJ,GAAG;MAAEC;IAAS,CAAC,kBAC9CT,KAAA,CAAAiB,aAAA,CAAChB,IAAI;MACHO,GAAG,EAAEA,GAAI;MACTU,WAAW,EACT,KAAK,CAAC,wGACP;MACDC,aAAa,EAAC,UAAU;MACxBC,KAAK,EAAElB,UAAU,CAACmB;IAAa,GAE9BZ,QACG,CACP,CAAC;EACJ;AACF", "ignoreList": []}