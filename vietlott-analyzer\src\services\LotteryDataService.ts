import { LotteryResult, LotteryStatistics, NumberFrequency } from '@/types/lottery';

export class LotteryDataService {
  private static instance: LotteryDataService;
  private cache: Map<string, any> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  public static getInstance(): LotteryDataService {
    if (!LotteryDataService.instance) {
      LotteryDataService.instance = new LotteryDataService();
    }
    return LotteryDataService.instance;
  }

  public async fetchLotteryData(): Promise<LotteryResult[]> {
    const cacheKey = 'lottery-data';
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }

    try {
      const response = await fetch('/api/lottery-data');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      this.cache.set(cacheKey, { data, timestamp: Date.now() });
      return data;
    } catch (error) {
      console.error('Failed to fetch lottery data:', error);
      throw error;
    }
  }

  public calculateNumberFrequency(data: LotteryResult[]): NumberFrequency[] {
    const frequency: { [key: number]: number } = {};
    const totalNumbers = data.length * 6;

    data.forEach(result => {
      result.result.forEach(number => {
        frequency[number] = (frequency[number] || 0) + 1;
      });
    });

    const frequencyArray: NumberFrequency[] = [];
    for (let i = 1; i <= 55; i++) {
      const count = frequency[i] || 0;
      frequencyArray.push({
        number: i,
        count,
        percentage: totalNumbers > 0 ? (count / totalNumbers) * 100 : 0,
      });
    }

    return frequencyArray.sort((a, b) => b.count - a.count);
  }

  public calculateStatistics(data: LotteryResult[]): LotteryStatistics {
    const allFrequency = this.calculateNumberFrequency(data);
    
    const now = new Date();
    const getFilteredData = (days: number) => {
      return data.filter(result => {
        const resultDate = new Date(result.date);
        const diffTime = now.getTime() - resultDate.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= days;
      });
    };

    return {
      totalDraws: data.length,
      mostFrequent: allFrequency.slice(0, 10),
      leastFrequent: allFrequency.slice(-10).reverse(),
      numberDistribution: allFrequency,
      recentTrends: {
        last30Days: this.calculateNumberFrequency(getFilteredData(30)),
        last60Days: this.calculateNumberFrequency(getFilteredData(60)),
        last90Days: this.calculateNumberFrequency(getFilteredData(90)),
      },
    };
  }

  public getHotNumbers(data: LotteryResult[], count: number = 6): number[] {
    const frequency = this.calculateNumberFrequency(data);
    return frequency.slice(0, count).map(f => f.number);
  }

  public getColdNumbers(data: LotteryResult[], count: number = 6): number[] {
    const frequency = this.calculateNumberFrequency(data);
    return frequency.slice(-count).map(f => f.number).reverse();
  }

  public getBalancedNumbers(data: LotteryResult[]): number[] {
    const hot = this.getHotNumbers(data, 3);
    const cold = this.getColdNumbers(data, 3);
    return [...hot, ...cold].sort((a, b) => a - b);
  }

  public getRecentTrendNumbers(data: LotteryResult[]): number[] {
    const stats = this.calculateStatistics(data);
    return stats.recentTrends.last30Days.slice(0, 6).map(f => f.number);
  }

  public getRandomNumbers(): number[] {
    const numbers: number[] = [];
    const used = new Set<number>();
    
    while (numbers.length < 6) {
      const num = Math.floor(Math.random() * 55) + 1;
      if (!used.has(num)) {
        numbers.push(num);
        used.add(num);
      }
    }
    
    return numbers.sort((a, b) => a - b);
  }

  public getMathematicalPatternNumbers(data: LotteryResult[]): number[] {
    const frequency = this.calculateNumberFrequency(data);
    const avgFrequency = frequency.reduce((sum, f) => sum + f.count, 0) / frequency.length;
    
    const balanced = frequency.filter(f => 
      Math.abs(f.count - avgFrequency) <= avgFrequency * 0.2
    );
    
    if (balanced.length >= 6) {
      return balanced.slice(0, 6).map(f => f.number);
    }
    
    return frequency.slice(0, 6).map(f => f.number);
  }

  public calculateConfidence(numbers: number[], data: LotteryResult[]): number {
    const stats = this.calculateStatistics(data);
    const avgFrequency = stats.numberDistribution.reduce((sum, f) => sum + f.count, 0) / stats.numberDistribution.length;
    const suggestedFrequencies = numbers.map(num => 
      stats.numberDistribution.find(f => f.number === num)?.count || 0
    );
    const avgSuggestedFreq = suggestedFrequencies.reduce((sum, f) => sum + f, 0) / suggestedFrequencies.length;
    return Math.min(100, Math.max(0, (avgSuggestedFreq / avgFrequency) * 50));
  }

  public clearCache(): void {
    this.cache.clear();
  }
}
