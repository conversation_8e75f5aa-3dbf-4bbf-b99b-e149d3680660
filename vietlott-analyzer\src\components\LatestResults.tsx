import { LotteryResult } from '@/types/lottery';

interface LatestResultsProps {
  data: LotteryResult[];
}

export default function LatestResults({ data }: LatestResultsProps) {
  const latestResults = data.slice(0, 5); // Show last 5 results

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const NumberBall = ({ number, isPower = false }: { number: number; isPower?: boolean }) => (
    <div
      className={`
        w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm
        ${isPower 
          ? 'bg-red-500 border-2 border-red-600' 
          : 'bg-blue-500 border-2 border-blue-600'
        }
        shadow-lg
      `}
    >
      {number}
    </div>
  );

  if (!latestResults.length) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold text-gray-800 mb-4">Latest Results</h2>
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-2">🎱</div>
          <p className="text-gray-500">No lottery results available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
        <span className="mr-2">🎯</span>
        Latest Results
      </h2>
      
      <div className="space-y-4">
        {latestResults.map((result, index) => (
          <div
            key={result.id}
            className={`
              p-4 rounded-lg border-2 transition-all duration-200
              ${index === 0 
                ? 'border-blue-200 bg-blue-50' 
                : 'border-gray-200 bg-gray-50'
              }
            `}
          >
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="font-semibold text-gray-800">
                  Draw #{result.id}
                  {index === 0 && (
                    <span className="ml-2 px-2 py-1 bg-blue-500 text-white text-xs rounded-full">
                      Latest
                    </span>
                  )}
                </h3>
                <p className="text-sm text-gray-600">{formatDate(result.date)}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-sm font-medium text-gray-700">Numbers:</span>
              <div className="flex space-x-1">
                {result.result.map((number, numIndex) => (
                  <NumberBall key={numIndex} number={number} />
                ))}
              </div>
            </div>
            
            {result.powerNumber && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">Power:</span>
                <NumberBall number={result.powerNumber} isPower={true} />
              </div>
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-4 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500 text-center">
          Showing {latestResults.length} most recent draws
        </p>
      </div>
    </div>
  );
}
