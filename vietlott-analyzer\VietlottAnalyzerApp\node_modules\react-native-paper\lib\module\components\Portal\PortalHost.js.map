{"version": 3, "names": ["React", "View", "StyleSheet", "PortalManager", "PortalContext", "createContext", "PortalHost", "Component", "displayName", "componentDidMount", "manager", "queue", "length", "action", "pop", "type", "mount", "key", "children", "update", "unmount", "setManager", "<PERSON><PERSON><PERSON>", "push", "op", "index", "findIndex", "o", "render", "createElement", "Provider", "value", "style", "styles", "container", "collapsable", "pointerEvents", "props", "ref", "create", "flex"], "sourceRoot": "../../../../src", "sources": ["components/Portal/PortalHost.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,EAAEC,UAAU,QAAQ,cAAc;AAE/C,OAAOC,aAAa,MAAM,iBAAiB;AAiB3C,OAAO,MAAMC,aAAa,gBAAGJ,KAAK,CAACK,aAAa,CAAgB,IAAW,CAAC;;AAE5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,UAAU,SAASN,KAAK,CAACO,SAAS,CAAQ;EAC7D,OAAOC,WAAW,GAAG,aAAa;EAElCC,iBAAiBA,CAAA,EAAG;IAClB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK;IAExB,OAAOA,KAAK,CAACC,MAAM,IAAIF,OAAO,EAAE;MAC9B,MAAMG,MAAM,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;MAC1B,IAAID,MAAM,EAAE;QACV;QACA,QAAQA,MAAM,CAACE,IAAI;UACjB,KAAK,OAAO;YACVL,OAAO,CAACM,KAAK,CAACH,MAAM,CAACI,GAAG,EAAEJ,MAAM,CAACK,QAAQ,CAAC;YAC1C;UACF,KAAK,QAAQ;YACXR,OAAO,CAACS,MAAM,CAACN,MAAM,CAACI,GAAG,EAAEJ,MAAM,CAACK,QAAQ,CAAC;YAC3C;UACF,KAAK,SAAS;YACZR,OAAO,CAACU,OAAO,CAACP,MAAM,CAACI,GAAG,CAAC;YAC3B;QACJ;MACF;IACF;EACF;EAEQI,UAAU,GAAIX,OAAyC,IAAK;IAClE,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB,CAAC;EAEOM,KAAK,GAAIE,QAAyB,IAAK;IAC7C,MAAMD,GAAG,GAAG,IAAI,CAACK,OAAO,EAAE;IAE1B,IAAI,IAAI,CAACZ,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACM,KAAK,CAACC,GAAG,EAAEC,QAAQ,CAAC;IACnC,CAAC,MAAM;MACL,IAAI,CAACP,KAAK,CAACY,IAAI,CAAC;QAAER,IAAI,EAAE,OAAO;QAAEE,GAAG;QAAEC;MAAS,CAAC,CAAC;IACnD;IAEA,OAAOD,GAAG;EACZ,CAAC;EAEOE,MAAM,GAAGA,CAACF,GAAW,EAAEC,QAAyB,KAAK;IAC3D,IAAI,IAAI,CAACR,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACS,MAAM,CAACF,GAAG,EAAEC,QAAQ,CAAC;IACpC,CAAC,MAAM;MACL,MAAMM,EAAa,GAAG;QAAET,IAAI,EAAE,OAAO;QAAEE,GAAG;QAAEC;MAAS,CAAC;MACtD,MAAMO,KAAK,GAAG,IAAI,CAACd,KAAK,CAACe,SAAS,CAC/BC,CAAC,IAAKA,CAAC,CAACZ,IAAI,KAAK,OAAO,IAAKY,CAAC,CAACZ,IAAI,KAAK,QAAQ,IAAIY,CAAC,CAACV,GAAG,KAAKA,GACjE,CAAC;MAED,IAAIQ,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACd,KAAK,CAACc,KAAK,CAAC,GAAGD,EAAE;MACxB,CAAC,MAAM;QACL,IAAI,CAACb,KAAK,CAACY,IAAI,CAACC,EAAe,CAAC;MAClC;IACF;EACF,CAAC;EAEOJ,OAAO,GAAIH,GAAW,IAAK;IACjC,IAAI,IAAI,CAACP,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACU,OAAO,CAACH,GAAG,CAAC;IAC3B,CAAC,MAAM;MACL,IAAI,CAACN,KAAK,CAACY,IAAI,CAAC;QAAER,IAAI,EAAE,SAAS;QAAEE;MAAI,CAAC,CAAC;IAC3C;EACF,CAAC;EAEOK,OAAO,GAAG,CAAC;EACXX,KAAK,GAAgB,EAAE;EAG/BiB,MAAMA,CAAA,EAAG;IACP,oBACE5B,KAAA,CAAA6B,aAAA,CAACzB,aAAa,CAAC0B,QAAQ;MACrBC,KAAK,EAAE;QACLf,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBG,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBC,OAAO,EAAE,IAAI,CAACA;MAChB;IAAE,gBAGFpB,KAAA,CAAA6B,aAAA,CAAC5B,IAAI;MACH+B,KAAK,EAAEC,MAAM,CAACC,SAAU;MACxBC,WAAW,EAAE,KAAM;MACnBC,aAAa,EAAC;IAAU,GAEvB,IAAI,CAACC,KAAK,CAACnB,QACR,CAAC,eACPlB,KAAA,CAAA6B,aAAA,CAAC1B,aAAa;MAACmC,GAAG,EAAE,IAAI,CAACjB;IAAW,CAAE,CAChB,CAAC;EAE7B;AACF;AAEA,MAAMY,MAAM,GAAG/B,UAAU,CAACqC,MAAM,CAAC;EAC/BL,SAAS,EAAE;IACTM,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}