(()=>{var t={};t.id=974,t.ids=[974],t.modules={440:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>r});var s=i(1658);let r=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},554:(t,e)=>{"use strict";function i(t){return t.endsWith("/route")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isAppRouteRoute",{enumerable:!0,get:function(){return i}})},660:(t,e)=>{"use strict";function i(t){let e=5381;for(let i=0;i<t.length;i++)e=(e<<5)+e+t.charCodeAt(i)|0;return e>>>0}function s(t){return i(t).toString(36).slice(0,5)}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{djb2Hash:function(){return i},hexHash:function(){return s}})},846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});let s=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Work\\\\Automation\\\\Draff\\\\vietlott-analyzer\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\page.tsx","default")},1437:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{INTERCEPTION_ROUTE_MARKERS:function(){return r},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return n}});let s=i(4722),r=["(..)(..)","(.)","(..)","(...)"];function n(t){return void 0!==t.split("/").find(t=>r.find(e=>t.startsWith(e)))}function a(t){let e,i,n;for(let s of t.split("/"))if(i=r.find(t=>s.startsWith(t))){[e,n]=t.split(i,2);break}if(!e||!i||!n)throw Object.defineProperty(Error("Invalid interception route: "+t+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(e=(0,s.normalizeAppPath)(e),i){case"(.)":n="/"===e?"/"+n:e+"/"+n;break;case"(..)":if("/"===e)throw Object.defineProperty(Error("Invalid interception route: "+t+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});n=e.split("/").slice(0,-1).concat(n).join("/");break;case"(...)":n="/"+n;break;case"(..)(..)":let a=e.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+t+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});n=a.slice(0,-2).concat(n).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:e,interceptedRoute:n}}},1658:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return f},normalizeMetadataRoute:function(){return p}});let s=i(8304),r=function(t){return t&&t.__esModule?t:{default:t}}(i(8671)),n=i(6341),a=i(4396),o=i(660),l=i(4722),h=i(2958),c=i(5499);function u(t){let e=r.default.dirname(t);if(t.endsWith("/sitemap"))return"";let i="";return e.split("/").some(t=>(0,c.isGroupSegment)(t)||(0,c.isParallelRouteSegment)(t))&&(i=(0,o.djb2Hash)(e).toString(36).slice(0,6)),i}function d(t,e,i){let s=(0,l.normalizeAppPath)(t),o=(0,a.getNamedRouteRegex)(s,{prefixRouteKeys:!1}),c=(0,n.interpolateDynamicPath)(s,e,o),{name:d,ext:p}=r.default.parse(i),f=u(r.default.posix.join(t,d)),m=f?`-${f}`:"";return(0,h.normalizePathSep)(r.default.join(c,`${d}${m}${p}`))}function p(t){if(!(0,s.isMetadataPage)(t))return t;let e=t,i="";if("/robots"===t?e+=".txt":"/manifest"===t?e+=".webmanifest":i=u(t),!e.endsWith("/route")){let{dir:t,name:s,ext:n}=r.default.parse(e);e=r.default.posix.join(t,`${s}${i?`-${i}`:""}${n}`,"route")}return e}function f(t,e){let i=t.endsWith("/route"),s=i?t.slice(0,-6):t,r=s.endsWith("/sitemap")?".xml":"";return(e?`${s}/[__metadata_id__]`:`${s}${r}`)+(i?"/route":"")}},2029:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},2437:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getPathMatch",{enumerable:!0,get:function(){return r}});let s=i(5362);function r(t,e){let i=[],r=(0,s.pathToRegexp)(t,i,{delimiter:"/",sensitive:"boolean"==typeof(null==e?void 0:e.sensitive)&&e.sensitive,strict:null==e?void 0:e.strict}),n=(0,s.regexpToFunction)((null==e?void 0:e.regexModifier)?new RegExp(e.regexModifier(r.source),r.flags):r,i);return(t,s)=>{if("string"!=typeof t)return!1;let r=n(t);if(!r)return!1;if(null==e?void 0:e.removeUnnamedParams)for(let t of i)"number"==typeof t.name&&delete r.params[t.name];return{...s,...r.params}}}},2785:(t,e)=>{"use strict";function i(t){let e={};for(let[i,s]of t.entries()){let t=e[i];void 0===t?e[i]=s:Array.isArray(t)?t.push(s):e[i]=[t,s]}return e}function s(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function r(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))if(Array.isArray(r))for(let t of r)e.append(i,s(t));else e.set(i,s(r));return e}function n(t){for(var e=arguments.length,i=Array(e>1?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,s]of e.entries())t.append(i,s)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return n},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},2958:(t,e)=>{"use strict";function i(t){return t.replace(/\\/g,"/")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"normalizePathSep",{enumerable:!0,get:function(){return i}})},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"escapeStringRegexp",{enumerable:!0,get:function(){return r}});let i=/[|\\{}()[\]^$+*?.-]/,s=/[|\\{}()[\]^$+*?.-]/g;function r(t){return i.test(t)?t.replace(s,"\\$&"):t}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parseRelativeUrl",{enumerable:!0,get:function(){return r}}),i(4827);let s=i(2785);function r(t,e,i){void 0===i&&(i=!0);let r=new URL("http://n"),n=e?new URL(e,r):t.startsWith(".")?new URL("http://n"):r,{pathname:a,searchParams:o,search:l,hash:h,href:c,origin:u}=new URL(t,n);if(u!==r.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+t),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:i?(0,s.searchParamsToUrlQuery)(o):void 0,search:l,hash:h,href:c.slice(u.length)}}},3747:()=>{},3873:t=>{"use strict";t.exports=require("path")},4396:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return u},parseParameter:function(){return l}});let s=i(6143),r=i(1437),n=i(3293),a=i(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(t){let e=t.match(o);return e?h(e[2]):h(t)}function h(t){let e=t.startsWith("[")&&t.endsWith("]");e&&(t=t.slice(1,-1));let i=t.startsWith("...");return i&&(t=t.slice(3)),{key:t,repeat:i,optional:e}}function c(t,e,i){let s={},l=1,c=[];for(let u of(0,a.removeTrailingSlash)(t).slice(1).split("/")){let t=r.INTERCEPTION_ROUTE_MARKERS.find(t=>u.startsWith(t)),a=u.match(o);if(t&&a&&a[2]){let{key:e,optional:i,repeat:r}=h(a[2]);s[e]={pos:l++,repeat:r,optional:i},c.push("/"+(0,n.escapeStringRegexp)(t)+"([^/]+?)")}else if(a&&a[2]){let{key:t,repeat:e,optional:r}=h(a[2]);s[t]={pos:l++,repeat:e,optional:r},i&&a[1]&&c.push("/"+(0,n.escapeStringRegexp)(a[1]));let o=e?r?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";i&&a[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,n.escapeStringRegexp)(u));e&&a&&a[3]&&c.push((0,n.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:s}}function u(t,e){let{includeSuffix:i=!1,includePrefix:s=!1,excludeOptionalTrailingSlash:r=!1}=void 0===e?{}:e,{parameterizedRoute:n,groups:a}=c(t,i,s),o=n;return r||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function d(t){let e,{interceptionMarker:i,getSafeRouteKey:s,segment:r,routeKeys:a,keyPrefix:o,backreferenceDuplicateKeys:l}=t,{key:c,optional:u,repeat:d}=h(r),p=c.replace(/\W/g,"");o&&(p=""+o+p);let f=!1;(0===p.length||p.length>30)&&(f=!0),isNaN(parseInt(p.slice(0,1)))||(f=!0),f&&(p=s());let m=p in a;o?a[p]=""+o+c:a[p]=c;let g=i?(0,n.escapeStringRegexp)(i):"";return e=m&&l?"\\k<"+p+">":d?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",u?"(?:/"+g+e+")?":"/"+g+e}function p(t,e,i,l,h){let c,u=(c=0,()=>{let t="",e=++c;for(;e>0;)t+=String.fromCharCode(97+(e-1)%26),e=Math.floor((e-1)/26);return t}),p={},f=[];for(let c of(0,a.removeTrailingSlash)(t).slice(1).split("/")){let t=r.INTERCEPTION_ROUTE_MARKERS.some(t=>c.startsWith(t)),a=c.match(o);if(t&&a&&a[2])f.push(d({getSafeRouteKey:u,interceptionMarker:a[1],segment:a[2],routeKeys:p,keyPrefix:e?s.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:h}));else if(a&&a[2]){l&&a[1]&&f.push("/"+(0,n.escapeStringRegexp)(a[1]));let t=d({getSafeRouteKey:u,segment:a[2],routeKeys:p,keyPrefix:e?s.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:h});l&&a[1]&&(t=t.substring(1)),f.push(t)}else f.push("/"+(0,n.escapeStringRegexp)(c));i&&a&&a[3]&&f.push((0,n.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:f.join(""),routeKeys:p}}function f(t,e){var i,s,r;let n=p(t,e.prefixRouteKeys,null!=(i=e.includeSuffix)&&i,null!=(s=e.includePrefix)&&s,null!=(r=e.backreferenceDuplicateKeys)&&r),a=n.namedParameterizedRoute;return e.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...u(t,e),namedRegex:"^"+a+"$",routeKeys:n.routeKeys}}function m(t,e){let{parameterizedRoute:i}=c(t,!1,!1),{catchAll:s=!0}=e;if("/"===i)return{namedRegex:"^/"+(s?".*":"")+"$"};let{namedParameterizedRoute:r}=p(t,!1,!1,!1,!1);return{namedRegex:"^"+r+(s?"(?:(/.*)?)":"")+"$"}}},4431:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>c,metadata:()=>l,viewport:()=>h});var s=i(7413),r=i(2376),n=i.n(r),a=i(8726),o=i.n(a);i(1135);let l={title:"Vietlott Power 6/55 Analyzer",description:"Historical analysis and number suggestions for Vietnam Vietlott Power 6/55 lottery. Get insights from past draws and generate smart number suggestions.",keywords:"vietlott, lottery, power 6/55, vietnam, analysis, statistics, number suggestions",authors:[{name:"Vietlott Analyzer"}]},h={width:"device-width",initialScale:1};function c({children:t}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:t})})}},4722:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{normalizeAppPath:function(){return n},normalizeRscURL:function(){return a}});let s=i(5531),r=i(5499);function n(t){return(0,s.ensureLeadingSlash)(t.split("/").reduce((t,e,i,s)=>!e||(0,r.isGroupSegment)(e)||"@"===e[0]||("page"===e||"route"===e)&&i===s.length-1?t:t+"/"+e,""))}function a(t){return t.replace(/\.rsc($|\?)/,"$1")}},4827:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return x},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return s},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return n},isResSent:function(){return h},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function s(t){let e,i=!1;return function(){for(var s=arguments.length,r=Array(s),n=0;n<s;n++)r[n]=arguments[n];return i||(i=!0,e=t(...r)),e}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,n=t=>r.test(t);function a(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function o(){let{href:t}=window.location,e=a();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function h(t){return t.finished||t.headersSent}function c(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function u(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await u(e.Component,e.ctx)}:{};let s=await t.getInitialProps(e);if(i&&h(i))return s;if(!s)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+s+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class f extends Error{}class m extends Error{}class g extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class x extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(t){return JSON.stringify({message:t.message,stack:t.stack})}},5034:(t,e,i)=>{"use strict";let s,r;i.r(e),i.d(e,{default:()=>uI});var n,a,o=i(687),l=i(3210);function h(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function c(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function u(t,e,i,s){if("function"==typeof e){let[r,n]=c(s);e=e(void 0!==i?i:t.custom,r,n)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,n]=c(s);e=e(void 0!==i?i:t.custom,r,n)}return e}function d(t,e,i){let s=t.getProps();return u(s,e,void 0!==i?i:s.custom,t)}function p(t,e){return t?.[e]??t?.default??t}let f=t=>t,m={},g=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],x={value:null,addProjectionMetrics:null};function y(t,e){let i=!1,s=!0,r={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,a=g.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,r=!1,n=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){a.has(e)&&(c.schedule(e),t()),l++,e(o)}let c={schedule:(t,e=!1,n=!1)=>{let o=n&&r?i:s;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{s.delete(t),a.delete(t)},process:t=>{if(o=t,r){n=!0;return}r=!0,[i,s]=[s,i],i.forEach(h),e&&x.value&&x.value.frameloop[e].push(l),l=0,i.clear(),r=!1,n&&(n=!1,c.process(t))}};return c}(n,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:h,preUpdate:c,update:u,preRender:d,render:p,postRender:f}=a,y=()=>{let n=m.useManualTiming?r.timestamp:performance.now();i=!1,m.useManualTiming||(r.delta=s?1e3/60:Math.max(Math.min(n-r.timestamp,40),1)),r.timestamp=n,r.isProcessing=!0,o.process(r),l.process(r),h.process(r),c.process(r),u.process(r),d.process(r),p.process(r),f.process(r),r.isProcessing=!1,i&&e&&(s=!1,t(y))},b=()=>{i=!0,s=!0,r.isProcessing||t(y)};return{schedule:g.reduce((t,e)=>{let s=a[e];return t[e]=(t,e=!1,r=!1)=>(i||b(),s.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<g.length;e++)a[g[e]].cancel(t)},state:r,steps:a}}let{schedule:b,cancel:v,state:_,steps:w}=y("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:f,!0),M=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],P=new Set(M),k=new Set(["width","height","top","left","right","bottom",...M]);function S(t,e){-1===t.indexOf(e)&&t.push(e)}function T(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class E{constructor(){this.subscriptions=[]}add(t){return S(this.subscriptions,t),()=>T(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let r=0;r<s;r++){let s=this.subscriptions[r];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function j(){s=void 0}let A={now:()=>(void 0===s&&A.set(_.isProcessing||m.useManualTiming?_.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(j)}},C=t=>!isNaN(parseFloat(t)),D={current:void 0};class N{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=A.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=A.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=C(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new E);let i=this.events[t].add(e);return"change"===t?()=>{i(),b.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return D.current&&D.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=A.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function R(t,e){return new N(t,e)}let O=t=>Array.isArray(t),L=t=>!!(t&&t.getVelocity);function V(t,e){let i=t.getValue("willChange");if(L(i)&&i.add)return i.add(e);if(!i&&m.WillChange){let i=new m.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let F=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),I="data-"+F("framerAppearId"),z=(t,e)=>i=>e(t(i)),B=(...t)=>t.reduce(z),$=(t,e,i)=>i>e?e:i<t?t:i,W=t=>1e3*t,H=t=>t/1e3,U={layout:0,mainThread:0,waapi:0},q=()=>{},Y=()=>{},X=t=>e=>"string"==typeof e&&e.startsWith(t),K=X("--"),G=X("var(--"),Z=t=>!!G(t)&&Q.test(t.split("/*")[0].trim()),Q=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,J={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tt={...J,transform:t=>$(0,1,t)},te={...J,default:1},ti=t=>Math.round(1e5*t)/1e5,ts=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tr=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tn=(t,e)=>i=>!!("string"==typeof i&&tr.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),ta=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[r,n,a,o]=s.match(ts);return{[t]:parseFloat(r),[e]:parseFloat(n),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},to=t=>$(0,255,t),tl={...J,transform:t=>Math.round(to(t))},th={test:tn("rgb","red"),parse:ta("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tl.transform(t)+", "+tl.transform(e)+", "+tl.transform(i)+", "+ti(tt.transform(s))+")"},tc={test:tn("#"),parse:function(t){let e="",i="",s="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,s+=s,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}},transform:th.transform},tu=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),td=tu("deg"),tp=tu("%"),tf=tu("px"),tm=tu("vh"),tg=tu("vw"),tx={...tp,parse:t=>tp.parse(t)/100,transform:t=>tp.transform(100*t)},ty={test:tn("hsl","hue"),parse:ta("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tp.transform(ti(e))+", "+tp.transform(ti(i))+", "+ti(tt.transform(s))+")"},tb={test:t=>th.test(t)||tc.test(t)||ty.test(t),parse:t=>th.test(t)?th.parse(t):ty.test(t)?ty.parse(t):tc.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?th.transform(t):ty.transform(t)},tv=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,t_="number",tw="color",tM=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tP(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},r=[],n=0,a=e.replace(tM,t=>(tb.test(t)?(s.color.push(n),r.push(tw),i.push(tb.parse(t))):t.startsWith("var(")?(s.var.push(n),r.push("var"),i.push(t)):(s.number.push(n),r.push(t_),i.push(parseFloat(t))),++n,"${}")).split("${}");return{values:i,split:a,indexes:s,types:r}}function tk(t){return tP(t).values}function tS(t){let{split:e,types:i}=tP(t),s=e.length;return t=>{let r="";for(let n=0;n<s;n++)if(r+=e[n],void 0!==t[n]){let e=i[n];e===t_?r+=ti(t[n]):e===tw?r+=tb.transform(t[n]):r+=t[n]}return r}}let tT=t=>"number"==typeof t?0:t,tE={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(ts)?.length||0)+(t.match(tv)?.length||0)>0},parse:tk,createTransformer:tS,getAnimatableNone:function(t){let e=tk(t);return tS(t)(e.map(tT))}};function tj(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tA(t,e){return i=>i>0?e:t}let tC=(t,e,i)=>t+(e-t)*i,tD=(t,e,i)=>{let s=t*t,r=i*(e*e-s)+s;return r<0?0:Math.sqrt(r)},tN=[tc,th,ty],tR=t=>tN.find(e=>e.test(t));function tO(t){let e=tR(t);if(q(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===ty&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let r=0,n=0,a=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,o=2*i-s;r=tj(o,s,t+1/3),n=tj(o,s,t),a=tj(o,s,t-1/3)}else r=n=a=i;return{red:Math.round(255*r),green:Math.round(255*n),blue:Math.round(255*a),alpha:s}}(i)),i}let tL=(t,e)=>{let i=tO(t),s=tO(e);if(!i||!s)return tA(t,e);let r={...i};return t=>(r.red=tD(i.red,s.red,t),r.green=tD(i.green,s.green,t),r.blue=tD(i.blue,s.blue,t),r.alpha=tC(i.alpha,s.alpha,t),th.transform(r))},tV=new Set(["none","hidden"]);function tF(t,e){return i=>tC(t,e,i)}function tI(t){return"number"==typeof t?tF:"string"==typeof t?Z(t)?tA:tb.test(t)?tL:t$:Array.isArray(t)?tz:"object"==typeof t?tb.test(t)?tL:tB:tA}function tz(t,e){let i=[...t],s=i.length,r=t.map((t,i)=>tI(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=r[e](t);return i}}function tB(t,e){let i={...t,...e},s={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(s[r]=tI(t[r])(t[r],e[r]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let t$=(t,e)=>{let i=tE.createTransformer(e),s=tP(t),r=tP(e);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?tV.has(t)&&!r.values.length||tV.has(e)&&!s.values.length?function(t,e){return tV.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):B(tz(function(t,e){let i=[],s={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let n=e.types[r],a=t.indexes[n][s[n]],o=t.values[a]??0;i[r]=o,s[n]++}return i}(s,r),r.values),i):(q(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tA(t,e))};function tW(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tC(t,e,i):tI(t)(t,e)}let tH=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>b.update(e,t),stop:()=>v(e),now:()=>_.isProcessing?_.timestamp:A.now()}},tU=(t,e,i=10)=>{let s="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)s+=t(e/(r-1))+", ";return`linear(${s.substring(0,s.length-2)})`};function tq(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tY(t,e,i){var s,r;let n=Math.max(e-5,0);return s=i-t(n),(r=e-n)?1e3/r*s:0}let tX={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tK(t,e){return t*Math.sqrt(1-e*e)}let tG=["duration","bounce"],tZ=["stiffness","damping","mass"];function tQ(t,e){return e.some(e=>void 0!==t[e])}function tJ(t=tX.visualDuration,e=tX.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:n}=s,a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:h,damping:c,mass:u,duration:d,velocity:p,isResolvedFromDuration:f}=function(t){let e={velocity:tX.velocity,stiffness:tX.stiffness,damping:tX.damping,mass:tX.mass,isResolvedFromDuration:!1,...t};if(!tQ(t,tZ)&&tQ(t,tG))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,r=2*$(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:tX.mass,stiffness:s,damping:r}}else{let i=function({duration:t=tX.duration,bounce:e=tX.bounce,velocity:i=tX.velocity,mass:s=tX.mass}){let r,n;q(t<=W(tX.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=$(tX.minDamping,tX.maxDamping,a),t=$(tX.minDuration,tX.maxDuration,H(t)),a<1?(r=e=>{let s=e*a,r=s*t;return .001-(s-i)/tK(e,a)*Math.exp(-r)},n=e=>{let s=e*a*t,n=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-s),l=tK(Math.pow(e,2),a);return(s*i+i-n)*o*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),n=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(r,n,5/t);if(t=W(t),isNaN(o))return{stiffness:tX.stiffness,damping:tX.damping,duration:t};{let e=Math.pow(o,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:tX.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-H(s.velocity||0)}),m=p||0,g=c/(2*Math.sqrt(h*u)),x=o-a,y=H(Math.sqrt(h/u)),b=5>Math.abs(x);if(r||(r=b?tX.restSpeed.granular:tX.restSpeed.default),n||(n=b?tX.restDelta.granular:tX.restDelta.default),g<1){let t=tK(y,g);i=e=>o-Math.exp(-g*y*e)*((m+g*y*x)/t*Math.sin(t*e)+x*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-y*t)*(x+(m+y*x)*t);else{let t=y*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*y*e),s=Math.min(t*e,300);return o-i*((m+g*y*x)*Math.sinh(s)+t*x*Math.cosh(s))/t}}let v={calculatedDuration:f&&d||null,next:t=>{let e=i(t);if(f)l.done=t>=d;else{let s=0===t?m:0;g<1&&(s=0===t?W(m):tY(i,t,e));let a=Math.abs(o-e)<=n;l.done=Math.abs(s)<=r&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(tq(v),2e4),e=tU(e=>v.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return v}function t0({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:n=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:c}){let u,d,p=t[0],f={done:!1,value:p},m=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,x=i*e,y=p+x,b=void 0===a?y:a(y);b!==y&&(x=b-p);let v=t=>-x*Math.exp(-t/s),_=t=>b+v(t),w=t=>{let e=v(t),i=_(t);f.done=Math.abs(e)<=h,f.value=f.done?b:i},M=t=>{m(f.value)&&(u=t,d=tJ({keyframes:[f.value,g(f.value)],velocity:tY(_,t,f.value),damping:r,stiffness:n,restDelta:h,restSpeed:c}))};return M(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==u||(e=!0,w(t),M(t)),void 0!==u&&t>=u)?d.next(t-u):(e||w(t),f)}}}tJ.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),r=Math.min(tq(s),2e4);return{type:"keyframes",ease:t=>s.next(r*t).value/e,duration:H(r)}}(t,100,tJ);return t.ease=e.ease,t.duration=W(e.duration),t.type="keyframes",t};let t1=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t2(t,e,i,s){if(t===e&&i===s)return f;let r=e=>(function(t,e,i,s,r){let n,a,o=0;do(n=t1(a=e+(i-e)/2,s,r)-t)>0?i=a:e=a;while(Math.abs(n)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:t1(r(t),e,s)}let t5=t2(.42,0,1,1),t6=t2(0,0,.58,1),t3=t2(.42,0,.58,1),t4=t=>Array.isArray(t)&&"number"!=typeof t[0],t8=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t9=t=>e=>1-t(1-e),t7=t2(.33,1.53,.69,.99),et=t9(t7),ee=t8(et),ei=t=>(t*=2)<1?.5*et(t):.5*(2-Math.pow(2,-10*(t-1))),es=t=>1-Math.sin(Math.acos(t)),er=t9(es),en=t8(es),ea=t=>Array.isArray(t)&&"number"==typeof t[0],eo={linear:f,easeIn:t5,easeInOut:t3,easeOut:t6,circIn:es,circInOut:en,circOut:er,backIn:et,backInOut:ee,backOut:t7,anticipate:ei},el=t=>"string"==typeof t,eh=t=>{if(ea(t)){Y(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,r]=t;return t2(e,i,s,r)}return el(t)?(Y(void 0!==eo[t],`Invalid easing type '${t}'`),eo[t]):t},ec=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s};function eu({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var r;let n=t4(s)?s.map(eh):eh(s),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:s,mixer:r}={}){let n=t.length;if(Y(n===e.length,"Both input and output ranges must be the same length"),1===n)return()=>e[0];if(2===n&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[n-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let s=[],r=i||m.mix||tW,n=t.length-1;for(let i=0;i<n;i++){let n=r(t[i],t[i+1]);e&&(n=B(Array.isArray(e)?e[i]||f:e,n)),s.push(n)}return s}(e,s,r),l=o.length,h=i=>{if(a&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let r=ec(t[s],t[s+1],i);return o[s](r)};return i?e=>h($(t[0],t[n-1],e)):h}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let r=ec(0,e,s);t.push(tC(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(n)?n:e.map(()=>n||t3).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let ed=t=>null!==t;function ep(t,{repeat:e,repeatType:i="loop"},s,r=1){let n=t.filter(ed),a=r<0||e&&"loop"!==i&&e%2==1?0:n.length-1;return a&&void 0!==s?s:n[a]}let ef={decay:t0,inertia:t0,tween:eu,keyframes:eu,spring:tJ};function em(t){"string"==typeof t.type&&(t.type=ef[t.type])}class eg{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ex=t=>t/100;class ey extends eg{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==A.now()&&this.tick(A.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},U.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;em(t);let{type:e=eu,repeat:i=0,repeatDelay:s=0,repeatType:r,velocity:n=0}=t,{keyframes:a}=t,o=e||eu;o!==eu&&"number"!=typeof a[0]&&(this.mixKeyframes=B(ex,tW(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===r&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-n})),null===l.calculatedDuration&&(l.calculatedDuration=tq(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:r,mirroredGenerator:n,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:c,repeatType:u,repeatDelay:d,type:p,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),x=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let y=this.currentTime,b=i;if(c){let t=Math.min(this.currentTime,s)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===u?(i=1-i,d&&(i-=d/a)):"mirror"===u&&(b=n)),y=$(0,1,i)*a}let v=x?{done:!1,value:h[0]}:b.next(y);r&&(v.value=r(v.value));let{done:_}=v;x||null===o||(_=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&_);return w&&p!==t0&&(v.value=ep(h,this.options,m,this.speed)),f&&f(v.value),w&&this.finish(),v}then(t,e){return this.finished.then(t,e)}get duration(){return H(this.calculatedDuration)}get time(){return H(this.currentTime)}set time(t){t=W(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=H(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tH,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,U.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let eb=t=>180*t/Math.PI,ev=t=>ew(eb(Math.atan2(t[1],t[0]))),e_={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ev,rotateZ:ev,skewX:t=>eb(Math.atan(t[1])),skewY:t=>eb(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ew=t=>((t%=360)<0&&(t+=360),t),eM=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eP=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ek={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:eM,scaleY:eP,scale:t=>(eM(t)+eP(t))/2,rotateX:t=>ew(eb(Math.atan2(t[6],t[5]))),rotateY:t=>ew(eb(Math.atan2(-t[2],t[0]))),rotateZ:ev,rotate:ev,skewX:t=>eb(Math.atan(t[4])),skewY:t=>eb(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eS(t){return+!!t.includes("scale")}function eT(t,e){let i,s;if(!t||"none"===t)return eS(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=ek,s=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=e_,s=e}if(!s)return eS(e);let n=i[e],a=s[1].split(",").map(ej);return"function"==typeof n?n(a):a[n]}let eE=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eT(i,e)};function ej(t){return parseFloat(t.trim())}let eA=t=>t===J||t===tf,eC=new Set(["x","y","z"]),eD=M.filter(t=>!eC.has(t)),eN={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eT(e,"x"),y:(t,{transform:e})=>eT(e,"y")};eN.translateX=eN.x,eN.translateY=eN.y;let eR=new Set,eO=!1,eL=!1,eV=!1;function eF(){if(eL){let t=Array.from(eR).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eD.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eL=!1,eO=!1,eR.forEach(t=>t.complete(eV)),eR.clear()}function eI(){eR.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eL=!0)})}class ez{constructor(t,e,i,s,r,n=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=r,this.isAsync=n}scheduleResolve(){this.state="scheduled",this.isAsync?(eR.add(this),eO||(eO=!0,b.read(eI),b.resolveKeyframes(eF))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let r=s?.get(),n=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let s=i.readValue(e,n);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=n),s&&void 0===r&&s.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eR.delete(this)}cancel(){"scheduled"===this.state&&(eR.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eB=t=>t.startsWith("--");function e$(t){let e;return()=>(void 0===e&&(e=t()),e)}let eW=e$(()=>void 0!==window.ScrollTimeline),eH={},eU=function(t,e){let i=e$(t);return()=>eH[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eq=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,eY={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eq([0,.65,.55,1]),circOut:eq([.55,0,1,.45]),backIn:eq([.31,.01,.66,-.59]),backOut:eq([.33,1.53,.69,.99])};function eX(t){return"function"==typeof t&&"applyToOptions"in t}class eK extends eg{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:r,allowFlatten:n=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!r,this.allowFlatten=n,this.options=t,Y("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eX(t)&&eU()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:r=300,repeat:n=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let c={[e]:i};l&&(c.offset=l);let u=function t(e,i){if(e)return"function"==typeof e?eU()?tU(e,i):"ease-out":ea(e)?eq(e):Array.isArray(e)?e.map(e=>t(e,i)||eY.easeOut):eY[e]}(o,r);Array.isArray(u)&&(c.easing=u),x.value&&U.waapi++;let d={delay:s,duration:r,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:n+1,direction:"reverse"===a?"alternate":"normal"};h&&(d.pseudoElement=h);let p=t.animate(c,d);return x.value&&p.finished.finally(()=>{U.waapi--}),p}(e,i,s,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=ep(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eB(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return H(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return H(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=W(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eW())?(this.animation.timeline=t,f):e(this)}}let eG={anticipate:ei,backInOut:ee,circInOut:en};class eZ extends eK{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eG&&(t.ease=eG[t.ease])}(t),em(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:r,...n}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new ey({...n,autoplay:!1}),o=W(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let eQ=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tE.test(t)||"0"===t)&&!t.startsWith("url("));function eJ(t){return"object"==typeof t&&null!==t}function e0(t){return eJ(t)&&"offsetHeight"in t}let e1=new Set(["opacity","clipPath","filter","transform"]),e2=e$(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class e5 extends eg{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:n="loop",keyframes:a,name:o,motionValue:l,element:h,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.now();let u={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:r,repeatType:n,name:o,motionValue:l,element:h,...c},d=h?.KeyframeResolver||ez;this.keyframeResolver=new d(a,(t,e,i)=>this.onKeyframesResolved(t,e,u,!i),o,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:r,type:n,velocity:a,delay:o,isHandoff:l,onUpdate:h}=i;this.resolvedAt=A.now(),!function(t,e,i,s){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let n=t[t.length-1],a=eQ(r,e),o=eQ(n,e);return q(a===o,`You are trying to animate ${e} from "${r}" to "${n}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${n} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eX(i))&&s)}(t,r,n,a)&&((m.instantAnimations||!o)&&h?.(ep(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let c={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},u=!l&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:r,damping:n,type:a}=t;if(!e0(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return e2()&&i&&e1.has(i)&&("transform"!==i||!l)&&!o&&!s&&"mirror"!==r&&0!==n&&"inertia"!==a}(c)?new eZ({...c,element:c.motionValue.owner.current}):new ey(c);u.finished.then(()=>this.notifyFinished()).catch(f),this.pendingTimeline&&(this.stopTimeline=u.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=u}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eV=!0,eI(),eF(),eV=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e6=t=>null!==t,e3={type:"spring",stiffness:500,damping:25,restSpeed:10},e4=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e8={type:"keyframes",duration:.8},e9={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e7=(t,{keyframes:e})=>e.length>2?e8:P.has(t)?t.startsWith("scale")?e4(e[1]):e3:e9,it=(t,e,i,s={},r,n)=>a=>{let o=p(s,t)||{},l=o.delay||s.delay||0,{elapsed:h=0}=s;h-=W(l);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-h,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:n?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:r,repeat:n,repeatType:a,repeatDelay:o,from:l,elapsed:h,...c}){return!!Object.keys(c).length}(o)&&Object.assign(c,e7(t,c)),c.duration&&(c.duration=W(c.duration)),c.repeatDelay&&(c.repeatDelay=W(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let u=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(u=!0)),(m.instantAnimations||m.skipAnimations)&&(u=!0,c.duration=0,c.delay=0),c.allowFlatten=!o.type&&!o.ease,u&&!n&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let r=t.filter(e6),n=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[n]}(c.keyframes,o);if(void 0!==t)return void b.update(()=>{c.onUpdate(t),c.onComplete()})}return o.isSync?new ey(c):new e5(c)};function ie(t,e,{delay:i=0,transitionOverride:s,type:r}={}){let{transition:n=t.getDefaultTransition(),transitionEnd:a,...o}=e;s&&(n=s);let l=[],h=r&&t.animationState&&t.animationState.getState()[r];for(let e in o){let s=t.getValue(e,t.latestValues[e]??null),r=o[e];if(void 0===r||h&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(h,e))continue;let a={delay:i,...p(n||{},e)},c=s.get();if(void 0!==c&&!s.isAnimating&&!Array.isArray(r)&&r===c&&!a.velocity)continue;let u=!1;if(window.MotionHandoffAnimation){let i=t.props[I];if(i){let t=window.MotionHandoffAnimation(i,e,b);null!==t&&(a.startTime=t,u=!0)}}V(t,e),s.start(it(e,s,r,t.shouldReduceMotion&&k.has(e)?{type:!1}:a,t,u));let d=s.animation;d&&l.push(d)}return a&&Promise.all(l).then(()=>{b.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:s={},...r}=d(t,e)||{};for(let e in r={...r,...i}){var n;let i=O(n=r[e])?n[n.length-1]||0:n;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,R(i))}}(t,a)})}),l}function ii(t,e,i={}){let s=d(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(r=i.transitionOverride);let n=s?()=>Promise.all(ie(t,s,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:a,staggerDirection:o}=r;return function(t,e,i=0,s=0,r=1,n){let a=[],o=(t.variantChildren.size-1)*s,l=1===r?(t=0)=>t*s:(t=0)=>o-t*s;return Array.from(t.variantChildren).sort(is).forEach((t,s)=>{t.notify("AnimationStart",e),a.push(ii(t,e,{...n,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,n+s,a,o,i)}:()=>Promise.resolve(),{when:o}=r;if(!o)return Promise.all([n(),a(i.delay)]);{let[t,e]="beforeChildren"===o?[n,a]:[a,n];return t().then(()=>e())}}function is(t,e){return t.sortNodePosition(e)}function ir(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function ia(t){return"string"==typeof t||Array.isArray(t)}let io=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],il=["initial",...io],ih=il.length,ic=[...io].reverse(),iu=io.length;function id(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ip(){return{animate:id(!0),whileInView:id(),whileHover:id(),whileTap:id(),whileDrag:id(),whileFocus:id(),exit:id()}}class im{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ig extends im{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>ii(t,e,i)));else if("string"==typeof e)s=ii(t,e,i);else{let r="function"==typeof e?d(t,e,i.custom):e;s=Promise.all(ie(t,r,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=ip(),s=!0,r=e=>(i,s)=>{let r=d(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...s}=r;i={...i,...s,...e}}return i};function n(n){let{props:a}=t,o=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ih;t++){let s=il[t],r=e.props[s];(ia(r)||!1===r)&&(i[s]=r)}return i}(t.parent)||{},l=[],c=new Set,u={},p=1/0;for(let e=0;e<iu;e++){var f,m;let d=ic[e],g=i[d],x=void 0!==a[d]?a[d]:o[d],y=ia(x),b=d===n?g.isActive:null;!1===b&&(p=e);let v=x===o[d]&&x!==a[d]&&y;if(v&&s&&t.manuallyAnimateOnMount&&(v=!1),g.protectedKeys={...u},!g.isActive&&null===b||!x&&!g.prevProp||h(x)||"boolean"==typeof x)continue;let _=(f=g.prevProp,"string"==typeof(m=x)?m!==f:!!Array.isArray(m)&&!ir(m,f)),w=_||d===n&&g.isActive&&!v&&y||e>p&&y,M=!1,P=Array.isArray(x)?x:[x],k=P.reduce(r(d),{});!1===b&&(k={});let{prevResolvedValues:S={}}=g,T={...S,...k},E=e=>{w=!0,c.has(e)&&(M=!0,c.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in T){let e=k[t],i=S[t];if(u.hasOwnProperty(t))continue;let s=!1;(O(e)&&O(i)?ir(e,i):e===i)?void 0!==e&&c.has(t)?E(t):g.protectedKeys[t]=!0:null!=e?E(t):c.add(t)}g.prevProp=x,g.prevResolvedValues=k,g.isActive&&(u={...u,...k}),s&&t.blockInitialAnimation&&(w=!1);let j=!(v&&_)||M;w&&j&&l.push(...P.map(t=>({animation:t,options:{type:d}})))}if(c.size){let e={};if("boolean"!=typeof a.initial){let i=d(t,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let s=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=s??null}),l.push({animation:e})}let g=!!l.length;return s&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(l):Promise.resolve()}return{animateChanges:n,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let r=n(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=ip(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();h(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ix=0;class iy extends im{constructor(){super(...arguments),this.id=ix++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let ib={x:!1,y:!1};function iv(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let i_=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iw(t){return{point:{x:t.pageX,y:t.pageY}}}let iM=t=>e=>i_(e)&&t(e,iw(e));function iP(t,e,i,s){return iv(t,e,iM(i),s)}function ik({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function iS(t){return t.max-t.min}function iT(t,e,i,s=.5){t.origin=s,t.originPoint=tC(e.min,e.max,t.origin),t.scale=iS(i)/iS(e),t.translate=tC(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iE(t,e,i,s){iT(t.x,e.x,i.x,s?s.originX:void 0),iT(t.y,e.y,i.y,s?s.originY:void 0)}function ij(t,e,i){t.min=i.min+e.min,t.max=t.min+iS(e)}function iA(t,e,i){t.min=e.min-i.min,t.max=t.min+iS(e)}function iC(t,e,i){iA(t.x,e.x,i.x),iA(t.y,e.y,i.y)}let iD=()=>({translate:0,scale:1,origin:0,originPoint:0}),iN=()=>({x:iD(),y:iD()}),iR=()=>({min:0,max:0}),iO=()=>({x:iR(),y:iR()});function iL(t){return[t("x"),t("y")]}function iV(t){return void 0===t||1===t}function iF({scale:t,scaleX:e,scaleY:i}){return!iV(t)||!iV(e)||!iV(i)}function iI(t){return iF(t)||iz(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iz(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iB(t,e,i,s,r){return void 0!==r&&(t=s+r*(t-s)),s+i*(t-s)+e}function i$(t,e=0,i=1,s,r){t.min=iB(t.min,e,i,s,r),t.max=iB(t.max,e,i,s,r)}function iW(t,{x:e,y:i}){i$(t.x,e.translate,e.scale,e.originPoint),i$(t.y,i.translate,i.scale,i.originPoint)}function iH(t,e){t.min=t.min+e,t.max=t.max+e}function iU(t,e,i,s,r=.5){let n=tC(t.min,t.max,r);i$(t,e,i,n,s)}function iq(t,e){iU(t.x,e.x,e.scaleX,e.scale,e.originX),iU(t.y,e.y,e.scaleY,e.scale,e.originY)}function iY(t,e){return ik(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let iX=({current:t})=>t?t.ownerDocument.defaultView:null;function iK(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iG=(t,e)=>Math.abs(t-e);class iZ{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=i0(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iG(t.x,e.x)**2+iG(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:r}=_;this.history.push({...s,timestamp:r});let{onStart:n,onMove:a}=this.handlers;e||(n&&n(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iQ(e,this.transformPagePoint),b.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=i0("pointercancel"===t.type?this.lastMoveEventInfo:iQ(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,n),s&&s(t,n)},!i_(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let n=iQ(iw(t),this.transformPagePoint),{point:a}=n,{timestamp:o}=_;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,i0(n,this.history)),this.removeListeners=B(iP(this.contextWindow,"pointermove",this.handlePointerMove),iP(this.contextWindow,"pointerup",this.handlePointerUp),iP(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),v(this.updatePoint)}}function iQ(t,e){return e?{point:e(t.point)}:t}function iJ(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i0({point:t},e){return{point:t,delta:iJ(t,i1(e)),offset:iJ(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,r=i1(t);for(;i>=0&&(s=t[i],!(r.timestamp-s.timestamp>W(.1)));)i--;if(!s)return{x:0,y:0};let n=H(r.timestamp-s.timestamp);if(0===n)return{x:0,y:0};let a={x:(r.x-s.x)/n,y:(r.y-s.y)/n};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function i1(t){return t[t.length-1]}function i2(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i5(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function i6(t,e,i){return{min:i3(t,e),max:i3(t,i)}}function i3(t,e){return"number"==typeof t?t:t[e]||0}let i4=new WeakMap;class i8{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iO(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iZ(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iw(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:r}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(ib[t])return null;else return ib[t]=!0,()=>{ib[t]=!1};return ib.x||ib.y?null:(ib.x=ib.y=!0,()=>{ib.x=ib.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iL(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tp.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=iS(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&b.postRender(()=>r(t,e)),V(this.visualElement,"transform");let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:r,onDrag:n}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),n&&n(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iL(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:iX(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:r}=this.getProps();r&&b.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!i9(t,s,this.currentDirection))return;let r=this.getAxisMotionValue(t),n=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(n=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?tC(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?tC(i,t,s.max):Math.min(t,i)),t}(n,this.constraints[t],this.elastic[t])),r.set(n)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&iK(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:r}){return{x:i2(t.x,i,r),y:i2(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i6(t,"left","right"),y:i6(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iL(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iK(e))return!1;let s=e.current;Y(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let n=function(t,e,i){let s=iY(t,i),{scroll:r}=e;return r&&(iH(s.x,r.offset.x),iH(s.y,r.offset.y)),s}(s,r.root,this.visualElement.getTransformPagePoint()),a=(t=r.layout.layoutBox,{x:i5(t.x,n.x),y:i5(t.y,n.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=ik(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:r,dragSnapToOrigin:n,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iL(a=>{if(!i9(a,e,this.currentDirection))return;let l=o&&o[a]||{};n&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return V(this.visualElement,t),i.start(it(t,i,0,e,this.visualElement,!1))}stopAnimation(){iL(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iL(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iL(e=>{let{drag:i}=this.getProps();if(!i9(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,r=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:n}=s.layout.layoutBox[e];r.set(t[e]-tC(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iK(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iL(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=iS(t),r=iS(e);return r>s?i=ec(e.min,e.max-s,t.min):s>r&&(i=ec(t.min,t.max-r,e.min)),$(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iL(e=>{if(!i9(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:n}=this.constraints[e];i.set(tC(r,n,s[e]))})}addListeners(){if(!this.visualElement.current)return;i4.set(this.visualElement,this);let t=iP(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iK(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),b.read(e);let r=iv(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iL(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),s(),n&&n()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:n=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:r,dragElastic:n,dragMomentum:a}}}function i9(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i7 extends im{constructor(t){super(t),this.removeGroupControls=f,this.removeListeners=f,this.controls=new i8(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||f}unmount(){this.removeGroupControls(),this.removeListeners()}}let st=t=>(e,i)=>{t&&b.postRender(()=>t(e,i))};class se extends im{constructor(){super(...arguments),this.removePointerDownListener=f}onPointerDown(t){this.session=new iZ(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iX(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:st(t),onStart:st(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&b.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=iP(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:si}=y(queueMicrotask,!1),ss=(0,l.createContext)(null);function sr(t=!0){let e=(0,l.useContext)(ss);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:r}=e,n=(0,l.useId)();(0,l.useEffect)(()=>{if(t)return r(n)},[t]);let a=(0,l.useCallback)(()=>t&&s&&s(n),[n,s,t]);return!i&&s?[!1,a]:[!0]}let sn=(0,l.createContext)({}),sa=(0,l.createContext)({}),so={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sl(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sh={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tf.test(t))return t;else t=parseFloat(t);let i=sl(t,e.target.x),s=sl(t,e.target.y);return`${i}% ${s}%`}},sc={};class su extends l.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=t;for(let t in sp)sc[t]=sp[t],K(t)&&(sc[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),so.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:r}=this.props,{projection:n}=i;return n&&(n.isPresent=r,s||t.layoutDependency!==e||void 0===e||t.isPresent!==r?n.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?n.promote():n.relegate()||b.postRender(()=>{let t=n.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),si.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sd(t){let[e,i]=sr(),s=(0,l.useContext)(sn);return(0,o.jsx)(su,{...t,layoutGroup:s,switchLayoutGroup:(0,l.useContext)(sa),isPresent:e,safeToRemove:i})}let sp={borderRadius:{...sh,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sh,borderTopRightRadius:sh,borderBottomLeftRadius:sh,borderBottomRightRadius:sh,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tE.parse(t);if(s.length>5)return t;let r=tE.createTransformer(t),n=+("number"!=typeof s[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;s[0+n]/=a,s[1+n]/=o;let l=tC(a,o,.5);return"number"==typeof s[2+n]&&(s[2+n]/=l),"number"==typeof s[3+n]&&(s[3+n]/=l),r(s)}}};function sf(t){return eJ(t)&&"ownerSVGElement"in t}let sm=(t,e)=>t.depth-e.depth;class sg{constructor(){this.children=[],this.isDirty=!1}add(t){S(this.children,t),this.isDirty=!0}remove(t){T(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sm),this.isDirty=!1,this.children.forEach(t)}}function sx(t){return L(t)?t.get():t}let sy=["TopLeft","TopRight","BottomLeft","BottomRight"],sb=sy.length,sv=t=>"string"==typeof t?parseFloat(t):t,s_=t=>"number"==typeof t||tf.test(t);function sw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sM=sk(0,.5,er),sP=sk(.5,.95,f);function sk(t,e,i){return s=>s<t?0:s>e?1:i(ec(t,e,s))}function sS(t,e){t.min=e.min,t.max=e.max}function sT(t,e){sS(t.x,e.x),sS(t.y,e.y)}function sE(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sj(t,e,i,s,r){return t-=e,t=s+1/i*(t-s),void 0!==r&&(t=s+1/r*(t-s)),t}function sA(t,e,[i,s,r],n,a){!function(t,e=0,i=1,s=.5,r,n=t,a=t){if(tp.test(e)&&(e=parseFloat(e),e=tC(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=tC(n.min,n.max,s);t===n&&(o-=e),t.min=sj(t.min,e,i,o,r),t.max=sj(t.max,e,i,o,r)}(t,e[i],e[s],e[r],e.scale,n,a)}let sC=["x","scaleX","originX"],sD=["y","scaleY","originY"];function sN(t,e,i,s){sA(t.x,e,sC,i?i.x:void 0,s?s.x:void 0),sA(t.y,e,sD,i?i.y:void 0,s?s.y:void 0)}function sR(t){return 0===t.translate&&1===t.scale}function sO(t){return sR(t.x)&&sR(t.y)}function sL(t,e){return t.min===e.min&&t.max===e.max}function sV(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sF(t,e){return sV(t.x,e.x)&&sV(t.y,e.y)}function sI(t){return iS(t.x)/iS(t.y)}function sz(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sB{constructor(){this.members=[]}add(t){S(this.members,t),t.scheduleRender()}remove(t){if(T(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let s$={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sW=["","X","Y","Z"],sH={visibility:"hidden"},sU=0;function sq(t,e,i,s){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sY({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=sU++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,x.value&&(s$.nodes=s$.calculatedTargetDeltas=s$.calculatedProjections=0),this.nodes.forEach(sG),this.nodes.forEach(s5),this.nodes.forEach(s6),this.nodes.forEach(sZ),x.addProjectionMetrics&&x.addProjectionMetrics(s$)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sg)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new E),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=sf(e)&&!(sf(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:s,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=A.now(),s=({timestamp:r})=>{let n=r-i;n>=250&&(v(s),t(n-e))};return b.setup(s,!0),()=>v(s)}(s,250),so.hasAnimatedSinceResize&&(so.hasAnimatedSinceResize=!1,this.nodes.forEach(s2))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||rt,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=r.getProps(),l=!this.targetLayout||!sF(this.targetLayout,s),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...p(n,"layout"),onPlay:a,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||s2(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),v(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(s3),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[I];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",b,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sJ);return}this.isUpdating||this.nodes.forEach(s0),this.isUpdating=!1,this.nodes.forEach(s1),this.nodes.forEach(sX),this.nodes.forEach(sK),this.clearAllSnapshots();let t=A.now();_.delta=$(0,1e3/60,t-_.timestamp),_.timestamp=t,_.isProcessing=!0,w.update.process(_),w.preRender.process(_),w.render.process(_),_.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,si.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sQ),this.sharedNodes.forEach(s4)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,b.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){b.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iS(this.snapshot.measuredBox.x)||iS(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iO(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sO(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,n=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||iI(this.latestValues)||n)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),rs((e=s).x),rs(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iO();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rn))){let{scroll:t}=this.root;t&&(iH(e.x,t.offset.x),iH(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iO();if(sT(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:r,options:n}=s;s!==this.root&&r&&n.layoutScroll&&(r.wasRoot&&sT(e,t),iH(e.x,r.offset.x),iH(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=iO();sT(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iq(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iI(s.latestValues)&&iq(i,s.latestValues)}return iI(this.latestValues)&&iq(i,this.latestValues),i}removeTransform(t){let e=iO();sT(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iI(i.latestValues))continue;iF(i.latestValues)&&i.updateSnapshot();let s=iO();sT(s,i.measurePageBox()),sN(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iI(this.latestValues)&&sN(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==_.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:r}=this.options;if(this.layout&&(s||r)){if(this.resolvedRelativeTargetAt=_.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iO(),this.relativeTargetOrigin=iO(),iC(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iO(),this.targetWithTransforms=iO()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var n,a,o;this.forceRelativeParentToResolveTarget(),n=this.target,a=this.relativeTarget,o=this.relativeParent.target,ij(n.x,a.x,o.x),ij(n.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sT(this.target,this.layout.layoutBox),iW(this.target,this.targetDelta)):sT(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iO(),this.relativeTargetOrigin=iO(),iC(this.relativeTargetOrigin,this.target,t.target),sT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}x.value&&s$.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iF(this.parent.latestValues)||iz(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===_.timestamp&&(i=!1),i)return;let{layout:s,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||r))return;sT(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let r,n,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){n=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iq(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(e.x*=n.x.scale,e.y*=n.y.scale,iW(t,n)),s&&iI(r.latestValues)&&iq(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iO());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sE(this.prevProjectionDelta.x,this.projectionDelta.x),sE(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iE(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===n&&this.treeScale.y===a&&sz(this.projectionDelta.x,this.prevProjectionDelta.x)&&sz(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),x.value&&s$.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iN(),this.projectionDelta=iN(),this.projectionDeltaWithTransform=iN()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,r=s?s.latestValues:{},n={...this.latestValues},a=iN();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=iO(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),c=!h||h.members.length<=1,u=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(s7));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(s8(a.x,t.x,s),s8(a.y,t.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,d,p,f,m,g;iC(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,m=o,g=s,s9(p.x,f.x,m.x,g),s9(p.y,f.y,m.y,g),i&&(h=this.relativeTarget,d=i,sL(h.x,d.x)&&sL(h.y,d.y))&&(this.isProjectionDirty=!1),i||(i=iO()),sT(i,this.relativeTarget)}l&&(this.animationValues=n,function(t,e,i,s,r,n){r?(t.opacity=tC(0,i.opacity??1,sM(s)),t.opacityExit=tC(e.opacity??1,0,sP(s))):n&&(t.opacity=tC(e.opacity??1,i.opacity??1,s));for(let r=0;r<sb;r++){let n=`border${sy[r]}Radius`,a=sw(e,n),o=sw(i,n);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||s_(a)===s_(o)?(t[n]=Math.max(tC(sv(a),sv(o),s),0),(tp.test(o)||tp.test(a))&&(t[n]+="%")):t[n]=o)}(e.rotate||i.rotate)&&(t.rotate=tC(e.rotate||0,i.rotate||0,s))}(n,r,this.latestValues,s,u,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(v(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=b.update(()=>{so.hasAnimatedSinceResize=!0,U.layout++,this.motionValue||(this.motionValue=R(0)),this.currentAnimation=function(t,e,i){let s=L(t)?t:R(t);return s.start(it("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{U.layout--},onComplete:()=>{U.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:r}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&rr(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iO();let e=iS(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iS(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sT(e,i),iq(e,r),iE(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sB),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&sq("z",t,s,this.animationValues);for(let e=0;e<sW.length;e++)sq(`rotate${sW[e]}`,t,s,this.animationValues),sq(`skew${sW[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return sH;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=sx(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sx(t?.pointerEvents)||""),this.hasProjected&&!iI(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",r=t.x.translate/e.x,n=t.y.translate/e.y,a=i?.z||0;if((r||n||a)&&(s=`translate3d(${r}px, ${n}px, ${a}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:n,skewX:a,skewY:o}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),r&&(s+=`rotateX(${r}deg) `),n&&(s+=`rotateY(${n}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:n,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*n.origin}% ${100*a.origin}% 0`,s.animationValues?e.opacity=s===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=s===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,sc){if(void 0===r[t])continue;let{correct:i,applyTo:n,isCSSVariable:a}=sc[t],o="none"===e.transform?r[t]:i(r[t],s);if(n){let t=n.length;for(let i=0;i<t;i++)e[n[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=s===this?sx(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(sJ),this.root.sharedNodes.clear()}}}function sX(t){t.updateLayout()}function sK(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:r}=t.options,n=e.source!==t.layout.source;"size"===r?iL(t=>{let s=n?e.measuredBox[t]:e.layoutBox[t],r=iS(s);s.min=i[t].min,s.max=s.min+r}):rr(r,e.layoutBox,i)&&iL(s=>{let r=n?e.measuredBox[s]:e.layoutBox[s],a=iS(i[s]);r.max=r.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+a)});let a=iN();iE(a,i,e.layoutBox);let o=iN();n?iE(o,t.applyTransform(s,!0),e.measuredBox):iE(o,i,e.layoutBox);let l=!sO(a),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:n}=s;if(r&&n){let a=iO();iC(a,e.layoutBox,r.layoutBox);let o=iO();iC(o,i,n.layoutBox),sF(a,o)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sG(t){x.value&&s$.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sZ(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sQ(t){t.clearSnapshot()}function sJ(t){t.clearMeasurements()}function s0(t){t.isLayoutDirty=!1}function s1(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function s2(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function s5(t){t.resolveTargetDelta()}function s6(t){t.calcProjection()}function s3(t){t.resetSkewAndRotation()}function s4(t){t.removeLeadSnapshot()}function s8(t,e,i){t.translate=tC(e.translate,0,i),t.scale=tC(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function s9(t,e,i,s){t.min=tC(e.min,i.min,s),t.max=tC(e.max,i.max,s)}function s7(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let rt={duration:.45,ease:[.4,0,.1,1]},re=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ri=re("applewebkit/")&&!re("chrome/")?Math.round:f;function rs(t){t.min=ri(t.min),t.max=ri(t.max)}function rr(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sI(e)-sI(i)))}function rn(t){return t!==t.root&&t.scroll?.wasRoot}let ra=sY({attachResizeListener:(t,e)=>iv(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ro={current:void 0},rl=sY({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ro.current){let t=new ra({});t.mount(window),t.setOptions({layoutScroll:!0}),ro.current=t}return ro.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function rh(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function rc(t){return!("touch"===t.pointerType||ib.x||ib.y)}function ru(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=s["onHover"+i];r&&b.postRender(()=>r(e,iw(e)))}class rd extends im{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,r,n]=rh(t,i),a=t=>{if(!rc(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let n=t=>{rc(t)&&(s(t),i.removeEventListener("pointerleave",n))};i.addEventListener("pointerleave",n,r)};return s.forEach(t=>{t.addEventListener("pointerenter",a,r)}),n}(t,(t,e)=>(ru(this.node,e,"Start"),t=>ru(this.node,t,"End"))))}unmount(){}}class rp extends im{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=B(iv(this.node.current,"focus",()=>this.onFocus()),iv(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rf=(t,e)=>!!e&&(t===e||rf(t,e.parentElement)),rm=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rg=new WeakSet;function rx(t){return e=>{"Enter"===e.key&&t(e)}}function ry(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rb=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=rx(()=>{if(rg.has(i))return;ry(i,"down");let t=rx(()=>{ry(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ry(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function rv(t){return i_(t)&&!(ib.x||ib.y)}function r_(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=s["onTap"+("End"===i?"":i)];r&&b.postRender(()=>r(e,iw(e)))}class rw extends im{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,r,n]=rh(t,i),a=t=>{let s=t.currentTarget;if(!rv(t))return;rg.add(s);let n=e(s,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rg.has(s)&&rg.delete(s),rv(t)&&"function"==typeof n&&n(t,{success:e})},o=t=>{a(t,s===window||s===document||i.useGlobalTarget||rf(s,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return s.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,r),e0(t))&&(t.addEventListener("focus",t=>rb(t,r)),rm.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),n}(t,(t,e)=>(r_(this.node,e,"Start"),(t,{success:e})=>r_(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rM=new WeakMap,rP=new WeakMap,rk=t=>{let e=rM.get(t.target);e&&e(t)},rS=t=>{t.forEach(rk)},rT={some:0,all:1};class rE extends im{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:r}=t,n={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:rT[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;rP.has(i)||rP.set(i,{});let s=rP.get(i),r=JSON.stringify(e);return s[r]||(s[r]=new IntersectionObserver(rS,{root:t,...e})),s[r]}(e);return rM.set(t,i),s.observe(t),()=>{rM.delete(t),s.unobserve(t)}}(this.node.current,n,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),n=e?i:s;n&&n(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rj=(0,l.createContext)({strict:!1}),rA=(0,l.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),rC=(0,l.createContext)({});function rD(t){return h(t.animate)||il.some(e=>ia(t[e]))}function rN(t){return!!(rD(t)||t.variants)}function rR(t){return Array.isArray(t)?t.join(" "):t}let rO="undefined"!=typeof window,rL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rV={};for(let t in rL)rV[t]={isEnabled:e=>rL[t].some(t=>!!e[t])};let rF=Symbol.for("motionComponentSymbol"),rI=rO?l.useLayoutEffect:l.useEffect;function rz(t,{layout:e,layoutId:i}){return P.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sc[t]||"opacity"===t)}let rB=(t,e)=>e&&"number"==typeof t?e.transform(t):t,r$={...J,transform:Math.round},rW={borderWidth:tf,borderTopWidth:tf,borderRightWidth:tf,borderBottomWidth:tf,borderLeftWidth:tf,borderRadius:tf,radius:tf,borderTopLeftRadius:tf,borderTopRightRadius:tf,borderBottomRightRadius:tf,borderBottomLeftRadius:tf,width:tf,maxWidth:tf,height:tf,maxHeight:tf,top:tf,right:tf,bottom:tf,left:tf,padding:tf,paddingTop:tf,paddingRight:tf,paddingBottom:tf,paddingLeft:tf,margin:tf,marginTop:tf,marginRight:tf,marginBottom:tf,marginLeft:tf,backgroundPositionX:tf,backgroundPositionY:tf,rotate:td,rotateX:td,rotateY:td,rotateZ:td,scale:te,scaleX:te,scaleY:te,scaleZ:te,skew:td,skewX:td,skewY:td,distance:tf,translateX:tf,translateY:tf,translateZ:tf,x:tf,y:tf,z:tf,perspective:tf,transformPerspective:tf,opacity:tt,originX:tx,originY:tx,originZ:tf,zIndex:r$,fillOpacity:tt,strokeOpacity:tt,numOctaves:r$},rH={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rU=M.length;function rq(t,e,i){let{style:s,vars:r,transformOrigin:n}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(P.has(t)){a=!0;continue}if(K(t)){r[t]=i;continue}{let e=rB(i,rW[t]);t.startsWith("origin")?(o=!0,n[t]=e):s[t]=e}}if(!e.transform&&(a||i?s.transform=function(t,e,i){let s="",r=!0;for(let n=0;n<rU;n++){let a=M[n],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=rB(o,rW[a]);if(!l){r=!1;let e=rH[a]||a;s+=`${e}(${t}) `}i&&(e[a]=t)}}return s=s.trim(),i?s=i(e,r?"":s):r&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=n;s.transformOrigin=`${t} ${e} ${i}`}}let rY=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rX(t,e,i){for(let s in e)L(e[s])||rz(s,i)||(t[s]=e[s])}let rK={offset:"stroke-dashoffset",array:"stroke-dasharray"},rG={offset:"strokeDashoffset",array:"strokeDasharray"};function rZ(t,{attrX:e,attrY:i,attrScale:s,pathLength:r,pathSpacing:n=1,pathOffset:a=0,...o},l,h,c){if(rq(t,o,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:u,style:d}=t;u.transform&&(d.transform=u.transform,delete u.transform),(d.transform||u.transformOrigin)&&(d.transformOrigin=u.transformOrigin??"50% 50%",delete u.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete u.transformBox),void 0!==e&&(u.x=e),void 0!==i&&(u.y=i),void 0!==s&&(u.scale=s),void 0!==r&&function(t,e,i=1,s=0,r=!0){t.pathLength=1;let n=r?rK:rG;t[n.offset]=tf.transform(-s);let a=tf.transform(e),o=tf.transform(i);t[n.array]=`${a} ${o}`}(u,r,n,a,!1)}let rQ=()=>({...rY(),attrs:{}}),rJ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),r0=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r1(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||r0.has(t)}let r2=t=>!r1(t);try{!function(t){t&&(r2=e=>e.startsWith("on")?!r1(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r5=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r6(t){if("string"!=typeof t||t.includes("-"));else if(r5.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}function r3(t){let e=(0,l.useRef)(null);return null===e.current&&(e.current=t()),e.current}let r4=t=>(e,i)=>{let s=(0,l.useContext)(rC),r=(0,l.useContext)(ss),n=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,r){return{latestValues:function(t,e,i,s){let r={},n=s(t,{});for(let t in n)r[t]=sx(n[t]);let{initial:a,animate:o}=t,l=rD(t),c=rN(t);e&&c&&!l&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===o&&(o=e.animate));let d=!!i&&!1===i.initial,p=(d=d||!1===a)?o:a;if(p&&"boolean"!=typeof p&&!h(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=u(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,s,r,t),renderState:e()}})(t,e,s,r);return i?n():r3(n)};function r8(t,e,i){let{style:s}=t,r={};for(let n in s)(L(s[n])||e.style&&L(e.style[n])||rz(n,t)||i?.getValue(n)?.liveStyle!==void 0)&&(r[n]=s[n]);return r}let r9={useVisualState:r4({scrapeMotionValuesFromProps:r8,createRenderState:rY})};function r7(t,e,i){let s=r8(t,e,i);for(let i in t)(L(t[i])||L(e[i]))&&(s[-1!==M.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let nt={useVisualState:r4({scrapeMotionValuesFromProps:r7,createRenderState:rQ})},ne=t=>e=>e.test(t),ni=[J,tf,tp,td,tg,tm,{test:t=>"auto"===t,parse:t=>t}],ns=t=>ni.find(ne(t)),nr=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),nn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,na=t=>/^0[^.\s]+$/u.test(t),no=new Set(["brightness","contrast","saturate","opacity"]);function nl(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(ts)||[];if(!s)return t;let r=i.replace(s,""),n=+!!no.has(e);return s!==i&&(n*=100),e+"("+n+r+")"}let nh=/\b([a-z-]*)\(.*?\)/gu,nc={...tE,getAnimatableNone:t=>{let e=t.match(nh);return e?e.map(nl).join(" "):t}},nu={...rW,color:tb,backgroundColor:tb,outlineColor:tb,fill:tb,stroke:tb,borderColor:tb,borderTopColor:tb,borderRightColor:tb,borderBottomColor:tb,borderLeftColor:tb,filter:nc,WebkitFilter:nc},nd=t=>nu[t];function np(t,e){let i=nd(t);return i!==nc&&(i=tE),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let nf=new Set(["auto","none","0"]);class nm extends ez{constructor(t,e,i,s,r){super(t,e,i,s,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&Z(s=s.trim())){let r=function t(e,i,s=1){Y(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,n]=function(t){let e=nn.exec(t);if(!e)return[,];let[,i,s,r]=e;return[`--${i??s}`,r]}(e);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let t=a.trim();return nr(t)?parseFloat(t):t}return Z(n)?t(n,i,s+1):n}(s,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!k.has(i)||2!==t.length)return;let[s,r]=t,n=ns(s),a=ns(r);if(n!==a)if(eA(n)&&eA(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eN[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||na(s)))&&i.push(e)}i.length&&function(t,e,i){let s,r=0;for(;r<t.length&&!s;){let e=t[r];"string"==typeof e&&!nf.has(e)&&tP(e).values.length&&(s=t[r]),r++}if(s&&i)for(let r of e)t[r]=np(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eN[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let r=i.length-1,n=i[r];i[r]=eN[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==n&&void 0===this.finalKeyframe&&(this.finalKeyframe=n),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let ng=[...ni,tb,tE],nx=t=>ng.find(ne(t)),ny={current:null},nb={current:!1},nv=new WeakMap,n_=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nw{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:r,visualState:n},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ez,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,b.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=n;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=rD(e),this.isVariantNode=rN(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==o[t]&&L(e)&&e.set(o[t],!1)}}mount(t){this.current=t,nv.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nb.current||function(){if(nb.current=!0,rO)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ny.current=t.matches;t.addListener(e),e()}else ny.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ny.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),v(this.notifyUpdate),v(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=P.has(t);s&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&b.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),n(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rV){let e=rV[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iO()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<n_.length;e++){let i=n_[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let r=e[s],n=i[s];if(L(r))t.addValue(s,r);else if(L(n))t.addValue(s,R(r,{owner:t}));else if(n!==r)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(s);t.addValue(s,R(void 0!==e?e:r,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=R(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(nr(i)||na(i))?i=parseFloat(i):!nx(i)&&tE.test(e)&&(i=np(t,e)),this.setBaseTarget(t,L(i)?i.get():i)),L(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=u(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||L(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new E),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nM extends nw{constructor(){super(...arguments),this.KeyframeResolver=nm}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;L(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function nP(t,{style:e,vars:i},s,r){for(let n in Object.assign(t.style,e,r&&r.getProjectionStyles(s)),i)t.style.setProperty(n,i[n])}class nk extends nM{constructor(){super(...arguments),this.type="html",this.renderInstance=nP}readValueFromInstance(t,e){if(P.has(e))return this.projection?.isProjecting?eS(e):eE(t,e);{let i=window.getComputedStyle(t),s=(K(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iY(t,e)}build(t,e,i){rq(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r8(t,e,i)}}let nS=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nT extends nM{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iO}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(P.has(e)){let t=nd(e);return t&&t.default||0}return e=nS.has(e)?e:F(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r7(t,e,i)}build(t,e,i){rZ(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in nP(t,e,void 0,s),e.attrs)t.setAttribute(nS.has(i)?i:F(i),e.attrs[i])}mount(t){this.isSVGTag=rJ(t.tagName),super.mount(t)}}let nE=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((n={animation:{Feature:ig},exit:{Feature:iy},inView:{Feature:rE},tap:{Feature:rw},focus:{Feature:rp},hover:{Feature:rd},pan:{Feature:se},drag:{Feature:i7,ProjectionNode:rl,MeasureLayout:sd},layout:{ProjectionNode:rl,MeasureLayout:sd}},a=(t,e)=>r6(t)?new nT(e):new nk(e,{allowProjection:t!==l.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:r}){function n(t,n){var a,h,c;let u,d={...(0,l.useContext)(rA),...t,layoutId:function({layoutId:t}){let e=(0,l.useContext)(sn).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:p}=d,f=function(t){let{initial:e,animate:i}=function(t,e){if(rD(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ia(e)?e:void 0,animate:ia(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,l.useContext)(rC));return(0,l.useMemo)(()=>({initial:e,animate:i}),[rR(e),rR(i)])}(t),m=s(t,p);if(!p&&rO){h=0,c=0,(0,l.useContext)(rj).strict;let t=function(t){let{drag:e,layout:i}=rV;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(d);u=t.MeasureLayout,f.visualElement=function(t,e,i,s,r){let{visualElement:n}=(0,l.useContext)(rC),a=(0,l.useContext)(rj),o=(0,l.useContext)(ss),h=(0,l.useContext)(rA).reducedMotion,c=(0,l.useRef)(null);s=s||a.renderer,!c.current&&s&&(c.current=s(t,{visualState:e,parent:n,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:h}));let u=c.current,d=(0,l.useContext)(sa);u&&!u.projection&&r&&("html"===u.type||"svg"===u.type)&&function(t,e,i,s){let{layoutId:r,layout:n,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:c}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:n,alwaysMeasureLayout:!!a||o&&iK(o),visualElement:t,animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,crossfade:c,layoutScroll:l,layoutRoot:h})}(c.current,i,r,d);let p=(0,l.useRef)(!1);(0,l.useInsertionEffect)(()=>{u&&p.current&&u.update(i,o)});let f=i[I],m=(0,l.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return rI(()=>{u&&(p.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),si.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,l.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),m.current=!1))}),u}(r,m,d,e,t.ProjectionNode)}return(0,o.jsxs)(rC.Provider,{value:f,children:[u&&f.visualElement?(0,o.jsx)(u,{visualElement:f.visualElement,...d}):null,i(r,t,(a=f.visualElement,(0,l.useCallback)(t=>{t&&m.onMount&&m.onMount(t),a&&(t?a.mount(t):a.unmount()),n&&("function"==typeof n?n(t):iK(n)&&(n.current=t))},[a])),m,p,f.visualElement)]})}t&&function(t){for(let e in t)rV[e]={...rV[e],...t[e]}}(t),n.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let a=(0,l.forwardRef)(n);return a[rF]=r,a}({...r6(t)?nt:r9,preloadedFeatures:n,useRender:function(t=!1){return(e,i,s,{latestValues:r},n)=>{let a=(r6(e)?function(t,e,i,s){let r=(0,l.useMemo)(()=>{let i=rQ();return rZ(i,e,rJ(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rX(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return rX(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,l.useMemo)(()=>{let i=rY();return rq(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,r,n,e),o=function(t,e,i){let s={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r2(r)||!0===i&&r1(r)||!e&&!r1(r)||t.draggable&&r.startsWith("onDrag"))&&(s[r]=t[r]);return s}(i,"string"==typeof e,t),h=e!==l.Fragment?{...o,...a,ref:s}:{},{children:c}=i,u=(0,l.useMemo)(()=>L(c)?c.get():c,[c]);return(0,l.createElement)(e,{...h,children:u})}}(e),createVisualElement:a,Component:t})})),nj=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),nA=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),nC=t=>{let e=nA(t);return e.charAt(0).toUpperCase()+e.slice(1)},nD=(...t)=>t.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim(),nN=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var nR={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let nO=(0,l.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:r="",children:n,iconNode:a,...o},h)=>(0,l.createElement)("svg",{ref:h,...nR,width:e,height:e,stroke:t,strokeWidth:s?24*Number(i)/Number(e):i,className:nD("lucide",r),...!n&&!nN(o)&&{"aria-hidden":"true"},...o},[...a.map(([t,e])=>(0,l.createElement)(t,e)),...Array.isArray(n)?n:[n]])),nL=(t,e)=>{let i=(0,l.forwardRef)(({className:i,...s},r)=>(0,l.createElement)(nO,{ref:r,iconNode:e,className:nD(`lucide-${nj(nC(t))}`,`lucide-${t}`,i),...s}));return i.displayName=nC(t),i},nV=nL("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),nF=nL("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),nI=nL("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),nz=nL("gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]),nB=nL("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);class n${constructor(){this.currentLotteryType="power655"}static getInstance(){return n$.instance||(n$.instance=new n$),n$.instance}getLotteryConfigs(){return[{type:"power655",name:"Power 6/55",description:"Pick 6 numbers from 1-55 + 1 power number",maxNumber:55,numbersCount:6,hasPowerNumber:!0,icon:"⚡",color:"from-blue-500 to-indigo-600"},{type:"mega645",name:"Mega 6/45",description:"Pick 6 numbers from 1-45",maxNumber:45,numbersCount:6,hasPowerNumber:!1,icon:"\uD83D\uDC8E",color:"from-purple-500 to-pink-600"}]}getCurrentConfig(){let t=this.getLotteryConfigs();return t.find(t=>t.type===this.currentLotteryType)||t[0]}setCurrentLotteryType(t){this.currentLotteryType=t}getCurrentLotteryType(){return this.currentLotteryType}getPower655Rules(){return["Select 6 different numbers from 1 to 55","Select 1 power number from 1 to 55","No duplicate numbers allowed in main selection","Power number can be same as main numbers","Draws held twice weekly (Tuesday & Friday)","Minimum jackpot: 12 billion VND"]}getMega645Rules(){return["Select 6 different numbers from 1 to 45","No power number required","No duplicate numbers allowed","Draws held twice weekly (Wednesday & Saturday)","Minimum jackpot: 15 billion VND","Better odds than Power 6/55"]}getRulesForCurrentLottery(){return"power655"===this.currentLotteryType?this.getPower655Rules():this.getMega645Rules()}getOdds(t){return"power655"===t?{jackpot:"1 in 139,838,160",match5:"1 in 2,542,512",match4:"1 in 47,415",match3:"1 in 1,235"}:{jackpot:"1 in 8,145,060",match5:"1 in 34,808",match4:"1 in 733",match3:"1 in 45"}}getPrizeStructure(t){return"power655"===t?[{level:"Jackpot",condition:"6 numbers + Power",prize:"Jackpot (min 12B VND)"},{level:"Prize 1",condition:"6 numbers",prize:"40M - 60M VND"},{level:"Prize 2",condition:"5 numbers + Power",prize:"10M - 20M VND"},{level:"Prize 3",condition:"5 numbers",prize:"500K - 1M VND"},{level:"Prize 4",condition:"4 numbers + Power",prize:"200K - 400K VND"},{level:"Prize 5",condition:"4 numbers",prize:"50K - 100K VND"},{level:"Prize 6",condition:"3 numbers + Power",prize:"20K - 50K VND"}]:[{level:"Jackpot",condition:"6 numbers",prize:"Jackpot (min 15B VND)"},{level:"Prize 1",condition:"5 numbers",prize:"10M - 30M VND"},{level:"Prize 2",condition:"4 numbers",prize:"300K - 800K VND"},{level:"Prize 3",condition:"3 numbers",prize:"30K - 80K VND"}]}}function nW({children:t,className:e="",hover:i=!0,gradient:s=!1,delay:r=0}){let n=`
    rounded-xl shadow-lg backdrop-blur-sm border border-white/10
    ${s?"bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-800/90 dark:to-gray-900/70":"bg-white/80 dark:bg-gray-800/80"}
    ${e}
  `;return(0,o.jsx)(nE.div,{className:n,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:r},...i?{whileHover:{scale:1.02,y:-4,transition:{duration:.2}},whileTap:{scale:.98}}:{},children:t})}function nH({children:t,onClick:e,variant:i="primary",size:s="md",disabled:r=!1,loading:n=!1,icon:a,className:l=""}){let h=`
    inline-flex items-center justify-center font-medium rounded-lg
    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
  `,c={primary:`
      bg-gradient-to-r from-blue-600 to-blue-700 text-white
      hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500
      shadow-lg shadow-blue-500/25
    `,secondary:`
      bg-gradient-to-r from-gray-600 to-gray-700 text-white
      hover:from-gray-700 hover:to-gray-800 focus:ring-gray-500
      shadow-lg shadow-gray-500/25
    `,success:`
      bg-gradient-to-r from-green-600 to-green-700 text-white
      hover:from-green-700 hover:to-green-800 focus:ring-green-500
      shadow-lg shadow-green-500/25
    `,danger:`
      bg-gradient-to-r from-red-600 to-red-700 text-white
      hover:from-red-700 hover:to-red-800 focus:ring-red-500
      shadow-lg shadow-red-500/25
    `,ghost:`
      bg-transparent text-gray-700 border border-gray-300
      hover:bg-gray-50 focus:ring-gray-500
    `},u=`
    ${h}
    ${{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[s]}
    ${c[i]}
    ${l}
  `;return(0,o.jsxs)(nE.button,{className:u,onClick:e,disabled:r||n,whileHover:{scale:r||n?1:1.02},whileTap:{scale:r||n?1:.98},transition:{duration:.1},children:[n&&(0,o.jsx)(nE.div,{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),a&&!n&&(0,o.jsx)("span",{className:"mr-2",children:a}),t]})}function nU({currentType:t,onTypeChange:e}){let[i,s]=(0,l.useState)(!1),r=n$.getInstance(),n=r.getLotteryConfigs(),a=n.find(e=>e.type===t)||n[0],h=t=>{e(t),r.setCurrentLotteryType(t)},c=t=>"power655"===t?(0,o.jsx)(nI,{size:20}):(0,o.jsx)(nz,{size:20});return(0,o.jsx)(nW,{className:"p-6 mb-6",gradient:!0,children:(0,o.jsxs)(nE.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,o.jsxs)("h2",{className:"text-xl font-bold text-gray-800 mb-4 flex items-center",children:[c(t),(0,o.jsx)("span",{className:"ml-2",children:"Lottery Type Selection"})]}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:n.map(e=>(0,o.jsxs)(nE.button,{onClick:()=>h(e.type),className:`p-4 rounded-xl border-2 transition-all duration-300 text-left ${t===e.type?"border-blue-500 bg-blue-50 shadow-lg":"border-gray-200 bg-white hover:border-gray-300 hover:shadow-md"}`,whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("span",{className:"text-2xl mr-2",children:e.icon}),(0,o.jsx)("h3",{className:"font-bold text-lg",children:e.name})]}),t===e.type&&(0,o.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"})]}),(0,o.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,o.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,o.jsxs)("span",{children:["Numbers: 1-",e.maxNumber]}),(0,o.jsx)("span",{children:e.hasPowerNumber?"With Power":"No Power"})]})]},e.type))}),(0,o.jsxs)(nE.div,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,o.jsxs)("h4",{className:"font-semibold text-blue-800 flex items-center",children:[c(t),(0,o.jsxs)("span",{className:"ml-2",children:["Current: ",a.name]})]}),(0,o.jsxs)(nH,{onClick:()=>s(!i),variant:"ghost",size:"sm",icon:(0,o.jsx)(nB,{size:16}),children:[i?"Hide":"Show"," Rules"]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2 text-sm",children:[(0,o.jsxs)("div",{className:"text-center p-2 bg-white rounded border",children:[(0,o.jsxs)("div",{className:"font-bold text-blue-600",children:["1-",a.maxNumber]}),(0,o.jsx)("div",{className:"text-gray-600",children:"Number Range"})]}),(0,o.jsxs)("div",{className:"text-center p-2 bg-white rounded border",children:[(0,o.jsx)("div",{className:"font-bold text-blue-600",children:a.numbersCount}),(0,o.jsx)("div",{className:"text-gray-600",children:"Numbers to Pick"})]}),(0,o.jsxs)("div",{className:"text-center p-2 bg-white rounded border",children:[(0,o.jsx)("div",{className:"font-bold text-blue-600",children:a.hasPowerNumber?"Yes":"No"}),(0,o.jsx)("div",{className:"text-gray-600",children:"Power Number"})]}),(0,o.jsxs)("div",{className:"text-center p-2 bg-white rounded border",children:[(0,o.jsx)("div",{className:"font-bold text-blue-600",children:r.getOdds(t).jackpot.split(" ")[2]}),(0,o.jsx)("div",{className:"text-gray-600",children:"Jackpot Odds"})]})]})]}),i&&(0,o.jsxs)(nE.div,{className:"mt-4 p-4 bg-gray-50 rounded-xl border border-gray-200",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:[(0,o.jsxs)("h5",{className:"font-semibold text-gray-800 mb-3",children:[a.name," Rules & Information"]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h6",{className:"font-medium text-gray-700 mb-2",children:"Game Rules:"}),(0,o.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:r.getRulesForCurrentLottery().map((t,e)=>(0,o.jsxs)("li",{className:"flex items-start",children:[(0,o.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"}),t]},e))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h6",{className:"font-medium text-gray-700 mb-2",children:"Winning Odds:"}),(0,o.jsx)("div",{className:"text-sm text-gray-600 space-y-1",children:Object.entries(r.getOdds(t)).map(([t,e])=>(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsxs)("span",{className:"capitalize",children:[t,":"]}),(0,o.jsx)("span",{className:"font-medium",children:e})]},t))})]})]}),(0,o.jsxs)("div",{className:"mt-4",children:[(0,o.jsx)("h6",{className:"font-medium text-gray-700 mb-2",children:"Prize Structure:"}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full text-xs",children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{className:"bg-gray-100",children:[(0,o.jsx)("th",{className:"p-2 text-left",children:"Level"}),(0,o.jsx)("th",{className:"p-2 text-left",children:"Condition"}),(0,o.jsx)("th",{className:"p-2 text-left",children:"Prize"})]})}),(0,o.jsx)("tbody",{children:r.getPrizeStructure(t).map((t,e)=>(0,o.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,o.jsx)("td",{className:"p-2 font-medium",children:t.level}),(0,o.jsx)("td",{className:"p-2",children:t.condition}),(0,o.jsx)("td",{className:"p-2 text-green-600 font-medium",children:t.prize})]},e))})]})})]})]})]})})}let nq=nL("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),nY=nL("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);function nX({number:t,size:e="md",variant:i="primary",delay:s=0,animate:r=!0}){let n=`
    rounded-full flex items-center justify-center text-white font-bold
    border-2 shadow-lg backdrop-blur-sm
    ${{xs:"w-6 h-6 text-xs",sm:"w-8 h-8 text-sm",md:"w-10 h-10 text-sm",lg:"w-12 h-12 text-base",xl:"w-16 h-16 text-lg"}[e]}
    ${{primary:"bg-gradient-to-br from-blue-500 to-blue-600 border-blue-400 shadow-blue-500/25",secondary:"bg-gradient-to-br from-gray-500 to-gray-600 border-gray-400 shadow-gray-500/25",power:"bg-gradient-to-br from-red-500 to-red-600 border-red-400 shadow-red-500/25",hot:"bg-gradient-to-br from-orange-500 to-red-500 border-orange-400 shadow-orange-500/25",cold:"bg-gradient-to-br from-cyan-500 to-blue-500 border-cyan-400 shadow-cyan-500/25",suggested:"bg-gradient-to-br from-green-500 to-emerald-600 border-green-400 shadow-green-500/25"}[i]}
  `;return(0,o.jsx)(nE.div,{className:n,...r?{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0,transition:{type:"spring",stiffness:260,damping:20,delay:s}},whileHover:{scale:1.1,transition:{duration:.2}}}:{},children:t})}function nK({data:t}){let e=t.slice(0,5),i=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),s=t=>{let e=new Date(t),s=Math.ceil(Math.abs(new Date().getTime()-e.getTime())/864e5);return 1===s?"Yesterday":s<7?`${s} days ago`:s<30?`${Math.ceil(s/7)} weeks ago`:i(t)};return e.length?(0,o.jsxs)(nW,{className:"p-6",gradient:!0,children:[(0,o.jsxs)(nE.h2,{className:"text-xl font-bold text-gray-800 mb-6 flex items-center",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},children:[(0,o.jsx)(nq,{className:"mr-2 text-blue-600",size:24}),"Latest Results"]}),(0,o.jsx)("div",{className:"space-y-4",children:e.map((t,e)=>(0,o.jsxs)(nE.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*(e+1)},className:`
              p-4 rounded-xl border-2 transition-all duration-300
              ${0===e?"border-blue-300 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg":"border-gray-200 bg-white/50 hover:bg-white/80"}
            `,children:[(0,o.jsx)("div",{className:"flex justify-between items-start mb-4",children:(0,o.jsxs)("div",{children:[(0,o.jsxs)("h3",{className:"font-semibold text-gray-800 flex items-center",children:["Draw #",t.id,0===e&&(0,o.jsx)(nE.span,{className:"ml-2 px-2 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full shadow-lg",initial:{scale:0},animate:{scale:1},transition:{delay:.5,type:"spring"},children:"Latest"})]}),(0,o.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mt-1",children:[(0,o.jsx)(nY,{size:14,className:"mr-1"}),(0,o.jsx)("span",{children:i(t.date)}),(0,o.jsx)("span",{className:"mx-2",children:"•"}),(0,o.jsx)("span",{className:"text-blue-600 font-medium",children:s(t.date)})]})]})}),(0,o.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,o.jsxs)("span",{className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,o.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),"Numbers:"]}),(0,o.jsx)("div",{className:"flex space-x-2",children:t.result.map((t,e)=>(0,o.jsx)(nX,{number:t,variant:"primary",delay:.1*(e+1),size:"sm"},e))})]}),t.powerNumber&&(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsxs)("span",{className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,o.jsx)(nI,{size:14,className:"mr-2 text-red-500"}),"Power:"]}),(0,o.jsx)(nX,{number:t.powerNumber,variant:"power",delay:.7,size:"sm"})]})]},t.id))}),(0,o.jsx)(nE.div,{className:"mt-6 pt-4 border-t border-gray-200",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:(0,o.jsxs)("p",{className:"text-xs text-gray-500 text-center",children:["Showing ",e.length," most recent draws"]})})]}):(0,o.jsxs)(nW,{className:"p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-bold text-gray-800 mb-4 flex items-center",children:[(0,o.jsx)(nq,{className:"mr-2 text-blue-600",size:24}),"Latest Results"]}),(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("div",{className:"text-gray-400 text-4xl mb-2",children:"\uD83C\uDFB1"}),(0,o.jsx)("p",{className:"text-gray-500",children:"No lottery results available"})]})]})}let nG=nL("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),nZ=nL("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);function nQ(t){return t+.5|0}let nJ=(t,e,i)=>Math.max(Math.min(t,i),e);function n0(t){return nJ(nQ(2.55*t),0,255)}function n1(t){return nJ(nQ(255*t),0,255)}function n2(t){return nJ(nQ(t/2.55)/100,0,1)}function n5(t){return nJ(nQ(100*t),0,100)}let n6={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},n3=[..."0123456789ABCDEF"],n4=t=>n3[15&t],n8=t=>n3[(240&t)>>4]+n3[15&t],n9=t=>(240&t)>>4==(15&t),n7=t=>n9(t.r)&&n9(t.g)&&n9(t.b)&&n9(t.a),at=(t,e)=>t<255?e(t):"",ae=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function ai(t,e,i){let s=e*Math.min(i,1-i),r=(e,r=(e+t/30)%12)=>i-s*Math.max(Math.min(r-3,9-r,1),-1);return[r(0),r(8),r(4)]}function as(t,e,i){let s=(s,r=(s+t/60)%6)=>i-i*e*Math.max(Math.min(r,4-r,1),0);return[s(5),s(3),s(1)]}function ar(t,e,i){let s,r=ai(t,1,.5);for(e+i>1&&(s=1/(e+i),e*=s,i*=s),s=0;s<3;s++)r[s]*=1-e-i,r[s]+=e;return r}function an(t){let e,i,s,r=t.r/255,n=t.g/255,a=t.b/255,o=Math.max(r,n,a),l=Math.min(r,n,a),h=(o+l)/2;o!==l&&(s=o-l,i=h>.5?s/(2-o-l):s/(o+l),e=60*(e=r===o?(n-a)/s+6*(n<a):n===o?(a-r)/s+2:(r-n)/s+4)+.5);return[0|e,i||0,h]}function aa(t,e,i,s){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,s)).map(n1)}function ao(t){return(t%360+360)%360}let al={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},ah={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},ac=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,au=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,ad=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function ap(t,e,i){if(t){let s=an(t);s[e]=Math.max(0,Math.min(s[e]+s[e]*i,0===e?360:1)),t.r=(s=aa(ai,s,void 0,void 0))[0],t.g=s[1],t.b=s[2]}}function af(t,e){return t?Object.assign(e||{},t):t}function am(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=n1(t[3]))):(e=af(t,{r:0,g:0,b:0,a:1})).a=n1(e.a),e}class ag{constructor(t){let e;if(t instanceof ag)return t;let i=typeof t;"object"===i?e=am(t):"string"===i&&(e=function(t){var e,i=t.length;return"#"===t[0]&&(4===i||5===i?e={r:255&17*n6[t[1]],g:255&17*n6[t[2]],b:255&17*n6[t[3]],a:5===i?17*n6[t[4]]:255}:(7===i||9===i)&&(e={r:n6[t[1]]<<4|n6[t[2]],g:n6[t[3]]<<4|n6[t[4]],b:n6[t[5]]<<4|n6[t[6]],a:9===i?n6[t[7]]<<4|n6[t[8]]:255})),e}(t)||function(t){r||((r=function(){let t,e,i,s,r,n={},a=Object.keys(ah),o=Object.keys(al);for(t=0;t<a.length;t++){for(e=0,s=r=a[t];e<o.length;e++)i=o[e],r=r.replace(i,al[i]);i=parseInt(ah[s],16),n[r]=[i>>16&255,i>>8&255,255&i]}return n}()).transparent=[0,0,0,0]);let e=r[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,i,s,r=ac.exec(t),n=255;if(r){if(r[7]!==e){let t=+r[7];n=r[8]?n0(t):nJ(255*t,0,255)}return e=+r[1],i=+r[3],s=+r[5],e=255&(r[2]?n0(e):nJ(e,0,255)),{r:e,g:i=255&(r[4]?n0(i):nJ(i,0,255)),b:s=255&(r[6]?n0(s):nJ(s,0,255)),a:n}}}(t):function(t){let e,i=ae.exec(t),s=255;if(!i)return;i[5]!==e&&(s=i[6]?n0(+i[5]):n1(+i[5]));let r=ao(+i[2]),n=i[3]/100,a=i[4]/100;return{r:(e="hwb"===i[1]?aa(ar,r,n,a):"hsv"===i[1]?aa(as,r,n,a):aa(ai,r,n,a))[0],g:e[1],b:e[2],a:s}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=af(this._rgb);return t&&(t.a=n2(t.a)),t}set rgb(t){this._rgb=am(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${n2(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;return this._valid?(e=n7(t=this._rgb)?n4:n8,t?"#"+e(t.r)+e(t.g)+e(t.b)+at(t.a,e):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=an(t),i=e[0],s=n5(e[1]),r=n5(e[2]);return t.a<255?`hsla(${i}, ${s}%, ${r}%, ${n2(t.a)})`:`hsl(${i}, ${s}%, ${r}%)`}(this._rgb):void 0}mix(t,e){if(t){let i,s=this.rgb,r=t.rgb,n=e===i?.5:e,a=2*n-1,o=s.a-r.a,l=((a*o==-1?a:(a+o)/(1+a*o))+1)/2;i=1-l,s.r=255&l*s.r+i*r.r+.5,s.g=255&l*s.g+i*r.g+.5,s.b=255&l*s.b+i*r.b+.5,s.a=n*s.a+(1-n)*r.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){let s=ad(n2(t.r)),r=ad(n2(t.g)),n=ad(n2(t.b));return{r:n1(au(s+i*(ad(n2(e.r))-s))),g:n1(au(r+i*(ad(n2(e.g))-r))),b:n1(au(n+i*(ad(n2(e.b))-n))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new ag(this.rgb)}alpha(t){return this._rgb.a=n1(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=nQ(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ap(this._rgb,2,t),this}darken(t){return ap(this._rgb,2,-t),this}saturate(t){return ap(this._rgb,1,t),this}desaturate(t){return ap(this._rgb,1,-t),this}rotate(t){var e,i;return e=this._rgb,(i=an(e))[0]=ao(i[0]+t),e.r=(i=aa(ai,i,void 0,void 0))[0],e.g=i[1],e.b=i[2],this}}function ax(){}let ay=(()=>{let t=0;return()=>t++})();function ab(t){return null==t}function av(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function a_(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function aw(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function aM(t,e){return aw(t)?t:e}function aP(t,e){return void 0===t?e:t}let ak=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,aS=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function aT(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function aE(t,e,i,s){let r,n,a;if(av(t))if(n=t.length,s)for(r=n-1;r>=0;r--)e.call(i,t[r],r);else for(r=0;r<n;r++)e.call(i,t[r],r);else if(a_(t))for(r=0,n=(a=Object.keys(t)).length;r<n;r++)e.call(i,t[a[r]],a[r])}function aj(t,e){let i,s,r,n;if(!t||!e||t.length!==e.length)return!1;for(i=0,s=t.length;i<s;++i)if(r=t[i],n=e[i],r.datasetIndex!==n.datasetIndex||r.index!==n.index)return!1;return!0}function aA(t){if(av(t))return t.map(aA);if(a_(t)){let e=Object.create(null),i=Object.keys(t),s=i.length,r=0;for(;r<s;++r)e[i[r]]=aA(t[i[r]]);return e}return t}function aC(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function aD(t,e,i,s){if(!aC(t))return;let r=e[t],n=i[t];a_(r)&&a_(n)?aN(r,n,s):e[t]=aA(n)}function aN(t,e,i){let s,r=av(e)?e:[e],n=r.length;if(!a_(t))return t;let a=(i=i||{}).merger||aD;for(let e=0;e<n;++e){if(!a_(s=r[e]))continue;let n=Object.keys(s);for(let e=0,r=n.length;e<r;++e)a(n[e],t,s,i)}return t}function aR(t,e){return aN(t,e,{merger:aO})}function aO(t,e,i){if(!aC(t))return;let s=e[t],r=i[t];a_(s)&&a_(r)?aR(s,r):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=aA(r))}let aL={"":t=>t,x:t=>t.x,y:t=>t.y};function aV(t,e){return(aL[e]||(aL[e]=function(t){let e=function(t){let e=t.split("."),i=[],s="";for(let t of e)(s+=t).endsWith("\\")?s=s.slice(0,-1)+".":(i.push(s),s="");return i}(t);return t=>{for(let i of e){if(""===i)break;t=t&&t[i]}return t}}(e)))(t)}function aF(t){return t.charAt(0).toUpperCase()+t.slice(1)}let aI=t=>void 0!==t,az=t=>"function"==typeof t,aB=(t,e)=>{if(t.size!==e.size)return!1;for(let i of t)if(!e.has(i))return!1;return!0},a$=Math.PI,aW=2*a$,aH=aW+a$,aU=Number.POSITIVE_INFINITY,aq=a$/180,aY=a$/2,aX=a$/4,aK=2*a$/3,aG=Math.log10,aZ=Math.sign;function aQ(t,e,i){return Math.abs(t-e)<i}function aJ(t){let e=Math.round(t),i=Math.pow(10,Math.floor(aG(t=aQ(t,e,t/1e3)?e:t))),s=t/i;return(s<=1?1:s<=2?2:s<=5?5:10)*i}function a0(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function a1(t,e,i){let s,r,n;for(s=0,r=t.length;s<r;s++)isNaN(n=t[s][i])||(e.min=Math.min(e.min,n),e.max=Math.max(e.max,n))}function a2(t){return a$/180*t}function a5(t){if(!aw(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function a6(t,e){let i=e.x-t.x,s=e.y-t.y,r=Math.sqrt(i*i+s*s),n=Math.atan2(s,i);return n<-.5*a$&&(n+=aW),{angle:n,distance:r}}function a3(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function a4(t,e){return(t-e+aH)%aW-a$}function a8(t){return(t%aW+aW)%aW}function a9(t,e,i,s){let r=a8(t),n=a8(e),a=a8(i),o=a8(n-r),l=a8(a-r),h=a8(r-n),c=a8(r-a);return r===n||r===a||s&&n===a||o>l&&h<c}function a7(t,e,i){return Math.max(e,Math.min(i,t))}function ot(t,e,i,s=1e-6){return t>=Math.min(e,i)-s&&t<=Math.max(e,i)+s}function oe(t,e,i){let s;i=i||(i=>t[i]<e);let r=t.length-1,n=0;for(;r-n>1;)i(s=n+r>>1)?n=s:r=s;return{lo:n,hi:r}}let oi=(t,e,i,s)=>oe(t,i,s?s=>{let r=t[s][e];return r<i||r===i&&t[s+1][e]===i}:s=>t[s][e]<i),os=(t,e,i)=>oe(t,i,s=>t[s][e]>=i),or=["push","pop","shift","splice","unshift"];function on(t,e){let i=t._chartjs;if(!i)return;let s=i.listeners,r=s.indexOf(e);-1!==r&&s.splice(r,1),s.length>0||(or.forEach(e=>{delete t[e]}),delete t._chartjs)}function oa(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let oo="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function ol(t,e){let i=[],s=!1;return function(...r){i=r,s||(s=!0,oo.call(window,()=>{s=!1,t.apply(e,i)}))}}let oh=t=>"start"===t?"left":"end"===t?"right":"center",oc=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2,ou=(t,e,i,s)=>t===(s?"left":"right")?i:"center"===t?(e+i)/2:e;function od(t,e,i){let s=e.length,r=0,n=s;if(t._sorted){let{iScale:a,vScale:o,_parsed:l}=t,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,c=a.axis,{min:u,max:d,minDefined:p,maxDefined:f}=a.getUserBounds();if(p){if(r=Math.min(oi(l,c,u).lo,i?s:oi(e,c,a.getPixelForValue(u)).lo),h){let t=l.slice(0,r+1).reverse().findIndex(t=>!ab(t[o.axis]));r-=Math.max(0,t)}r=a7(r,0,s-1)}if(f){let t=Math.max(oi(l,a.axis,d,!0).hi+1,i?0:oi(e,c,a.getPixelForValue(d),!0).hi+1);if(h){let e=l.slice(t-1).findIndex(t=>!ab(t[o.axis]));t+=Math.max(0,e)}n=a7(t,r,s)-r}else n=s-r}return{start:r,count:n}}function op(t){let{xScale:e,yScale:i,_scaleRanges:s}=t,r={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!s)return t._scaleRanges=r,!0;let n=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==i.min||s.ymax!==i.max;return Object.assign(s,r),n}let of=t=>0===t||1===t,om=(t,e,i)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*aW/i)),og=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*aW/i)+1,ox={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*aY)+1,easeOutSine:t=>Math.sin(t*aY),easeInOutSine:t=>-.5*(Math.cos(a$*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>of(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>of(t)?t:om(t,.075,.3),easeOutElastic:t=>of(t)?t:og(t,.075,.3),easeInOutElastic:t=>of(t)?t:t<.5?.5*om(2*t,.1125,.45):.5+.5*og(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-ox.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*ox.easeInBounce(2*t):.5*ox.easeOutBounce(2*t-1)+.5};function oy(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function ob(t){return oy(t)?t:new ag(t)}function ov(t){return oy(t)?t:new ag(t).saturate(.5).darken(.1).hexString()}let o_=["x","y","borderWidth","radius","tension"],ow=["color","borderColor","backgroundColor"],oM=new Map;function oP(t,e,i){return(function(t,e){let i=t+JSON.stringify(e=e||{}),s=oM.get(i);return s||(s=new Intl.NumberFormat(t,e),oM.set(i,s)),s})(e,i).format(t)}let ok={values:t=>av(t)?t:""+t,numeric(t,e,i){let s;if(0===t)return"0";let r=this.chart.options.locale,n=t;if(i.length>1){var a,o;let e,r=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(r<1e-4||r>1e15)&&(s="scientific"),a=t,Math.abs(e=(o=i).length>3?o[2].value-o[1].value:o[1].value-o[0].value)>=1&&a!==Math.floor(a)&&(e=a-Math.floor(a)),n=e}let l=aG(Math.abs(n)),h=isNaN(l)?1:Math.max(Math.min(-1*Math.floor(l),20),0),c={notation:s,minimumFractionDigits:h,maximumFractionDigits:h};return Object.assign(c,this.options.ticks.format),oP(t,r,c)},logarithmic(t,e,i){return 0===t?"0":[1,2,3,5,10,15].includes(i[e].significand||t/Math.pow(10,Math.floor(aG(t))))||e>.8*i.length?ok.numeric.call(this,t,e,i):""}};var oS={formatters:ok};let oT=Object.create(null),oE=Object.create(null);function oj(t,e){if(!e)return t;let i=e.split(".");for(let e=0,s=i.length;e<s;++e){let s=i[e];t=t[s]||(t[s]=Object.create(null))}return t}function oA(t,e,i){return"string"==typeof e?aN(oj(t,e),i):aN(oj(t,""),e)}class oC{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>ov(e.backgroundColor),this.hoverBorderColor=(t,e)=>ov(e.borderColor),this.hoverColor=(t,e)=>ov(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return oA(this,t,e)}get(t){return oj(this,t)}describe(t,e){return oA(oE,t,e)}override(t,e){return oA(oT,t,e)}route(t,e,i,s){let r=oj(this,t),n=oj(this,i),a="_"+e;Object.defineProperties(r,{[a]:{value:r[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[a],e=n[s];return a_(t)?Object.assign({},e,t):aP(t,e)},set(t){this[a]=t}}})}apply(t){t.forEach(t=>t(this))}}var oD=new oC({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:ow},numbers:{type:"number",properties:o_}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:oS.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function oN(t,e,i,s,r){let n=e[r];return n||(n=e[r]=t.measureText(r).width,i.push(r)),n>s&&(s=n),s}function oR(t,e,i){let s=t.currentDevicePixelRatio,r=0!==i?Math.max(i/2,.5):0;return Math.round((e-r)*s)/s+r}function oO(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function oL(t,e,i,s){oV(t,e,i,s,null)}function oV(t,e,i,s,r){let n,a,o,l,h,c,u,d,p=e.pointStyle,f=e.rotation,m=e.radius,g=(f||0)*aq;if(p&&"object"==typeof p&&("[object HTMLImageElement]"===(n=p.toString())||"[object HTMLCanvasElement]"===n)){t.save(),t.translate(i,s),t.rotate(g),t.drawImage(p,-p.width/2,-p.height/2,p.width,p.height),t.restore();return}if(!isNaN(m)&&!(m<=0)){switch(t.beginPath(),p){default:r?t.ellipse(i,s,r/2,m,0,0,aW):t.arc(i,s,m,0,aW),t.closePath();break;case"triangle":c=r?r/2:m,t.moveTo(i+Math.sin(g)*c,s-Math.cos(g)*m),g+=aK,t.lineTo(i+Math.sin(g)*c,s-Math.cos(g)*m),g+=aK,t.lineTo(i+Math.sin(g)*c,s-Math.cos(g)*m),t.closePath();break;case"rectRounded":h=.516*m,a=Math.cos(g+aX)*(l=m-h),u=Math.cos(g+aX)*(r?r/2-h:l),o=Math.sin(g+aX)*l,d=Math.sin(g+aX)*(r?r/2-h:l),t.arc(i-u,s-o,h,g-a$,g-aY),t.arc(i+d,s-a,h,g-aY,g),t.arc(i+u,s+o,h,g,g+aY),t.arc(i-d,s+a,h,g+aY,g+a$),t.closePath();break;case"rect":if(!f){l=Math.SQRT1_2*m,c=r?r/2:l,t.rect(i-c,s-l,2*c,2*l);break}g+=aX;case"rectRot":u=Math.cos(g)*(r?r/2:m),a=Math.cos(g)*m,o=Math.sin(g)*m,d=Math.sin(g)*(r?r/2:m),t.moveTo(i-u,s-o),t.lineTo(i+d,s-a),t.lineTo(i+u,s+o),t.lineTo(i-d,s+a),t.closePath();break;case"crossRot":g+=aX;case"cross":u=Math.cos(g)*(r?r/2:m),a=Math.cos(g)*m,o=Math.sin(g)*m,d=Math.sin(g)*(r?r/2:m),t.moveTo(i-u,s-o),t.lineTo(i+u,s+o),t.moveTo(i+d,s-a),t.lineTo(i-d,s+a);break;case"star":u=Math.cos(g)*(r?r/2:m),a=Math.cos(g)*m,o=Math.sin(g)*m,d=Math.sin(g)*(r?r/2:m),t.moveTo(i-u,s-o),t.lineTo(i+u,s+o),t.moveTo(i+d,s-a),t.lineTo(i-d,s+a),g+=aX,u=Math.cos(g)*(r?r/2:m),a=Math.cos(g)*m,o=Math.sin(g)*m,d=Math.sin(g)*(r?r/2:m),t.moveTo(i-u,s-o),t.lineTo(i+u,s+o),t.moveTo(i+d,s-a),t.lineTo(i-d,s+a);break;case"line":a=r?r/2:Math.cos(g)*m,o=Math.sin(g)*m,t.moveTo(i-a,s-o),t.lineTo(i+a,s+o);break;case"dash":t.moveTo(i,s),t.lineTo(i+Math.cos(g)*(r?r/2:m),s+Math.sin(g)*m);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function oF(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function oI(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function oz(t){t.restore()}function oB(t,e,i,s,r){if(!e)return t.lineTo(i.x,i.y);if("middle"===r){let s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else"after"===r!=!!s?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function o$(t,e,i,s){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?i.cp2x:i.cp1x,s?i.cp2y:i.cp1y,i.x,i.y)}function oW(t,e,i,s,r,n={}){let a,o,l=av(e)?e:[e],h=n.strokeWidth>0&&""!==n.strokeColor;for(t.save(),t.font=r.string,n.translation&&t.translate(n.translation[0],n.translation[1]),ab(n.rotation)||t.rotate(n.rotation),n.color&&(t.fillStyle=n.color),n.textAlign&&(t.textAlign=n.textAlign),n.textBaseline&&(t.textBaseline=n.textBaseline),a=0;a<l.length;++a)o=l[a],n.backdrop&&function(t,e){let i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}(t,n.backdrop),h&&(n.strokeColor&&(t.strokeStyle=n.strokeColor),ab(n.strokeWidth)||(t.lineWidth=n.strokeWidth),t.strokeText(o,i,s,n.maxWidth)),t.fillText(o,i,s,n.maxWidth),function(t,e,i,s,r){if(r.strikethrough||r.underline){let n=t.measureText(s),a=e-n.actualBoundingBoxLeft,o=e+n.actualBoundingBoxRight,l=i-n.actualBoundingBoxAscent,h=i+n.actualBoundingBoxDescent,c=r.strikethrough?(l+h)/2:h;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=r.decorationWidth||2,t.moveTo(a,c),t.lineTo(o,c),t.stroke()}}(t,i,s,o,n),s+=Number(r.lineHeight);t.restore()}function oH(t,e){let{x:i,y:s,w:r,h:n,radius:a}=e;t.arc(i+a.topLeft,s+a.topLeft,a.topLeft,1.5*a$,a$,!0),t.lineTo(i,s+n-a.bottomLeft),t.arc(i+a.bottomLeft,s+n-a.bottomLeft,a.bottomLeft,a$,aY,!0),t.lineTo(i+r-a.bottomRight,s+n),t.arc(i+r-a.bottomRight,s+n-a.bottomRight,a.bottomRight,aY,0,!0),t.lineTo(i+r,s+a.topRight),t.arc(i+r-a.topRight,s+a.topRight,a.topRight,0,-aY,!0),t.lineTo(i+a.topLeft,s)}let oU=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,oq=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,oY=t=>+t||0;function oX(t,e){let i={},s=a_(e),r=s?Object.keys(e):e,n=a_(t)?s?i=>aP(t[i],t[e[i]]):e=>t[e]:()=>t;for(let t of r)i[t]=oY(n(t));return i}function oK(t){return oX(t,{top:"y",right:"x",bottom:"y",left:"x"})}function oG(t){return oX(t,["topLeft","topRight","bottomLeft","bottomRight"])}function oZ(t){let e=oK(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function oQ(t,e){t=t||{},e=e||oD.font;let i=aP(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let s=aP(t.style,e.style);s&&!(""+s).match(oq)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let r={family:aP(t.family,e.family),lineHeight:function(t,e){let i=(""+t).match(oU);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}(aP(t.lineHeight,e.lineHeight),i),size:i,style:s,weight:aP(t.weight,e.weight),string:""};return r.string=!r||ab(r.size)||ab(r.family)?null:(r.style?r.style+" ":"")+(r.weight?r.weight+" ":"")+r.size+"px "+r.family,r}function oJ(t,e,i,s){let r,n,a,o=!0;for(r=0,n=t.length;r<n;++r)if(void 0!==(a=t[r])&&(void 0!==e&&"function"==typeof a&&(a=a(e),o=!1),void 0!==i&&av(a)&&(a=a[i%a.length],o=!1),void 0!==a))return s&&!o&&(s.cacheable=!1),a}function o0(t,e){return Object.assign(Object.create(t),e)}function o1(t,e=[""],i,s,r=()=>t[0]){let n=i||t;return void 0===s&&(s=lt("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:n,_fallback:s,_getTarget:r,override:i=>o1([i,...t],e,n,s)},{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,s)=>o4(i,s,()=>(function(t,e,i,s){let r;for(let n of e)if(void 0!==(r=lt(o6(n,t),i)))return o3(t,r)?o9(i,s,t,r):r})(s,e,t,i)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>le(t).includes(e),ownKeys:t=>le(t),set(t,e,i){let s=t._storage||(t._storage=r());return t[e]=s[e]=i,delete t._keys,!0}})}function o2(t,e,i,s){return new Proxy({_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:o5(t,s),setContext:e=>o2(t,e,i,s),override:r=>o2(t.override(r),e,i,s)},{deleteProperty:(e,i)=>(delete e[i],delete t[i],!0),get:(t,e,i)=>o4(t,e,()=>(function(t,e,i){let{_proxy:s,_context:r,_subProxy:n,_descriptors:a}=t,o=s[e];return az(o)&&a.isScriptable(e)&&(o=function(t,e,i,s){let{_proxy:r,_context:n,_subProxy:a,_stack:o}=i;if(o.has(t))throw Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let l=e(n,a||s);return o.delete(t),o3(t,l)&&(l=o9(r._scopes,r,t,l)),l}(e,o,t,i)),av(o)&&o.length&&(o=function(t,e,i,s){let{_proxy:r,_context:n,_subProxy:a,_descriptors:o}=i;if(void 0!==n.index&&s(t))return e[n.index%e.length];if(a_(e[0])){let i=e,s=r._scopes.filter(t=>t!==i);for(let l of(e=[],i)){let i=o9(s,r,t,l);e.push(o2(i,n,a&&a[t],o))}}return e}(e,o,t,a.isIndexable)),o3(e,o)&&(o=o2(o,r,n&&n[e],a)),o})(t,e,i)),getOwnPropertyDescriptor:(e,i)=>e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,i)=>Reflect.has(t,i),ownKeys:()=>Reflect.ownKeys(t),set:(e,i,s)=>(t[i]=s,delete e[i],!0)})}function o5(t,e={scriptable:!0,indexable:!0}){let{_scriptable:i=e.scriptable,_indexable:s=e.indexable,_allKeys:r=e.allKeys}=t;return{allKeys:r,scriptable:i,indexable:s,isScriptable:az(i)?i:()=>i,isIndexable:az(s)?s:()=>s}}let o6=(t,e)=>t?t+aF(e):e,o3=(t,e)=>a_(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function o4(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let s=i();return t[e]=s,s}let o8=(t,e)=>!0===t?e:"string"==typeof t?aV(e,t):void 0;function o9(t,e,i,s){var r;let n=e._rootScopes,a=(r=e._fallback,az(r)?r(i,s):r),o=[...t,...n],l=new Set;l.add(s);let h=o7(l,o,i,a||i,s);return null!==h&&(void 0===a||a===i||null!==(h=o7(l,o,a,h,s)))&&o1(Array.from(l),[""],n,a,()=>(function(t,e,i){let s=t._getTarget();e in s||(s[e]={});let r=s[e];return av(r)&&a_(i)?i:r||{}})(e,i,s))}function o7(t,e,i,s,r){for(;i;)i=function(t,e,i,s,r){for(let a of e){let e=o8(i,a);if(e){var n;t.add(e);let a=(n=e._fallback,az(n)?n(i,r):n);if(void 0!==a&&a!==i&&a!==s)return a}else if(!1===e&&void 0!==s&&i!==s)return null}return!1}(t,e,i,s,r);return i}function lt(t,e){for(let i of e){if(!i)continue;let e=i[t];if(void 0!==e)return e}}function le(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let i of t)for(let t of Object.keys(i).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function li(t,e,i,s){let r,n,a,{iScale:o}=t,{key:l="r"}=this._parsing,h=Array(s);for(r=0;r<s;++r)a=e[n=r+i],h[r]={r:o.parse(aV(a,l),n)};return h}let ls=Number.EPSILON||1e-14,lr=(t,e)=>e<t.length&&!t[e].skip&&t[e],ln=t=>"x"===t?"y":"x";function la(t,e,i){return Math.max(Math.min(t,i),e)}function lo(){return"undefined"!=typeof window&&"undefined"!=typeof document}function ll(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function lh(t,e,i){let s;return"string"==typeof t?(s=parseInt(t,10),-1!==t.indexOf("%")&&(s=s/100*e.parentNode[i])):s=t,s}let lc=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),lu=["top","right","bottom","left"];function ld(t,e,i){let s={};i=i?"-"+i:"";for(let r=0;r<4;r++){let n=lu[r];s[n]=parseFloat(t[e+"-"+n+i])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}let lp=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function lf(t,e){if("native"in t)return t;let{canvas:i,currentDevicePixelRatio:s}=e,r=lc(i),n="border-box"===r.boxSizing,a=ld(r,"padding"),o=ld(r,"border","width"),{x:l,y:h,box:c}=function(t,e){let i,s,r=t.touches,n=r&&r.length?r[0]:t,{offsetX:a,offsetY:o}=n,l=!1;if(lp(a,o,t.target))i=a,s=o;else{let t=e.getBoundingClientRect();i=n.clientX-t.left,s=n.clientY-t.top,l=!0}return{x:i,y:s,box:l}}(t,i),u=a.left+(c&&o.left),d=a.top+(c&&o.top),{width:p,height:f}=e;return n&&(p-=a.width+o.width,f-=a.height+o.height),{x:Math.round((l-u)/p*i.width/s),y:Math.round((h-d)/f*i.height/s)}}let lm=t=>Math.round(10*t)/10;function lg(t,e,i){let s=e||1,r=Math.floor(t.height*s),n=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let a=t.canvas;return a.style&&(i||!a.style.height&&!a.style.width)&&(a.style.height=`${t.height}px`,a.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==s||a.height!==r||a.width!==n)&&(t.currentDevicePixelRatio=s,a.height=r,a.width=n,t.ctx.setTransform(s,0,0,s,0,0),!0)}let lx=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};lo()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function ly(t,e){let i=lc(t).getPropertyValue(e),s=i&&i.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function lb(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function lv(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:"middle"===s?i<.5?t.y:e.y:"after"===s?i<1?t.y:e.y:i>0?e.y:t.y}}function l_(t,e,i,s){let r={x:t.cp2x,y:t.cp2y},n={x:e.cp1x,y:e.cp1y},a=lb(t,r,i),o=lb(r,n,i),l=lb(n,e,i),h=lb(a,o,i),c=lb(o,l,i);return lb(h,c,i)}function lw(t,e,i){var s;return t?(s=i,{x:t=>e+e+s-t,setWidth(t){s=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function lM(t,e){let i,s;("ltr"===e||"rtl"===e)&&(s=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=s)}function lP(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function lk(t){return"angle"===t?{between:a9,compare:a4,normalize:a8}:{between:ot,compare:(t,e)=>t-e,normalize:t=>t}}function lS({start:t,end:e,count:i,loop:s,style:r}){return{start:t%i,end:e%i,loop:s&&(e-t+1)%i==0,style:r}}function lT(t,e,i){let s,r,n;if(!i)return[t];let{property:a,start:o,end:l}=i,h=e.length,{compare:c,between:u,normalize:d}=lk(a),{start:p,end:f,loop:m,style:g}=function(t,e,i){let s,{property:r,start:n,end:a}=i,{between:o,normalize:l}=lk(r),h=e.length,{start:c,end:u,loop:d}=t;if(d){for(c+=h,u+=h,s=0;s<h&&o(l(e[c%h][r]),n,a);++s)c--,u--;c%=h,u%=h}return u<c&&(u+=h),{start:c,end:u,loop:d,style:t.style}}(t,e,i),x=[],y=!1,b=null,v=()=>u(o,n,s)&&0!==c(o,n),_=()=>0===c(l,s)||u(l,n,s),w=()=>y||v(),M=()=>!y||_();for(let t=p,i=p;t<=f;++t)(r=e[t%h]).skip||(s=d(r[a]))!==n&&(y=u(s,o,l),null===b&&w()&&(b=0===c(s,o)?t:i),null!==b&&M()&&(x.push(lS({start:b,end:t,loop:m,count:h,style:g})),b=null),i=t,n=s);return null!==b&&x.push(lS({start:b,end:f,loop:m,count:h,style:g})),x}function lE(t,e){let i=[],s=t.segments;for(let r=0;r<s.length;r++){let n=lT(s[r],t.points,e);n.length&&i.push(...n)}return i}function lj(t,e,i,s){return s&&s.setContext&&i?function(t,e,i,s){let r=t._chart.getContext(),n=lA(t.options),{_datasetIndex:a,options:{spanGaps:o}}=t,l=i.length,h=[],c=n,u=e[0].start,d=u;function p(t,e,s,r){let n=o?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=n;for(;i[e%l].skip;)e+=n;t%l!=e%l&&(h.push({start:t%l,end:e%l,loop:s,style:r}),c=r,u=e%l)}}for(let t of e){let e,n=i[(u=o?u:t.start)%l];for(d=u+1;d<=t.end;d++){let o=i[d%l];(function(t,e){if(!e)return!1;let i=[],s=function(t,e){return oy(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,s)!==JSON.stringify(e,s)})(e=lA(s.setContext(o0(r,{type:"segment",p0:n,p1:o,p0DataIndex:(d-1)%l,p1DataIndex:d%l,datasetIndex:a}))),c)&&p(u,d-1,t.loop,c),n=o,c=e}u<d-1&&p(u,d-1,t.loop,c)}return h}(t,e,i,s):e}function lA(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function lC(t,e,i){return t.options.clip?t[i]:e[i]}function lD(t,e){let i=e._clip;if(i.disabled)return!1;let s=function(t,e){let{xScale:i,yScale:s}=t;return i&&s?{left:lC(i,e,"left"),right:lC(i,e,"right"),top:lC(s,e,"top"),bottom:lC(s,e,"bottom")}:e}(e,t.chartArea);return{left:!1===i.left?0:s.left-(!0===i.left?0:i.left),right:!1===i.right?t.width:s.right+(!0===i.right?0:i.right),top:!1===i.top?0:s.top-(!0===i.top?0:i.top),bottom:!1===i.bottom?t.height:s.bottom+(!0===i.bottom?0:i.bottom)}}class lN{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){let r=e.listeners[s],n=e.duration;r.forEach(s=>s({chart:t,initial:e.initial,numSteps:n,currentStep:Math.min(i-e.start,n)}))}_refresh(){this._request||(this._running=!0,this._request=oo.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{let r;if(!i.running||!i.items.length)return;let n=i.items,a=n.length-1,o=!1;for(;a>=0;--a)(r=n[a])._active?(r._total>i.duration&&(i.duration=r._total),r.tick(t),o=!0):(n[a]=n[n.length-1],n.pop());o&&(s.draw(),this._notify(s,i,t,"progress")),n.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=n.length}),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((t,e)=>Math.max(t,e._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!!e&&!!e.running&&!!e.items.length}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var lR=new lN;let lO="transparent",lL={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){let s=ob(t||lO),r=s.valid&&ob(e||lO);return r&&r.valid?r.mix(s,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class lV{constructor(t,e,i,s){let r=e[i];s=oJ([t.to,s,r,t.from]);let n=oJ([t.from,r,s]);this._active=!0,this._fn=t.fn||lL[t.type||typeof n],this._easing=ox[t.easing]||ox.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=n,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let s=this._target[this._prop],r=i-this._start,n=this._duration-r;this._start=i,this._duration=Math.floor(Math.max(n,t.duration)),this._total+=r,this._loop=!!t.loop,this._to=oJ([t.to,e,s,t.from]),this._from=oJ([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e,i=t-this._start,s=this._duration,r=this._prop,n=this._from,a=this._loop,o=this._to;if(this._active=n!==o&&(a||i<s),!this._active){this._target[r]=o,this._notify(!0);return}if(i<0){this._target[r]=n;return}e=i/s%2,e=a&&e>1?2-e:e,e=this._easing(Math.min(1,Math.max(0,e))),this._target[r]=this._fn(n,o,e)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class lF{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!a_(t))return;let e=Object.keys(oD.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{let r=t[s];if(!a_(r))return;let n={};for(let t of e)n[t]=r[t];(av(r.properties)&&r.properties||[s]).forEach(t=>{t!==s&&i.has(t)||i.set(t,n)})})}_animateOptions(t,e){let i=e.options,s=function(t,e){if(!e)return;let i=t.options;if(!i){t.options=e;return}return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i}(t,i);if(!s)return[];let r=this._createAnimations(s,i);return i.$shared&&(function(t,e){let i=[],s=Object.keys(e);for(let e=0;e<s.length;e++){let r=t[s[e]];r&&r.active()&&i.push(r.wait())}return Promise.all(i)})(t.options.$animations,i).then(()=>{t.options=i},()=>{}),r}_createAnimations(t,e){let i,s=this._properties,r=[],n=t.$animations||(t.$animations={}),a=Object.keys(e),o=Date.now();for(i=a.length-1;i>=0;--i){let l=a[i];if("$"===l.charAt(0))continue;if("options"===l){r.push(...this._animateOptions(t,e));continue}let h=e[l],c=n[l],u=s.get(l);if(c)if(u&&c.active()){c.update(u,h,o);continue}else c.cancel();if(!u||!u.duration){t[l]=h;continue}n[l]=c=new lV(u,t,l,h),r.push(c)}return r}update(t,e){if(0===this._properties.size)return void Object.assign(t,e);let i=this._createAnimations(t,e);if(i.length)return lR.add(this._chart,i),!0}}function lI(t,e){let i=t&&t.options||{},s=i.reverse,r=void 0===i.min?e:0,n=void 0===i.max?e:0;return{start:s?n:r,end:s?r:n}}function lz(t,e){let i,s,r=[],n=t._getSortedDatasetMetas(e);for(i=0,s=n.length;i<s;++i)r.push(n[i].index);return r}function lB(t,e,i,s={}){let r,n,a,o,l=t.keys,h="single"===s.mode;if(null===e)return;let c=!1;for(r=0,n=l.length;r<n;++r){if((a=+l[r])===i){if(c=!0,s.all)continue;break}aw(o=t.values[a])&&(h||0===e||aZ(e)===aZ(o))&&(e+=o)}return c||s.all?e:0}function l$(t,e){let i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function lW(t,e,i,s){for(let r of e.getMatchingVisibleMetas(s).reverse()){let e=t[r.index];if(i&&e>0||!i&&e<0)return r.index}return null}function lH(t,e){let i,{chart:s,_cachedMeta:r}=t,n=s._stacks||(s._stacks={}),{iScale:a,vScale:o,index:l}=r,h=a.axis,c=o.axis,u=`${a.id}.${o.id}.${r.stack||r.type}`,d=e.length;for(let t=0;t<d;++t){let s=e[t],{[h]:a,[c]:d}=s;(i=(s._stacks||(s._stacks={}))[c]=function(t,e,i){let s=t[e]||(t[e]={});return s[i]||(s[i]={})}(n,u,a))[l]=d,i._top=lW(i,o,!0,r.type),i._bottom=lW(i,o,!1,r.type),(i._visualValues||(i._visualValues={}))[l]=d}}function lU(t,e){let i=t.scales;return Object.keys(i).filter(t=>i[t].axis===e).shift()}function lq(t,e){let i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s)for(let r of e=e||t._parsed){let t=r._stacks;if(!t||void 0===t[s]||void 0===t[s][i])return;delete t[s][i],void 0!==t[s]._visualValues&&void 0!==t[s]._visualValues[i]&&delete t[s]._visualValues[i]}}let lY=t=>"reset"===t||"none"===t,lX=(t,e)=>e?t:Object.assign({},t),lK=(t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:lz(i,!0),values:null};class lG{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=l$(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&lq(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,r=e.xAxisID=aP(i.xAxisID,lU(t,"x")),n=e.yAxisID=aP(i.yAxisID,lU(t,"y")),a=e.rAxisID=aP(i.rAxisID,lU(t,"r")),o=e.indexAxis,l=e.iAxisID=s(o,r,n,a),h=e.vAxisID=s(o,n,r,a);e.xScale=this.getScaleForId(r),e.yScale=this.getScaleForId(n),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&on(this._data,this),t._stacked&&lq(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(a_(e)){let t=this._cachedMeta;this._data=function(t,e){let i,s,r,{iScale:n,vScale:a}=e,o="x"===n.axis?"x":"y",l="x"===a.axis?"x":"y",h=Object.keys(t),c=Array(h.length);for(i=0,s=h.length;i<s;++i)r=h[i],c[i]={[o]:r,[l]:t[r]};return c}(e,t)}else if(i!==e){if(i){on(i,this);let t=this._cachedMeta;lq(t),t._parsed=[]}e&&Object.isExtensible(e)&&function(t,e){if(t._chartjs)return t._chartjs.listeners.push(e);Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),or.forEach(e=>{let i="_onData"+aF(e),s=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let r=s.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[i]&&t[i](...e)}),r}})})}(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),s=!1;this._dataCheck();let r=e._stacked;e._stacked=l$(e.vScale,e),e.stack!==i.stack&&(s=!0,lq(e),e.stack=i.stack),this._resyncElements(t),(s||r!==e._stacked)&&(lH(this,e._parsed),e._stacked=l$(e.vScale,e))}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let i,s,r,{_cachedMeta:n,_data:a}=this,{iScale:o,_stacked:l}=n,h=o.axis,c=0===t&&e===a.length||n._sorted,u=t>0&&n._parsed[t-1];if(!1===this._parsing)n._parsed=a,n._sorted=!0,r=a;else{r=av(a[t])?this.parseArrayData(n,a,t,e):a_(a[t])?this.parseObjectData(n,a,t,e):this.parsePrimitiveData(n,a,t,e);let o=()=>null===s[h]||u&&s[h]<u[h];for(i=0;i<e;++i)n._parsed[i+t]=s=r[i],c&&(o()&&(c=!1),u=s);n._sorted=c}l&&lH(this,r)}parsePrimitiveData(t,e,i,s){let r,n,{iScale:a,vScale:o}=t,l=a.axis,h=o.axis,c=a.getLabels(),u=a===o,d=Array(s);for(r=0;r<s;++r)n=r+i,d[r]={[l]:u||a.parse(c[n],n),[h]:o.parse(e[n],n)};return d}parseArrayData(t,e,i,s){let r,n,a,{xScale:o,yScale:l}=t,h=Array(s);for(r=0;r<s;++r)a=e[n=r+i],h[r]={x:o.parse(a[0],n),y:l.parse(a[1],n)};return h}parseObjectData(t,e,i,s){let r,n,a,{xScale:o,yScale:l}=t,{xAxisKey:h="x",yAxisKey:c="y"}=this._parsing,u=Array(s);for(r=0;r<s;++r)a=e[n=r+i],u[r]={x:o.parse(aV(a,h),n),y:l.parse(aV(a,c),n)};return u}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let s=this.chart,r=this._cachedMeta,n=e[t.axis];return lB({keys:lz(s,!0),values:e._stacks[t.axis]._visualValues},n,r.index,{mode:i})}updateRangeFromParsed(t,e,i,s){let r=i[e.axis],n=null===r?NaN:r,a=s&&i._stacks[e.axis];s&&a&&(s.values=a,n=lB(s,r,this._cachedMeta.index)),t.min=Math.min(t.min,n),t.max=Math.max(t.max,n)}getMinMax(t,e){let i,s,r=this._cachedMeta,n=r._parsed,a=r._sorted&&t===r.iScale,o=n.length,l=this._getOtherScale(t),h=lK(e,r,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:u,max:d}=function(t){let{min:e,max:i,minDefined:s,maxDefined:r}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:r?i:Number.POSITIVE_INFINITY}}(l);function p(){let e=(s=n[i])[l.axis];return!aw(s[t.axis])||u>e||d<e}for(i=0;i<o&&(p()||(this.updateRangeFromParsed(c,t,s,h),!a));++i);if(a){for(i=o-1;i>=0;--i)if(!p()){this.updateRangeFromParsed(c,t,s,h);break}}return c}getAllParsedValues(t){let e,i,s,r=this._cachedMeta._parsed,n=[];for(e=0,i=r.length;e<i;++e)aw(s=r[e][t.axis])&&n.push(s);return n}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,s=e.vScale,r=this.getParsed(t);return{label:i?""+i.getLabelForValue(r[i.axis]):"",value:s?""+s.getLabelForValue(r[s.axis]):""}}_update(t){var e;let i,s,r,n,a=this._cachedMeta;this.update(t||"default"),a_(e=aP(this.options.clip,function(t,e,i){if(!1===i)return!1;let s=lI(t,i),r=lI(e,i);return{top:r.end,right:s.end,bottom:r.start,left:s.start}}(a.xScale,a.yScale,this.getMaxOverflow())))?(i=e.top,s=e.right,r=e.bottom,n=e.left):i=s=r=n=e,a._clip={top:i,right:s,bottom:r,left:n,disabled:!1===e}}update(t){}draw(){let t,e=this._ctx,i=this.chart,s=this._cachedMeta,r=s.data||[],n=i.chartArea,a=[],o=this._drawStart||0,l=this._drawCount||r.length-o,h=this.options.drawActiveElementsOnTop;for(s.dataset&&s.dataset.draw(e,n,o,l),t=o;t<o+l;++t){let i=r[t];i.hidden||(i.active&&h?a.push(i):i.draw(e,n))}for(t=0;t<a.length;++t)a[t].draw(e,n)}getStyle(t,e){let i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){var s,r;let n,a=this.getDataset();if(t>=0&&t<this._cachedMeta.data.length){let e=this._cachedMeta.data[t];(n=e.$context||(s=this.getContext(),e.$context=o0(s,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"}))).parsed=this.getParsed(t),n.raw=a.data[t],n.index=n.dataIndex=t}else(n=this.$context||(this.$context=o0(this.chart.getContext(),{active:!1,dataset:void 0,datasetIndex:r=this.index,index:r,mode:"default",type:"dataset"}))).dataset=a,n.index=n.datasetIndex=this.index;return n.active=!!e,n.mode=i,n}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let s="active"===e,r=this._cachedDataOpts,n=t+"-"+e,a=r[n],o=this.enableOptionSharing&&aI(i);if(a)return lX(a,o);let l=this.chart.config,h=l.datasetElementScopeKeys(this._type,t),c=s?[`${t}Hover`,"hover",t,""]:[t,""],u=l.getOptionScopes(this.getDataset(),h),d=Object.keys(oD.elements[t]),p=l.resolveNamedOptions(u,d,()=>this.getContext(i,s,e),c);return p.$shared&&(p.$shared=o,r[n]=Object.freeze(lX(p,o))),p}_resolveAnimations(t,e,i){let s,r=this.chart,n=this._cachedDataOpts,a=`animation-${e}`,o=n[a];if(o)return o;if(!1!==r.options.animation){let r=this.chart.config,n=r.datasetAnimationScopeKeys(this._type,e),a=r.getOptionScopes(this.getDataset(),n);s=r.createResolver(a,this.getContext(t,i,e))}let l=new lF(r,s&&s.animations);return s&&s._cacheable&&(n[a]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||lY(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,r=this.getSharedOptions(i),n=this.includeOptions(e,r)||r!==s;return this.updateSharedOptions(r,e,i),{sharedOptions:r,includeOptions:n}}updateElement(t,e,i,s){lY(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!lY(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;let r=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(r)||r})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];let s=i.length,r=e.length,n=Math.min(r,s);n&&this.parse(0,n),r>s?this._insertElements(s,r-s,t):r<s&&this._removeElements(r,s-r)}_insertElements(t,e,i=!0){let s,r=this._cachedMeta,n=r.data,a=t+e,o=t=>{for(t.length+=e,s=t.length-1;s>=a;s--)t[s]=t[s-e]};for(o(n),s=t;s<a;++s)n[s]=new this.dataElementType;this._parsing&&o(r._parsed),this.parse(t,e),i&&this.updateElements(n,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let s=i._parsed.splice(t,e);i._stacked&&lq(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function lZ(t,e,i,s){return av(t)?!function(t,e,i,s){let r=i.parse(t[0],s),n=i.parse(t[1],s),a=Math.min(r,n),o=Math.max(r,n),l=a,h=o;Math.abs(a)>Math.abs(o)&&(l=o,h=a),e[i.axis]=h,e._custom={barStart:l,barEnd:h,start:r,end:n,min:a,max:o}}(t,e,i,s):e[i.axis]=i.parse(t,s),e}function lQ(t,e,i,s){let r,n,a,o,l=t.iScale,h=t.vScale,c=l.getLabels(),u=l===h,d=[];for(r=i,n=i+s;r<n;++r)o=e[r],(a={})[l.axis]=u||l.parse(c[r],r),d.push(lZ(o,a,h,r));return d}function lJ(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function l0(t,e,i,s){var r,n,a;return t=s?l1((r=t,n=e,a=i,t=r===n?a:r===a?n:r),i,e):l1(t,e,i)}function l1(t,e,i){return"start"===t?e:"end"===t?i:t}class l2 extends lG{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,s){return lQ(t,e,i,s)}parseArrayData(t,e,i,s){return lQ(t,e,i,s)}parseObjectData(t,e,i,s){let r,n,a,o,{iScale:l,vScale:h}=t,{xAxisKey:c="x",yAxisKey:u="y"}=this._parsing,d="x"===l.axis?c:u,p="x"===h.axis?c:u,f=[];for(r=i,n=i+s;r<n;++r)o=e[r],(a={})[l.axis]=l.parse(aV(o,d),r),f.push(lZ(aV(o,p),a,h,r));return f}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);let r=i._custom;r&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,r.min),t.max=Math.max(t.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let{iScale:e,vScale:i}=this._cachedMeta,s=this.getParsed(t),r=s._custom,n=lJ(r)?"["+r.start+", "+r.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+e.getLabelForValue(s[e.axis]),value:n}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){let r="reset"===s,{index:n,_cachedMeta:{vScale:a}}=this,o=a.getBasePixel(),l=a.isHorizontal(),h=this._getRuler(),{sharedOptions:c,includeOptions:u}=this._getSharedOptions(e,s);for(let d=e;d<e+i;d++){let e=this.getParsed(d),i=r||ab(e[a.axis])?{base:o,head:o}:this._calculateBarValuePixels(d),p=this._calculateBarIndexPixels(d,h),f=(e._stacks||{})[a.axis],m={horizontal:l,base:i.base,enableBorderRadius:!f||lJ(e._custom)||n===f._top||n===f._bottom,x:l?i.head:p.center,y:l?p.center:i.head,height:l?p.size:Math.abs(i.size),width:l?Math.abs(i.size):p.size};u&&(m.options=c||this.resolveDataElementOptions(d,t[d].active?"active":s));let g=m.options||t[d].options;!function(t,e,i,s){let r,n,a,o,l,h=e.borderSkipped,c={};if(!h){t.borderSkipped=c;return}if(!0===h){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:u,end:d,reverse:p,top:f,bottom:m}=(t.horizontal?(r=t.base>t.x,n="left",a="right"):(r=t.base<t.y,n="bottom",a="top"),r?(o="end",l="start"):(o="start",l="end"),{start:n,end:a,reverse:r,top:o,bottom:l});"middle"===h&&i&&(t.enableBorderRadius=!0,(i._top||0)===s?h=f:(i._bottom||0)===s?h=m:(c[l0(m,u,d,p)]=!0,h=f)),c[l0(h,u,d,p)]=!0,t.borderSkipped=c}(m,g,f,n),function(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?.33*(1===i):e}(m,g,h.ratio),this.updateElement(t[d],d,m,s)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(t=>t.controller.options.grouped),r=i.options.stacked,n=[],a=this._cachedMeta.controller.getParsed(e),o=a&&a[i.axis],l=t=>{let e=t._parsed.find(t=>t[i.axis]===o),s=e&&e[t.vScale.axis];if(ab(s)||isNaN(s))return!0};for(let i of s)if(!(void 0!==e&&l(i))&&((!1===r||-1===n.indexOf(i.stack)||void 0===r&&void 0===i.stack)&&n.push(i.stack),i.index===t))break;return n.length||n.push(void 0),n}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){let s=this._getStacks(t,i),r=void 0!==e?s.indexOf(e):-1;return -1===r?s.length-1:r}_getRuler(){let t,e,i=this.options,s=this._cachedMeta,r=s.iScale,n=[];for(t=0,e=s.data.length;t<e;++t)n.push(r.getPixelForValue(this.getParsed(t)[r.axis],t));let a=i.barThickness;return{min:a||function(t){let e,i,s,r,n=t.iScale,a=function(t,e){if(!t._cache.$bar){let i=t.getMatchingVisibleMetas(e),s=[];for(let e=0,r=i.length;e<r;e++)s=s.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=oa(s.sort((t,e)=>t-e))}return t._cache.$bar}(n,t.type),o=n._length,l=()=>{32767!==s&&-32768!==s&&(aI(r)&&(o=Math.min(o,Math.abs(s-r)||o)),r=s)};for(e=0,i=a.length;e<i;++e)s=n.getPixelForValue(a[e]),l();for(e=0,r=void 0,i=n.ticks.length;e<i;++e)s=n.getPixelForTick(e),l();return o}(s),pixels:n,start:r._startPixel,end:r._endPixel,stackCount:this._getStackCount(),scale:r,grouped:i.grouped,ratio:a?1:i.categoryPercentage*i.barPercentage}}_calculateBarValuePixels(t){let e,i,{_cachedMeta:{vScale:s,_stacked:r,index:n},options:{base:a,minBarLength:o}}=this,l=a||0,h=this.getParsed(t),c=h._custom,u=lJ(c),d=h[s.axis],p=0,f=r?this.applyStack(s,h,r):d;f!==d&&(p=f-d,f=d),u&&(d=c.barStart,f=c.barEnd-c.barStart,0!==d&&aZ(d)!==aZ(c.barEnd)&&(p=0),p+=d);let m=ab(a)||u?p:a,g=s.getPixelForValue(m);if(Math.abs(i=(e=this.chart.getDataVisibility(t)?s.getPixelForValue(p+f):g)-g)<o){var x;i=(0!==(x=i)?aZ(x):(s.isHorizontal()?1:-1)*(s.min>=l?1:-1))*o,d===l&&(g-=i/2);let t=s.getPixelForDecimal(0),a=s.getPixelForDecimal(1),c=Math.min(t,a);e=(g=Math.max(Math.min(g,Math.max(t,a)),c))+i,r&&!u&&(h._stacks[s.axis]._visualValues[n]=s.getValueForPixel(e)-s.getValueForPixel(g))}if(g===s.getPixelForValue(l)){let t=aZ(i)*s.getLineWidthForValue(l)/2;g+=t,i-=t}return{size:i,base:g,head:e,center:e+i/2}}_calculateBarIndexPixels(t,e){let i,s,r=e.scale,n=this.options,a=n.skipNull,o=aP(n.maxBarThickness,1/0);if(e.grouped){let r=a?this._getStackCount(t):e.stackCount,l="flex"===n.barThickness?function(t,e,i,s){let r=e.pixels,n=r[t],a=t>0?r[t-1]:null,o=t<r.length-1?r[t+1]:null,l=i.categoryPercentage;null===a&&(a=n-(null===o?e.end-e.start:o-n)),null===o&&(o=n+n-a);let h=n-(n-Math.min(a,o))/2*l;return{chunk:Math.abs(o-a)/2*l/s,ratio:i.barPercentage,start:h}}(t,e,n,r):function(t,e,i,s){let r,n,a=i.barThickness;return ab(a)?(r=e.min*i.categoryPercentage,n=i.barPercentage):(r=a*s,n=1),{chunk:r/s,ratio:n,start:e.pixels[t]-r/2}}(t,e,n,r),h=this._getStackIndex(this.index,this._cachedMeta.stack,a?t:void 0);i=l.start+l.chunk*h+l.chunk/2,s=Math.min(o,l.chunk*l.ratio)}else i=r.getPixelForValue(this.getParsed(t)[r.axis],t),s=Math.min(o,e.min*e.ratio);return{base:i-s/2,head:i+s/2,center:i,size:s}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length,r=0;for(;r<s;++r)null===this.getParsed(r)[e.axis]||i[r].hidden||i[r].draw(this._ctx)}}class l5 extends lG{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){let r=super.parsePrimitiveData(t,e,i,s);for(let t=0;t<r.length;t++)r[t]._custom=this.resolveDataElementOptions(t+i).radius;return r}parseArrayData(t,e,i,s){let r=super.parseArrayData(t,e,i,s);for(let t=0;t<r.length;t++){let s=e[i+t];r[t]._custom=aP(s[2],this.resolveDataElementOptions(t+i).radius)}return r}parseObjectData(t,e,i,s){let r=super.parseObjectData(t,e,i,s);for(let t=0;t<r.length;t++){let s=e[i+t];r[t]._custom=aP(s&&s.r&&+s.r,this.resolveDataElementOptions(t+i).radius)}return r}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:r}=e,n=this.getParsed(t),a=s.getLabelForValue(n.x),o=r.getLabelForValue(n.y),l=n._custom;return{label:i[t]||"",value:"("+a+", "+o+(l?", "+l:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let r="reset"===s,{iScale:n,vScale:a}=this._cachedMeta,{sharedOptions:o,includeOptions:l}=this._getSharedOptions(e,s),h=n.axis,c=a.axis;for(let u=e;u<e+i;u++){let e=t[u],i=!r&&this.getParsed(u),d={},p=d[h]=r?n.getPixelForDecimal(.5):n.getPixelForValue(i[h]),f=d[c]=r?a.getBasePixel():a.getPixelForValue(i[c]);d.skip=isNaN(p)||isNaN(f),l&&(d.options=o||this.resolveDataElementOptions(u,e.active?"active":s),r&&(d.options.radius=0)),this.updateElement(e,u,d,s)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));let r=s.radius;return"active"!==e&&(s.radius=0),s.radius+=aP(i&&i._custom,r),s}}class l6 extends lG{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,r)=>{let n=t.getDatasetMeta(0).controller.getStyle(r);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,s=this._cachedMeta;if(!1===this._parsing)s._parsed=i;else{let r,n,a=t=>+i[t];if(a_(i[t])){let{key:t="value"}=this._parsing;a=e=>+aV(i[e],t)}for(r=t,n=t+e;r<n;++r)s._parsed[r]=a(r)}}_getRotation(){return a2(this.options.rotation-90)}_getCircumference(){return a2(this.options.circumference)}_getRotationExtents(){let t=aW,e=-aW;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){let s=this.chart.getDatasetMeta(i).controller,r=s._getRotation(),n=s._getCircumference();t=Math.min(t,r),e=Math.max(e,r+n)}return{rotation:t,circumference:e-t}}update(t){let{chartArea:e}=this.chart,i=this._cachedMeta,s=i.data,r=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,n=Math.max((Math.min(e.width,e.height)-r)/2,0),a=Math.min(ak(this.options.cutout,n),1),o=this._getRingWeight(this.index),{circumference:l,rotation:h}=this._getRotationExtents(),{ratioX:c,ratioY:u,offsetX:d,offsetY:p}=function(t,e,i){let s=1,r=1,n=0,a=0;if(e<aW){let o=t+e,l=Math.cos(t),h=Math.sin(t),c=Math.cos(o),u=Math.sin(o),d=(e,s,r)=>a9(e,t,o,!0)?1:Math.max(s,s*i,r,r*i),p=(e,s,r)=>a9(e,t,o,!0)?-1:Math.min(s,s*i,r,r*i),f=d(0,l,c),m=d(aY,h,u),g=p(a$,l,c),x=p(a$+aY,h,u);s=(f-g)/2,r=(m-x)/2,n=-(f+g)/2,a=-(m+x)/2}return{ratioX:s,ratioY:r,offsetX:n,offsetY:a}}(h,l,a),f=Math.max(Math.min((e.width-r)/c,(e.height-r)/u)/2,0),m=aS(this.options.radius,f),g=Math.max(m*a,0),x=(m-g)/this._getVisibleDatasetWeightTotal();this.offsetX=d*m,this.offsetY=p*m,i.total=this.calculateTotal(),this.outerRadius=m-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*o,0),this.updateElements(s,0,s.length,t)}_circumference(t,e){let i=this.options,s=this._cachedMeta,r=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===s._parsed[t]||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*r/aW)}updateElements(t,e,i,s){let r,n="reset"===s,a=this.chart,o=a.chartArea,l=a.options.animation,h=(o.left+o.right)/2,c=(o.top+o.bottom)/2,u=n&&l.animateScale,d=u?0:this.innerRadius,p=u?0:this.outerRadius,{sharedOptions:f,includeOptions:m}=this._getSharedOptions(e,s),g=this._getRotation();for(r=0;r<e;++r)g+=this._circumference(r,n);for(r=e;r<e+i;++r){let e=this._circumference(r,n),i=t[r],a={x:h+this.offsetX,y:c+this.offsetY,startAngle:g,endAngle:g+e,circumference:e,outerRadius:p,innerRadius:d};m&&(a.options=f||this.resolveDataElementOptions(r,i.active?"active":s)),g+=e,this.updateElement(i,r,a,s)}}calculateTotal(){let t,e=this._cachedMeta,i=e.data,s=0;for(t=0;t<i.length;t++){let r=e._parsed[t];null!==r&&!isNaN(r)&&this.chart.getDataVisibility(t)&&!i[t].hidden&&(s+=Math.abs(r))}return s}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?Math.abs(t)/e*aW:0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=oP(e._parsed[t],i.options.locale);return{label:s[t]||"",value:r}}getMaxBorderWidth(t){let e,i,s,r,n,a=0,o=this.chart;if(!t){for(e=0,i=o.data.datasets.length;e<i;++e)if(o.isDatasetVisible(e)){t=(s=o.getDatasetMeta(e)).data,r=s.controller;break}}if(!t)return 0;for(e=0,i=t.length;e<i;++e)"inner"!==(n=r.resolveDataElementOptions(e)).borderAlign&&(a=Math.max(a,n.borderWidth||0,n.hoverBorderWidth||0));return a}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){let t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(aP(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class l3 extends lG{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:s=[],_dataset:r}=e,n=this.chart._animationsDisabled,{start:a,count:o}=od(e,s,n);this._drawStart=a,this._drawCount=o,op(e)&&(a=0,o=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!r._decimated,i.points=s;let l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(i,void 0,{animated:!n,options:l},t),this.updateElements(s,a,o,t)}updateElements(t,e,i,s){let r="reset"===s,{iScale:n,vScale:a,_stacked:o,_dataset:l}=this._cachedMeta,{sharedOptions:h,includeOptions:c}=this._getSharedOptions(e,s),u=n.axis,d=a.axis,{spanGaps:p,segment:f}=this.options,m=a0(p)?p:Number.POSITIVE_INFINITY,g=this.chart._animationsDisabled||r||"none"===s,x=e+i,y=t.length,b=e>0&&this.getParsed(e-1);for(let i=0;i<y;++i){let p=t[i],y=g?p:{};if(i<e||i>=x){y.skip=!0;continue}let v=this.getParsed(i),_=ab(v[d]),w=y[u]=n.getPixelForValue(v[u],i),M=y[d]=r||_?a.getBasePixel():a.getPixelForValue(o?this.applyStack(a,v,o):v[d],i);y.skip=isNaN(w)||isNaN(M)||_,y.stop=i>0&&Math.abs(v[u]-b[u])>m,f&&(y.parsed=v,y.raw=l.data[i]),c&&(y.options=h||this.resolveDataElementOptions(i,p.active?"active":s)),g||this.updateElement(p,i,y,s),b=v}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];return s.length?Math.max(i,s[0].size(this.resolveDataElementOptions(0)),s[s.length-1].size(this.resolveDataElementOptions(s.length-1)))/2:i}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class l4 extends lG{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,r)=>{let n=t.getDatasetMeta(0).controller.getStyle(r);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=oP(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:r}}parseObjectData(t,e,i,s){return li.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((t,i)=>{let s=this.getParsed(i).r;!isNaN(s)&&this.chart.getDataVisibility(i)&&(s<e.min&&(e.min=s),s>e.max&&(e.max=s))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,s=Math.max(Math.min(e.right-e.left,e.bottom-e.top)/2,0),r=Math.max(i.cutoutPercentage?s/100*i.cutoutPercentage:1,0),n=(s-r)/t.getVisibleDatasetCount();this.outerRadius=s-n*this.index,this.innerRadius=this.outerRadius-n}updateElements(t,e,i,s){let r,n="reset"===s,a=this.chart,o=a.options.animation,l=this._cachedMeta.rScale,h=l.xCenter,c=l.yCenter,u=l.getIndexAngle(0)-.5*a$,d=u,p=360/this.countVisibleElements();for(r=0;r<e;++r)d+=this._computeAngle(r,s,p);for(r=e;r<e+i;r++){let e=t[r],i=d,f=d+this._computeAngle(r,s,p),m=a.getDataVisibility(r)?l.getDistanceFromCenterForValue(this.getParsed(r).r):0;d=f,n&&(o.animateScale&&(m=0),o.animateRotate&&(i=f=u));let g={x:h,y:c,innerRadius:0,outerRadius:m,startAngle:i,endAngle:f,options:this.resolveDataElementOptions(r,e.active?"active":s)};this.updateElement(e,r,g,s)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?a2(this.resolveDataElementOptions(t,e).angle||i):0}}class l8 extends l6{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class l9 extends lG{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return li.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta,i=e.dataset,s=e.data||[],r=e.iScale.getLabels();if(i.points=s,"resize"!==t){let e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);let n={_loop:!0,_fullLoop:r.length===s.length,options:e};this.updateElement(i,void 0,n,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){let r=this._cachedMeta.rScale,n="reset"===s;for(let a=e;a<e+i;a++){let e=t[a],i=this.resolveDataElementOptions(a,e.active?"active":s),o=r.getPointPositionForValue(a,this.getParsed(a).r),l=n?r.xCenter:o.x,h=n?r.yCenter:o.y,c={x:l,y:h,angle:o.angle,skip:isNaN(l)||isNaN(h),options:i};this.updateElement(e,a,c,s)}}}class l7 extends lG{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:r}=e,n=this.getParsed(t),a=s.getLabelForValue(n.x),o=r.getLabelForValue(n.y);return{label:i[t]||"",value:"("+a+", "+o+")"}}update(t){let e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled,{start:r,count:n}=od(e,i,s);if(this._drawStart=r,this._drawCount=n,op(e)&&(r=0,n=i.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:r,_dataset:n}=e;r._chart=this.chart,r._datasetIndex=this.index,r._decimated=!!n._decimated,r.points=i;let a=this.resolveDatasetElementOptions(t);a.segment=this.options.segment,this.updateElement(r,void 0,{animated:!s,options:a},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,r,n,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,s){let r="reset"===s,{iScale:n,vScale:a,_stacked:o,_dataset:l}=this._cachedMeta,h=this.resolveDataElementOptions(e,s),c=this.getSharedOptions(h),u=this.includeOptions(s,c),d=n.axis,p=a.axis,{spanGaps:f,segment:m}=this.options,g=a0(f)?f:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||r||"none"===s,y=e>0&&this.getParsed(e-1);for(let h=e;h<e+i;++h){let e=t[h],i=this.getParsed(h),f=x?e:{},b=ab(i[p]),v=f[d]=n.getPixelForValue(i[d],h),_=f[p]=r||b?a.getBasePixel():a.getPixelForValue(o?this.applyStack(a,i,o):i[p],h);f.skip=isNaN(v)||isNaN(_)||b,f.stop=h>0&&Math.abs(i[d]-y[d])>g,m&&(f.parsed=i,f.raw=l.data[h]),u&&(f.options=c||this.resolveDataElementOptions(h,e.active?"active":s)),x||this.updateElement(e,h,f,s),y=i}this.updateSharedOptions(c,s,h)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}let i=t.dataset,s=i.options&&i.options.borderWidth||0;return e.length?Math.max(s,e[0].size(this.resolveDataElementOptions(0)),e[e.length-1].size(this.resolveDataElementOptions(e.length-1)))/2:s}}function ht(){throw Error("This method is not implemented: Check that a complete date adapter is provided.")}class he{static override(t){Object.assign(he.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return ht()}parse(){return ht()}format(){return ht()}add(){return ht()}diff(){return ht()}startOf(){return ht()}endOf(){return ht()}}var hi={_date:he};function hs(t,e,i,s,r){let n=t.getSortedVisibleDatasetMetas(),a=i[e];for(let t=0,i=n.length;t<i;++t){let{index:i,data:o}=n[t],{lo:l,hi:h}=function(t,e,i,s){let{controller:r,data:n,_sorted:a}=t,o=r._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(o&&e===o.axis&&"r"!==e&&a&&n.length){let a=o._reversePixels?os:oi;if(s){if(r._sharedOptions){let t=n[0],s="function"==typeof t.getRange&&t.getRange(e);if(s){let t=a(n,e,i-s),r=a(n,e,i+s);return{lo:t.lo,hi:r.hi}}}}else{let s=a(n,e,i);if(l){let{vScale:e}=r._cachedMeta,{_parsed:i}=t,n=i.slice(0,s.lo+1).reverse().findIndex(t=>!ab(t[e.axis]));s.lo-=Math.max(0,n);let a=i.slice(s.hi).findIndex(t=>!ab(t[e.axis]));s.hi+=Math.max(0,a)}return s}}return{lo:0,hi:n.length-1}}(n[t],e,a,r);for(let t=l;t<=h;++t){let e=o[t];e.skip||s(e,i,t)}}}function hr(t,e,i,s,r){let n=[];return(r||t.isPointInArea(e))&&hs(t,i,e,function(i,a,o){(r||oF(i,t.chartArea,0))&&i.inRange(e.x,e.y,s)&&n.push({element:i,datasetIndex:a,index:o})},!0),n}function hn(t,e,i,s,r,n){let a;return n||t.isPointInArea(e)?"r"!==i||s?function(t,e,i,s,r,n){let a=[],o=function(t){let e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){return Math.sqrt(Math.pow(e?Math.abs(t.x-s.x):0,2)+Math.pow(i?Math.abs(t.y-s.y):0,2))}}(i),l=Number.POSITIVE_INFINITY;return hs(t,i,e,function(i,h,c){let u=i.inRange(e.x,e.y,r);if(s&&!u)return;let d=i.getCenterPoint(r);if(!(n||t.isPointInArea(d))&&!u)return;let p=o(e,d);p<l?(a=[{element:i,datasetIndex:h,index:c}],l=p):p===l&&a.push({element:i,datasetIndex:h,index:c})}),a}(t,e,i,s,r,n):(a=[],hs(t,i,e,function(t,i,s){let{startAngle:n,endAngle:o}=t.getProps(["startAngle","endAngle"],r),{angle:l}=a6(t,{x:e.x,y:e.y});a9(l,n,o)&&a.push({element:t,datasetIndex:i,index:s})}),a):[]}function ha(t,e,i,s,r){let n=[],a="x"===i?"inXRange":"inYRange",o=!1;return(hs(t,i,e,(t,s,l)=>{t[a]&&t[a](e[i],r)&&(n.push({element:t,datasetIndex:s,index:l}),o=o||t.inRange(e.x,e.y,r))}),s&&!o)?[]:n}var ho={modes:{index(t,e,i,s){let r=lf(e,t),n=i.axis||"x",a=i.includeInvisible||!1,o=i.intersect?hr(t,r,n,s,a):hn(t,r,n,!1,s,a),l=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(t=>{let e=o[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})}),l):[]},dataset(t,e,i,s){let r=lf(e,t),n=i.axis||"xy",a=i.includeInvisible||!1,o=i.intersect?hr(t,r,n,s,a):hn(t,r,n,!1,s,a);if(o.length>0){let e=o[0].datasetIndex,i=t.getDatasetMeta(e).data;o=[];for(let t=0;t<i.length;++t)o.push({element:i[t],datasetIndex:e,index:t})}return o},point(t,e,i,s){let r=lf(e,t);return hr(t,r,i.axis||"xy",s,i.includeInvisible||!1)},nearest(t,e,i,s){let r=lf(e,t),n=i.axis||"xy",a=i.includeInvisible||!1;return hn(t,r,n,i.intersect,s,a)},x(t,e,i,s){let r=lf(e,t);return ha(t,r,"x",i.intersect,s)},y(t,e,i,s){let r=lf(e,t);return ha(t,r,"y",i.intersect,s)}}};let hl=["left","top","right","bottom"];function hh(t,e){return t.filter(t=>t.pos===e)}function hc(t,e){return t.filter(t=>-1===hl.indexOf(t.pos)&&t.box.axis===e)}function hu(t,e){return t.sort((t,i)=>{let s=e?i:t,r=e?t:i;return s.weight===r.weight?s.index-r.index:s.weight-r.weight})}function hd(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function hp(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function hf(t,e,i,s){let r,n,a,o,l,h,c=[];for(r=0,n=t.length,l=0;r<n;++r){(o=(a=t[r]).box).update(a.width||e.w,a.height||e.h,function(t,e){let i=e.maxPadding;var s=t?["left","right"]:["top","bottom"];let r={left:0,top:0,right:0,bottom:0};return s.forEach(t=>{r[t]=Math.max(e[t],i[t])}),r}(a.horizontal,e));let{same:n,other:u}=function(t,e,i,s){let{pos:r,box:n}=i,a=t.maxPadding;if(!a_(r)){i.size&&(t[r]-=i.size);let e=s[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?n.height:n.width),i.size=e.size/e.count,t[r]+=i.size}n.getPadding&&hp(a,n.getPadding());let o=Math.max(0,e.outerWidth-hd(a,t,"left","right")),l=Math.max(0,e.outerHeight-hd(a,t,"top","bottom")),h=o!==t.w,c=l!==t.h;return t.w=o,t.h=l,i.horizontal?{same:h,other:c}:{same:c,other:h}}(e,i,a,s);l|=n&&c.length,h=h||u,o.fullSize||c.push(a)}return l&&hf(c,e,i,s)||h}function hm(t,e,i,s,r){t.top=i,t.left=e,t.right=e+s,t.bottom=i+r,t.width=s,t.height=r}function hg(t,e,i,s){let r=i.padding,{x:n,y:a}=e;for(let o of t){let t=o.box,l=s[o.stack]||{count:1,placed:0,weight:1},h=o.stackWeight/l.weight||1;if(o.horizontal){let s=e.w*h,n=l.size||t.height;aI(l.start)&&(a=l.start),t.fullSize?hm(t,r.left,a,i.outerWidth-r.right-r.left,n):hm(t,e.left+l.placed,a,s,n),l.start=a,l.placed+=s,a=t.bottom}else{let s=e.h*h,a=l.size||t.width;aI(l.start)&&(n=l.start),t.fullSize?hm(t,n,r.top,a,i.outerHeight-r.bottom-r.top):hm(t,n,e.top+l.placed,a,s),l.start=n,l.placed+=s,n=t.right}}e.x=n,e.y=a}var hx={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){let i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,s){if(!t)return;let r=oZ(t.options.layout.padding),n=Math.max(e-r.width,0),a=Math.max(i-r.height,0),o=function(t){let e=function(t){let e,i,s,r,n,a,o=[];for(e=0,i=(t||[]).length;e<i;++e)s=t[e],({position:r,options:{stack:n,stackWeight:a=1}}=s),o.push({index:e,box:s,pos:r,horizontal:s.isHorizontal(),weight:s.weight,stack:n&&r+n,stackWeight:a});return o}(t),i=hu(e.filter(t=>t.box.fullSize),!0),s=hu(hh(e,"left"),!0),r=hu(hh(e,"right")),n=hu(hh(e,"top"),!0),a=hu(hh(e,"bottom")),o=hc(e,"x"),l=hc(e,"y");return{fullSize:i,leftAndTop:s.concat(n),rightAndBottom:r.concat(l).concat(a).concat(o),chartArea:hh(e,"chartArea"),vertical:s.concat(r).concat(l),horizontal:n.concat(a).concat(o)}}(t.boxes),l=o.vertical,h=o.horizontal;aE(t.boxes,t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()});let c=Object.freeze({outerWidth:e,outerHeight:i,padding:r,availableWidth:n,availableHeight:a,vBoxMaxWidth:n/2/(l.reduce((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1,0)||1),hBoxMaxHeight:a/2}),u=Object.assign({},r);hp(u,oZ(s));let d=Object.assign({maxPadding:u,w:n,h:a,x:r.left,y:r.top},r),p=function(t,e){let i,s,r,n=function(t){let e={};for(let i of t){let{stack:t,pos:s,stackWeight:r}=i;if(!t||!hl.includes(s))continue;let n=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});n.count++,n.weight+=r}return e}(t),{vBoxMaxWidth:a,hBoxMaxHeight:o}=e;for(i=0,s=t.length;i<s;++i){let{fullSize:s}=(r=t[i]).box,l=n[r.stack],h=l&&r.stackWeight/l.weight;r.horizontal?(r.width=h?h*a:s&&e.availableWidth,r.height=o):(r.width=a,r.height=h?h*o:s&&e.availableHeight)}return n}(l.concat(h),c);hf(o.fullSize,d,c,p),hf(l,d,c,p),hf(h,d,c,p)&&hf(l,d,c,p);let f=d.maxPadding;function m(t){let e=Math.max(f[t]-d[t],0);return d[t]+=e,e}d.y+=m("top"),d.x+=m("left"),m("right"),m("bottom"),hg(o.leftAndTop,d,c,p),d.x+=d.w,d.y+=d.h,hg(o.rightAndBottom,d,c,p),t.chartArea={left:d.left,top:d.top,right:d.left+d.w,bottom:d.top+d.h,height:d.h,width:d.w},aE(o.chartArea,e=>{let i=e.box;Object.assign(i,t.chartArea),i.update(d.w,d.h,{left:0,top:0,right:0,bottom:0})})}};class hy{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class hb extends hy{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}let hv="$chartjs",h_={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},hw=t=>null===t||""===t,hM=!!lx&&{passive:!0};function hP(t,e){for(let i of t)if(i===e||i.contains(e))return!0}function hk(t,e,i){let s=t.canvas,r=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||hP(i.addedNodes,s))&&!hP(i.removedNodes,s);e&&i()});return r.observe(document,{childList:!0,subtree:!0}),r}function hS(t,e,i){let s=t.canvas,r=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||hP(i.removedNodes,s))&&!hP(i.addedNodes,s);e&&i()});return r.observe(document,{childList:!0,subtree:!0}),r}let hT=new Map,hE=0;function hj(){let t=window.devicePixelRatio;t!==hE&&(hE=t,hT.forEach((e,i)=>{i.currentDevicePixelRatio!==t&&e()}))}function hA(t,e,i){let s=t.canvas,r=s&&ll(s);if(!r)return;let n=ol((t,e)=>{let s=r.clientWidth;i(t,e),s<r.clientWidth&&i()},window),a=new ResizeObserver(t=>{let e=t[0],i=e.contentRect.width,s=e.contentRect.height;(0!==i||0!==s)&&n(i,s)});return a.observe(r),hT.size||window.addEventListener("resize",hj),hT.set(t,n),a}function hC(t,e,i){i&&i.disconnect(),"resize"===e&&(hT.delete(t),hT.size||window.removeEventListener("resize",hj))}function hD(t,e,i){let s=t.canvas,r=ol(e=>{null!==t.ctx&&i(function(t,e){let i=h_[t.type]||t.type,{x:s,y:r}=lf(t,e);return{type:i,chart:e,native:t,x:void 0!==s?s:null,y:void 0!==r?r:null}}(e,t))},t);return s&&s.addEventListener(e,r,hM),r}class hN extends hy{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(!function(t,e){let i=t.style,s=t.getAttribute("height"),r=t.getAttribute("width");if(t[hv]={initial:{height:s,width:r,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",hw(r)){let e=ly(t,"width");void 0!==e&&(t.width=e)}if(hw(s))if(""===t.style.height)t.height=t.width/(e||2);else{let e=ly(t,"height");void 0!==e&&(t.height=e)}}(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[hv])return!1;let i=e[hv].initial;["height","width"].forEach(t=>{let s=i[t];ab(s)?e.removeAttribute(t):e.setAttribute(t,s)});let s=i.style||{};return Object.keys(s).forEach(t=>{e.style[t]=s[t]}),e.width=e.width,delete e[hv],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let s=t.$proxies||(t.$proxies={}),r={attach:hk,detach:hS,resize:hA}[e]||hD;s[e]=r(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),s=i[e];s&&((({attach:hC,detach:hC,resize:hC})[e]||function(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,hM)})(t,e,s),i[e]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return function(t,e,i,s){let r=lc(t),n=ld(r,"margin"),a=lh(r.maxWidth,t,"clientWidth")||aU,o=lh(r.maxHeight,t,"clientHeight")||aU,l=function(t,e,i){let s,r;if(void 0===e||void 0===i){let n=t&&ll(t);if(n){let t=n.getBoundingClientRect(),a=lc(n),o=ld(a,"border","width"),l=ld(a,"padding");e=t.width-l.width-o.width,i=t.height-l.height-o.height,s=lh(a.maxWidth,n,"clientWidth"),r=lh(a.maxHeight,n,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:s||aU,maxHeight:r||aU}}(t,e,i),{width:h,height:c}=l;if("content-box"===r.boxSizing){let t=ld(r,"border","width"),e=ld(r,"padding");h-=e.width+t.width,c-=e.height+t.height}return h=Math.max(0,h-n.width),c=Math.max(0,s?h/s:c-n.height),h=lm(Math.min(h,a,l.maxWidth)),c=lm(Math.min(c,o,l.maxHeight)),h&&!c&&(c=lm(h/2)),(void 0!==e||void 0!==i)&&s&&l.height&&c>l.height&&(h=lm(Math.floor((c=l.height)*s))),{width:h,height:c}}(t,e,i,s)}isAttached(t){let e=t&&ll(t);return!!(e&&e.isConnected)}}class hR{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return a0(this.x)&&a0(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let s={};return t.forEach(t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]}),s}}function hO(t,e,i,s,r){let n,a,o,l=aP(s,0),h=Math.min(aP(r,t.length),t.length),c=0;for(i=Math.ceil(i),r&&(i=(n=r-s)/Math.floor(n/i)),o=l;o<0;)o=Math.round(l+ ++c*i);for(a=Math.max(l,0);a<h;a++)a===o&&(e.push(t[a]),o=Math.round(l+ ++c*i))}let hL=t=>"left"===t?"right":"right"===t?"left":t,hV=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,hF=(t,e)=>Math.min(e||t,t);function hI(t,e){let i=[],s=t.length/e,r=t.length,n=0;for(;n<r;n+=s)i.push(t[Math.floor(n)]);return i}function hz(t){return t.drawTicks?t.tickLength:0}function hB(t,e){if(!t.display)return 0;let i=oQ(t.font,e),s=oZ(t.padding);return(av(t.text)?t.text.length:1)*i.lineHeight+s.height}class h$ extends hR{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=aM(t,Number.POSITIVE_INFINITY),e=aM(e,Number.NEGATIVE_INFINITY),i=aM(i,Number.POSITIVE_INFINITY),s=aM(s,Number.NEGATIVE_INFINITY),{min:aM(t,i),max:aM(e,s),minDefined:aw(t),maxDefined:aw(e)}}getMinMax(t){let e,{min:i,max:s,minDefined:r,maxDefined:n}=this.getUserBounds();if(r&&n)return{min:i,max:s};let a=this.getMatchingVisibleMetas();for(let o=0,l=a.length;o<l;++o)e=a[o].controller.getMinMax(this,t),r||(i=Math.min(i,e.min)),n||(s=Math.max(s,e.max));return i=n&&i>s?s:i,s=r&&i>s?i:s,{min:aM(i,aM(s,i)),max:aM(s,aM(i,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){aT(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:s,grace:r,ticks:n}=this.options,a=n.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(t,e,i){let{min:s,max:r}=t,n=aS(e,(r-s)/2),a=(t,e)=>i&&0===t?0:t+e;return{min:a(s,-Math.abs(n)),max:a(r,n)}}(this,r,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let o=a<this.ticks.length;this._convertTicksToLabels(o?hI(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),n.display&&(n.autoSkip||"auto"===n.source)&&(this.ticks=function(t,e){let i=t.options.ticks,s=function(t){let e=t.options.offset,i=t._tickSize();return Math.floor(Math.min(t._length/i+ +!e,t._maxLength/i))}(t),r=Math.min(i.maxTicksLimit||s,s),n=i.major.enabled?function(t){let e,i,s=[];for(e=0,i=t.length;e<i;e++)t[e].major&&s.push(e);return s}(e):[],a=n.length,o=n[0],l=n[a-1],h=[];if(a>r)return function(t,e,i,s){let r,n=0,a=i[0];for(r=0,s=Math.ceil(s);r<t.length;r++)r===a&&(e.push(t[r]),a=i[++n*s])}(e,h,n,a/r),h;let c=function(t,e,i){let s=function(t){let e,i,s=t.length;if(s<2)return!1;for(i=t[0],e=1;e<s;++e)if(t[e]-t[e-1]!==i)return!1;return i}(t),r=e.length/i;if(!s)return Math.max(r,1);let n=function(t){let e,i=[],s=Math.sqrt(t);for(e=1;e<s;e++)t%e==0&&(i.push(e),i.push(t/e));return s===(0|s)&&i.push(s),i.sort((t,e)=>t-e).pop(),i}(s);for(let t=0,e=n.length-1;t<e;t++){let e=n[t];if(e>r)return e}return Math.max(r,1)}(n,e,r);if(a>0){let t,i,s=a>1?Math.round((l-o)/(a-1)):null;for(hO(e,h,c,ab(s)?0:o-s,o),t=0,i=a-1;t<i;t++)hO(e,h,c,n[t],n[t+1]);return hO(e,h,c,l,ab(s)?e.length:l+s),h}return hO(e,h,c),h}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),o&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){aT(this.options.afterUpdate,[this])}beforeSetDimensions(){aT(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){aT(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),aT(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){aT(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e,i,s,r=this.options.ticks;for(e=0,i=t.length;e<i;e++)(s=t[e]).label=aT(r.callback,[s.value,e,t],this)}afterTickToLabelConversion(){aT(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){aT(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t,e,i,s=this.options,r=s.ticks,n=hF(this.ticks.length,s.ticks.maxTicksLimit),a=r.minRotation||0,o=r.maxRotation,l=a;if(!this._isVisible()||!r.display||a>=o||n<=1||!this.isHorizontal()){this.labelRotation=a;return}let h=this._getLabelSizes(),c=h.widest.width,u=h.highest.height,d=a7(this.chart.width-c,0,this.maxWidth);c+6>(t=s.offset?this.maxWidth/n:d/(n-1))&&(t=d/(n-(s.offset?.5:1)),e=this.maxHeight-hz(s.grid)-r.padding-hB(s.title,this.chart.options.font),i=Math.sqrt(c*c+u*u),l=Math.max(a,Math.min(o,l=180/a$*Math.min(Math.asin(a7((h.highest.height+6)/t,-1,1)),Math.asin(a7(e/i,-1,1))-Math.asin(a7(u/i,-1,1)))))),this.labelRotation=l}afterCalculateLabelRotation(){aT(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){aT(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:r}}=this,n=this._isVisible(),a=this.isHorizontal();if(n){let n=hB(s,e.options.font);if(a?(t.width=this.maxWidth,t.height=hz(r)+n):(t.height=this.maxHeight,t.width=hz(r)+n),i.display&&this.ticks.length){let{first:e,last:s,widest:r,highest:n}=this._getLabelSizes(),o=2*i.padding,l=a2(this.labelRotation),h=Math.cos(l),c=Math.sin(l);if(a){let e=i.mirror?0:c*r.width+h*n.height;t.height=Math.min(this.maxHeight,t.height+e+o)}else{let e=i.mirror?0:h*r.width+c*n.height;t.width=Math.min(this.maxWidth,t.width+e+o)}this._calculatePadding(e,s,c,h)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){let{ticks:{align:r,padding:n},position:a}=this.options,o=0!==this.labelRotation,l="top"!==a&&"x"===this.axis;if(this.isHorizontal()){let a=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1),c=0,u=0;o?l?(c=s*t.width,u=i*e.height):(c=i*t.height,u=s*e.width):"start"===r?u=e.width:"end"===r?c=t.width:"inner"!==r&&(c=t.width/2,u=e.width/2),this.paddingLeft=Math.max((c-a+n)*this.width/(this.width-a),0),this.paddingRight=Math.max((u-h+n)*this.width/(this.width-h),0)}else{let i=e.height/2,s=t.height/2;"start"===r?(i=0,s=t.height):"end"===r&&(i=e.height,s=0),this.paddingTop=i+n,this.paddingBottom=s+n}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){aT(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)ab(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=hI(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){let s,r,n,a,o,l,h,c,u,d,p,{ctx:f,_longestTextCache:m}=this,g=[],x=[],y=Math.floor(e/hF(e,i)),b=0,v=0;for(s=0;s<e;s+=y){if(a=t[s].label,f.font=l=(o=this._resolveTickFontOptions(s)).string,h=m[l]=m[l]||{data:{},gc:[]},c=o.lineHeight,u=d=0,ab(a)||av(a)){if(av(a))for(r=0,n=a.length;r<n;++r)ab(p=a[r])||av(p)||(u=oN(f,h.data,h.gc,u,p),d+=c)}else u=oN(f,h.data,h.gc,u,a),d=c;g.push(u),x.push(d),b=Math.max(u,b),v=Math.max(d,v)}aE(m,t=>{let i,s=t.gc,r=s.length/2;if(r>e){for(i=0;i<r;++i)delete t.data[s[i]];s.splice(0,r)}});let _=g.indexOf(b),w=x.indexOf(v),M=t=>({width:g[t]||0,height:x[t]||0});return{first:M(0),last:M(e-1),widest:M(_),highest:M(w),widths:g,heights:x}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return a7(this._alignToPixels?oR(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){var e,i;let s=this.ticks||[];if(t>=0&&t<s.length){let i=s[t];return i.$context||(e=this.getContext(),i.$context=o0(e,{tick:i,index:t,type:"tick"}))}return this.$context||(this.$context=(i=this.chart.getContext(),o0(i,{scale:this,type:"scale"})))}_tickSize(){let t=this.options.ticks,e=a2(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),r=this._getLabelSizes(),n=t.autoSkipPadding||0,a=r?r.widest.width+n:0,o=r?r.highest.height+n:0;return this.isHorizontal()?o*i>a*s?a/i:o/s:o*s<a*i?o/i:a/s}_isVisible(){let t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e,i,s,r,n,a,o,l,h,c,u,d,p=this.axis,f=this.chart,m=this.options,{grid:g,position:x,border:y}=m,b=g.offset,v=this.isHorizontal(),_=this.ticks.length+ +!!b,w=hz(g),M=[],P=y.setContext(this.getContext()),k=P.display?P.width:0,S=k/2,T=function(t){return oR(f,t,k)};if("top"===x)e=T(this.bottom),a=this.bottom-w,l=e-S,c=T(t.top)+S,d=t.bottom;else if("bottom"===x)e=T(this.top),c=t.top,d=T(t.bottom)-S,a=e+S,l=this.top+w;else if("left"===x)e=T(this.right),n=this.right-w,o=e-S,h=T(t.left)+S,u=t.right;else if("right"===x)e=T(this.left),h=t.left,u=T(t.right)-S,n=e+S,o=this.left+w;else if("x"===p){if("center"===x)e=T((t.top+t.bottom)/2+.5);else if(a_(x)){let t=Object.keys(x)[0],i=x[t];e=T(this.chart.scales[t].getPixelForValue(i))}c=t.top,d=t.bottom,l=(a=e+S)+w}else if("y"===p){if("center"===x)e=T((t.left+t.right)/2);else if(a_(x)){let t=Object.keys(x)[0],i=x[t];e=T(this.chart.scales[t].getPixelForValue(i))}o=(n=e-S)-w,h=t.left,u=t.right}let E=aP(m.ticks.maxTicksLimit,_),j=Math.max(1,Math.ceil(_/E));for(i=0;i<_;i+=j){let t=this.getContext(i),e=g.setContext(t),p=y.setContext(t),m=e.lineWidth,x=e.color,_=p.dash||[],w=p.dashOffset,P=e.tickWidth,k=e.tickColor,S=e.tickBorderDash||[],T=e.tickBorderDashOffset;void 0!==(s=function(t,e,i){let s,r=t.ticks.length,n=Math.min(e,r-1),a=t._startPixel,o=t._endPixel,l=t.getPixelForTick(n);if(!i||(s=1===r?Math.max(l-a,o-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(n-1))/2,!((l+=n<e?s:-s)<a-1e-6)&&!(l>o+1e-6)))return l}(this,i,b))&&(r=oR(f,s,m),v?n=o=h=u=r:a=l=c=d=r,M.push({tx1:n,ty1:a,tx2:o,ty2:l,x1:h,y1:c,x2:u,y2:d,width:m,color:x,borderDash:_,borderDashOffset:w,tickWidth:P,tickColor:k,tickBorderDash:S,tickBorderDashOffset:T}))}return this._ticksLength=_,this._borderValue=e,M}_computeLabelItems(t){let e,i,s,r,n,a,o,l,h,c,u,d=this.axis,p=this.options,{position:f,ticks:m}=p,g=this.isHorizontal(),x=this.ticks,{align:y,crossAlign:b,padding:v,mirror:_}=m,w=hz(p.grid),M=w+v,P=_?-v:M,k=-a2(this.labelRotation),S=[],T="middle";if("top"===f)n=this.bottom-P,a=this._getXAxisLabelAlignment();else if("bottom"===f)n=this.top+P,a=this._getXAxisLabelAlignment();else if("left"===f){let t=this._getYAxisLabelAlignment(w);a=t.textAlign,r=t.x}else if("right"===f){let t=this._getYAxisLabelAlignment(w);a=t.textAlign,r=t.x}else if("x"===d){if("center"===f)n=(t.top+t.bottom)/2+M;else if(a_(f)){let t=Object.keys(f)[0],e=f[t];n=this.chart.scales[t].getPixelForValue(e)+M}a=this._getXAxisLabelAlignment()}else if("y"===d){if("center"===f)r=(t.left+t.right)/2-M;else if(a_(f)){let t=Object.keys(f)[0],e=f[t];r=this.chart.scales[t].getPixelForValue(e)}a=this._getYAxisLabelAlignment(w).textAlign}"y"===d&&("start"===y?T="top":"end"===y&&(T="bottom"));let E=this._getLabelSizes();for(e=0,i=x.length;e<i;++e){let t;s=x[e].label;let d=m.setContext(this.getContext(e));o=this.getPixelForTick(e)+m.labelOffset,h=(l=this._resolveTickFontOptions(e)).lineHeight;let p=(c=av(s)?s.length:1)/2,y=d.color,v=d.textStrokeColor,w=d.textStrokeWidth,M=a;if(g?(r=o,"inner"===a&&(M=e===i-1?this.options.reverse?"left":"right":0===e?this.options.reverse?"right":"left":"center"),u="top"===f?"near"===b||0!==k?-c*h+h/2:"center"===b?-E.highest.height/2-p*h+h:-E.highest.height+h/2:"near"===b||0!==k?h/2:"center"===b?E.highest.height/2-p*h:E.highest.height-c*h,_&&(u*=-1),0===k||d.showLabelBackdrop||(r+=h/2*Math.sin(k))):(n=o,u=(1-c)*h/2),d.showLabelBackdrop){let s=oZ(d.backdropPadding),r=E.heights[e],n=E.widths[e],o=u-s.top,l=0-s.left;switch(T){case"middle":o-=r/2;break;case"bottom":o-=r}switch(a){case"center":l-=n/2;break;case"right":l-=n;break;case"inner":e===i-1?l-=n:e>0&&(l-=n/2)}t={left:l,top:o,width:n+s.width,height:r+s.height,color:d.backdropColor}}S.push({label:s,font:l,textOffset:u,options:{rotation:k,color:y,strokeColor:v,strokeWidth:w,textAlign:M,textBaseline:T,translation:[r,n],backdrop:t}})}return S}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-a2(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){let e,i,{position:s,ticks:{crossAlign:r,mirror:n,padding:a}}=this.options,o=this._getLabelSizes(),l=t+a,h=o.widest.width;return"left"===s?n?(i=this.right+a,"near"===r?e="left":"center"===r?(e="center",i+=h/2):(e="right",i+=h)):(i=this.right-l,"near"===r?e="right":"center"===r?(e="center",i-=h/2):(e="left",i=this.left)):"right"===s?n?(i=this.left+a,"near"===r?e="right":"center"===r?(e="center",i-=h/2):(e="left",i-=h)):(i=this.left+l,"near"===r?e="left":"center"===r?(e="center",i+=h/2):(e="right",i=this.right)):e="right",{textAlign:e,x:i}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:s,width:r,height:n}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,r,n),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let i=this.ticks.findIndex(e=>e.value===t);return i>=0?e.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){let e,i,s=this.options.grid,r=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),a=(t,e,i)=>{i.width&&i.color&&(r.save(),r.lineWidth=i.width,r.strokeStyle=i.color,r.setLineDash(i.borderDash||[]),r.lineDashOffset=i.borderDashOffset,r.beginPath(),r.moveTo(t.x,t.y),r.lineTo(e.x,e.y),r.stroke(),r.restore())};if(s.display)for(e=0,i=n.length;e<i;++e){let t=n[e];s.drawOnChartArea&&a({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),s.drawTicks&&a({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){let t,e,i,s,{chart:r,ctx:n,options:{border:a,grid:o}}=this,l=a.setContext(this.getContext()),h=a.display?l.width:0;if(!h)return;let c=o.setContext(this.getContext(0)).lineWidth,u=this._borderValue;this.isHorizontal()?(t=oR(r,this.left,h)-h/2,e=oR(r,this.right,c)+c/2,i=s=u):(i=oR(r,this.top,h)-h/2,s=oR(r,this.bottom,c)+c/2,t=e=u),n.save(),n.lineWidth=l.width,n.strokeStyle=l.color,n.beginPath(),n.moveTo(t,i),n.lineTo(e,s),n.stroke(),n.restore()}drawLabels(t){if(!this.options.ticks.display)return;let e=this.ctx,i=this._computeLabelArea();for(let s of(i&&oI(e,i),this.getLabelItems(t))){let t=s.options,i=s.font;oW(e,s.label,0,s.textOffset,i,t)}i&&oz(e)}drawTitle(){let t,{ctx:e,options:{position:i,title:s,reverse:r}}=this;if(!s.display)return;let n=oQ(s.font),a=oZ(s.padding),o=s.align,l=n.lineHeight/2;"bottom"===i||"center"===i||a_(i)?(l+=a.bottom,av(s.text)&&(l+=n.lineHeight*(s.text.length-1))):l+=a.top;let{titleX:h,titleY:c,maxWidth:u,rotation:d}=function(t,e,i,s){let r,n,a,{top:o,left:l,bottom:h,right:c,chart:u}=t,{chartArea:d,scales:p}=u,f=0,m=h-o,g=c-l;if(t.isHorizontal()){if(n=oc(s,l,c),a_(i)){let t=Object.keys(i)[0],s=i[t];a=p[t].getPixelForValue(s)+m-e}else a="center"===i?(d.bottom+d.top)/2+m-e:hV(t,i,e);r=c-l}else{if(a_(i)){let t=Object.keys(i)[0],s=i[t];n=p[t].getPixelForValue(s)-g+e}else n="center"===i?(d.left+d.right)/2-g+e:hV(t,i,e);a=oc(s,h,o),f="left"===i?-aY:aY}return{titleX:n,titleY:a,maxWidth:r,rotation:f}}(this,l,i,o);oW(e,s.text,0,0,n,{color:s.color,maxWidth:u,rotation:d,textAlign:(t=oh(o),(r&&"right"!==i||!r&&"right"===i)&&(t=hL(t)),t),textBaseline:"middle",translation:[h,c]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=aP(t.grid&&t.grid.z,-1),s=aP(t.border&&t.border.z,0);return this._isVisible()&&this.draw===h$.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){let e,i,s=this.chart.getSortedVisibleDatasetMetas(),r=this.axis+"AxisID",n=[];for(e=0,i=s.length;e<i;++e){let i=s[e];i[r]!==this.id||t&&i.type!==t||n.push(i)}return n}_resolveTickFontOptions(t){return oQ(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class hW{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){var e;let i,s=Object.getPrototypeOf(t);"id"in(e=s)&&"defaults"in e&&(i=this.register(s));let r=this.items,n=t.id,a=this.scope+"."+n;if(!n)throw Error("class does not have id: "+t);return n in r||(r[n]=t,function(t,e,i){var s,r;let n=aN(Object.create(null),[i?oD.get(i):{},oD.get(e),t.defaults]);oD.set(e,n),t.defaultRoutes&&(s=e,Object.keys(r=t.defaultRoutes).forEach(t=>{let e=t.split("."),i=e.pop(),n=[s].concat(e).join("."),a=r[t].split("."),o=a.pop(),l=a.join(".");oD.route(n,i,l,o)})),t.descriptors&&oD.describe(e,t.descriptors)}(t,a,i),this.override&&oD.override(t.id,t.overrides)),a}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in oD[s]&&(delete oD[s][i],this.override&&delete oT[i])}}class hH{constructor(){this.controllers=new hW(lG,"datasets",!0),this.elements=new hW(hR,"elements"),this.plugins=new hW(Object,"plugins"),this.scales=new hW(h$,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(e=>{let s=i||this._getRegistryForType(e);i||s.isForType(e)||s===this.plugins&&e.id?this._exec(t,s,e):aE(e,e=>{let s=i||this._getRegistryForType(e);this._exec(t,s,e)})})}_exec(t,e,i){let s=aF(t);aT(i["before"+s],[],i),e[t](i),aT(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let s=e.get(t);if(void 0===s)throw Error('"'+t+'" is not a registered '+i+".");return s}}var hU=new hH;class hq{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let r=s?this._descriptors(t).filter(s):this._descriptors(t),n=this._notify(r,t,e,i);return"afterDestroy"===e&&(this._notify(r,t,"stop"),this._notify(this._init,t,"uninstall")),n}_notify(t,e,i,s){for(let r of(s=s||{},t)){let t=r.plugin;if(!1===aT(t[i],[e,s,r.options],t)&&s.cancelable)return!1}return!0}invalidate(){ab(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,s=aP(i.options&&i.options.plugins,{}),r=function(t){let e={},i=[],s=Object.keys(hU.plugins.items);for(let t=0;t<s.length;t++)i.push(hU.getPlugin(s[t]));let r=t.plugins||[];for(let t=0;t<r.length;t++){let s=r[t];-1===i.indexOf(s)&&(i.push(s),e[s.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==s||e?function(t,{plugins:e,localIds:i},s,r){let n=[],a=t.getContext();for(let l of e){var o;let e=l.id,h=(o=s[e],r||!1!==o?!0===o?{}:o:null);null!==h&&n.push({plugin:l,options:function(t,{plugin:e,local:i},s,r){let n=t.pluginScopeKeys(e),a=t.getOptionScopes(s,n);return i&&e.defaults&&a.push(e.defaults),t.createResolver(a,r,[""],{scriptable:!1,indexable:!1,allKeys:!0})}(t.config,{plugin:l,local:i[e]},h,a)})}return n}(t,r,s,e):[]}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter(t=>!e.some(e=>t.plugin.id===e.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function hY(t,e){let i=oD.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function hX(t){if("x"===t||"y"===t||"r"===t)return t}function hK(t,...e){if(hX(t))return t;for(let s of e){var i;let e=s.axis||("top"===(i=s.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||t.length>1&&hX(t[0].toLowerCase());if(e)return e}throw Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function hG(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function hZ(t){let e=t.options||(t.options={});e.plugins=aP(e.plugins,{}),e.scales=function(t,e){let i=oT[t.type]||{scales:{}},s=e.scales||{},r=hY(t.type,e),n=Object.create(null);return Object.keys(s).forEach(e=>{let a=s[e];if(!a_(a))return console.error(`Invalid scale configuration for scale: ${e}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);let o=hK(e,a,function(t,e){if(e.data&&e.data.datasets){let i=e.data.datasets.filter(e=>e.xAxisID===t||e.yAxisID===t);if(i.length)return hG(t,"x",i[0])||hG(t,"y",i[0])}return{}}(e,t),oD.scales[a.type]),l=o===r?"_index_":"_value_",h=i.scales||{};n[e]=aR(Object.create(null),[{axis:o},a,h[o],h[l]])}),t.data.datasets.forEach(i=>{let r=i.type||t.type,a=i.indexAxis||hY(r,e),o=(oT[r]||{}).scales||{};Object.keys(o).forEach(t=>{let e,r=(e=t,"_index_"===t?e=a:"_value_"===t&&(e="x"===a?"y":"x"),e),l=i[r+"AxisID"]||r;n[l]=n[l]||Object.create(null),aR(n[l],[{axis:r},s[l],o[t]])})}),Object.keys(n).forEach(t=>{let e=n[t];aR(e,[oD.scales[e.type],oD.scale])}),n}(t,e)}function hQ(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}let hJ=new Map,h0=new Set;function h1(t,e){let i=hJ.get(t);return i||(i=e(),hJ.set(t,i),h0.add(i)),i}let h2=(t,e,i)=>{let s=aV(e,i);void 0!==s&&t.add(s)};class h5{constructor(t){this._config=function(t){return(t=t||{}).data=hQ(t.data),hZ(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=hQ(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),hZ(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return h1(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return h1(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return h1(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return h1(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){let{options:s,type:r}=this,n=this._cachedScopes(t,i),a=n.get(e);if(a)return a;let o=new Set;e.forEach(e=>{t&&(o.add(t),e.forEach(e=>h2(o,t,e))),e.forEach(t=>h2(o,s,t)),e.forEach(t=>h2(o,oT[r]||{},t)),e.forEach(t=>h2(o,oD,t)),e.forEach(t=>h2(o,oE,t))});let l=Array.from(o);return 0===l.length&&l.push(Object.create(null)),h0.has(e)&&n.set(e,l),l}chartOptionScopes(){let{options:t,type:e}=this;return[t,oT[e]||{},oD.datasets[e]||{},{type:e},oD,oE]}resolveNamedOptions(t,e,i,s=[""]){let r={$shared:!0},{resolver:n,subPrefixes:a}=h6(this._resolverCache,t,s),o=n;if(function(t,e){let{isScriptable:i,isIndexable:s}=o5(t);for(let r of e){let e=i(r),n=s(r),a=(n||e)&&t[r];if(e&&(az(a)||h3(a))||n&&av(a))return!0}return!1}(n,e)){r.$shared=!1,i=az(i)?i():i;let e=this.createResolver(t,i,a);o=o2(n,i,e)}for(let t of e)r[t]=o[t];return r}createResolver(t,e,i=[""],s){let{resolver:r}=h6(this._resolverCache,t,i);return a_(e)?o2(r,e,void 0,s):r}}function h6(t,e,i){let s=t.get(e);s||(s=new Map,t.set(e,s));let r=i.join(),n=s.get(r);return n||(n={resolver:o1(e,i),subPrefixes:i.filter(t=>!t.toLowerCase().includes("hover"))},s.set(r,n)),n}let h3=t=>a_(t)&&Object.getOwnPropertyNames(t).some(e=>az(t[e])),h4=["top","bottom","left","right","chartArea"];function h8(t,e){return"top"===t||"bottom"===t||-1===h4.indexOf(t)&&"x"===e}function h9(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function h7(t){let e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),aT(i&&i.onComplete,[t],e)}function ct(t){let e=t.chart,i=e.options.animation;aT(i&&i.onProgress,[t],e)}function ce(t){return lo()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}let ci={},cs=t=>{let e=ce(t);return Object.values(ci).filter(t=>t.canvas===e).pop()};class cr{static defaults=oD;static instances=ci;static overrides=oT;static registry=hU;static version="4.4.9";static getChart=cs;static register(...t){hU.add(...t),cn()}static unregister(...t){hU.remove(...t),cn()}constructor(t,e){let i=this.config=new h5(e),s=ce(t),r=cs(s);if(r)throw Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");let n=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||(!lo()||"undefined"!=typeof OffscreenCanvas&&s instanceof OffscreenCanvas?hb:hN)),this.platform.updateConfig(i);let a=this.platform.acquireContext(s,n.aspectRatio),o=a&&a.canvas,l=o&&o.height,h=o&&o.width;if(this.id=ay(),this.ctx=a,this.canvas=o,this.width=h,this.height=l,this._options=n,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new hq,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t,e){let i;return function(...s){return e?(clearTimeout(i),i=setTimeout(t,e,s)):t.apply(this,s),e}}(t=>this.update(t),n.resizeDelay||0),this._dataChanges=[],ci[this.id]=this,!a||!o)return void console.error("Failed to create chart: can't acquire context from the given item");lR.listen(this,"complete",h7),lR.listen(this,"progress",ct),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:r}=this;return ab(t)?e&&r?r:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return hU}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():lg(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return oO(this.canvas,this.ctx),this}stop(){return lR.stop(this),this}resize(t,e){lR.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,s=this.canvas,r=i.maintainAspectRatio&&this.aspectRatio,n=this.platform.getMaximumSize(s,t,e,r),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),o=this.width?"resize":"attach";this.width=n.width,this.height=n.height,this._aspectRatio=this.aspectRatio,lg(this,a,!0)&&(this.notifyPlugins("resize",{size:n}),aT(i.onResize,[this,n],this),this.attached&&this._doResize(o)&&this.render())}ensureScalesHaveIDs(){aE(this.options.scales||{},(t,e)=>{t.id=e})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((t,e)=>(t[e]=!1,t),{}),r=[];e&&(r=r.concat(Object.keys(e).map(t=>{let i=e[t],s=hK(t,i),r="r"===s,n="x"===s;return{options:i,dposition:r?"chartArea":n?"bottom":"left",dtype:r?"radialLinear":n?"category":"linear"}}))),aE(r,e=>{let r=e.options,n=r.id,a=hK(n,r),o=aP(r.type,e.dtype);(void 0===r.position||h8(r.position,a)!==h8(e.dposition))&&(r.position=e.dposition),s[n]=!0;let l=null;n in i&&i[n].type===o?l=i[n]:i[(l=new(hU.getScale(o))({id:n,type:o,ctx:this.ctx,chart:this})).id]=l,l.init(r,t)}),aE(s,(t,e)=>{t||delete i[e]}),aE(i,t=>{hx.configure(this,t,t.options),hx.addBox(this,t)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((t,e)=>t.index-e.index),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(h9("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((t,i)=>{0===e.filter(e=>e===t._dataset).length&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){let t,e,i=[],s=this.data.datasets;for(this._removeUnreferencedMetasets(),t=0,e=s.length;t<e;t++){let e=s[t],r=this.getDatasetMeta(t),n=e.type||this.config.type;if(r.type&&r.type!==n&&(this._destroyDatasetMeta(t),r=this.getDatasetMeta(t)),r.type=n,r.indexAxis=e.indexAxis||hY(n,this.options),r.order=e.order||0,r.index=t,r.label=""+e.label,r.visible=this.isDatasetVisible(t),r.controller)r.controller.updateIndex(t),r.controller.linkScales();else{let e=hU.getController(n),{datasetElementType:s,dataElementType:a}=oD.datasets[n];Object.assign(e,{dataElementType:hU.getElement(a),datasetElementType:s&&hU.getElement(s)}),r.controller=new e(this,t),i.push(r.controller)}}return this._updateMetasets(),i}_resetElements(){aE(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;let r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let n=0;for(let t=0,e=this.data.datasets.length;t<e;t++){let{controller:e}=this.getDatasetMeta(t),i=!s&&-1===r.indexOf(e);e.buildOrUpdateElements(i),n=Math.max(+e.getMaxOverflow(),n)}n=this._minPadding=i.layout.autoPadding?n:0,this._updateLayout(n),s||aE(r,t=>{t.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(h9("z","_idx"));let{_active:a,_lastEvent:o}=this;o?this._eventHandler(o,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){aE(this.scales,t=>{hx.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options;aB(new Set(Object.keys(this._listeners)),new Set(t.events))&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this;for(let{method:i,start:s,count:r}of this._getUniformDataChanges()||[]){var e="_removeElements"===i?-r:r;for(let i of Object.keys(t)){let r=+i;if(r>=s){let n=t[i];delete t[i],(e>0||r>s)&&(t[r+e]=n)}}}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=e=>new Set(t.filter(t=>t[0]===e).map((t,e)=>e+","+t.splice(1).join(","))),s=i(0);for(let t=1;t<e;t++)if(!aB(s,i(t)))return;return Array.from(s).map(t=>t.split(",")).map(t=>({method:t[1],start:+t[2],count:+t[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;hx.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],aE(this.boxes,t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))},this),this._layers.forEach((t,e)=>{t._idx=e}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,az(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(lR.has(this)?this.attached&&!lR.running(this)&&lR.start(this):(this.draw(),h7({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e,i,s=this._sortedMetasets,r=[];for(e=0,i=s.length;e<i;++e){let i=s[e];(!t||i.visible)&&r.push(i)}return r}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i={meta:t,index:t.index,cancelable:!0},s=lD(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",i)&&(s&&oI(e,s),t.controller.draw(),s&&oz(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return oF(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){let r=ho.modes[e];return"function"==typeof r?r(this,t,i,s):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,s=i.filter(t=>t&&t._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=o0(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let s=i?"show":"hide",r=this.getDatasetMeta(t),n=r.controller._resolveAnimations(void 0,s);aI(e)?(r.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),n.update(r,{visible:i}),this.update(e=>e.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),lR.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),oO(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ci[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};aE(this.options.events,t=>i(t,s))}bindResponsiveEvents(){let t;this._responsiveListeners||(this._responsiveListeners={});let e=this._responsiveListeners,i=this.platform,s=(t,s)=>{i.addEventListener(this,t,s),e[t]=s},r=(t,s)=>{e[t]&&(i.removeEventListener(this,t,s),delete e[t])},n=(t,e)=>{this.canvas&&this.resize(t,e)},a=()=>{r("attach",a),this.attached=!0,this.resize(),s("resize",n),s("detach",t)};t=()=>{this.attached=!1,r("resize",n),this._stop(),this._resize(0,0),s("attach",a)},i.isAttached(this.canvas)?a():t()}unbindEvents(){aE(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},aE(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let s,r,n,a=i?"set":"remove";for("dataset"===e&&this.getDatasetMeta(t[0].datasetIndex).controller["_"+a+"DatasetHoverStyle"](),r=0,n=t.length;r<n;++r){let e=(s=t[r])&&this.getDatasetMeta(s.datasetIndex).controller;e&&e[a+"HoverStyle"](s.element,s.datasetIndex,s.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:t,index:e})=>{let i=this.getDatasetMeta(t);if(!i)throw Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}});aj(i,e)||(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter(e=>e.plugin.id===t).length}_updateHoverStyles(t,e,i){let s=this.options.hover,r=(t,e)=>t.filter(t=>!e.some(e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)),n=r(e,t),a=i?t:r(t,e);n.length&&this.updateHoverStyle(n,s.mode,!1),a.length&&s.mode&&this.updateHoverStyle(a,s.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;let r=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(r||i.changed)&&this.render(),this}_handleEvent(t,e,i){var s;let{_active:r=[],options:n}=this,a=this._getActiveElements(t,r,i,e),o="mouseup"===t.type||"click"===t.type||"contextmenu"===t.type,l=(s=this._lastEvent,i&&"mouseout"!==t.type?o?s:t:null);i&&(this._lastEvent=null,aT(n.onHover,[t,a,this],this),o&&aT(n.onClick,[t,a,this],this));let h=!aj(a,r);return(h||e)&&(this._active=a,this._updateHoverStyles(a,r,e)),this._lastEvent=l,h}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;let r=this.options.hover;return this.getElementsAtEventForMode(t,r.mode,r,s)}}function cn(){return aE(cr.instances,t=>t._plugins.invalidate())}function ca(t,e,i,s){return{x:i+t*Math.cos(e),y:s+t*Math.sin(e)}}function co(t,e,i,s,r,n){let{x:a,y:o,startAngle:l,pixelMargin:h,innerRadius:c}=e,u=Math.max(e.outerRadius+s+i-h,0),d=c>0?c+s+i+h:0,p=0,f=r-l;if(s){let t=u>0?u-s:0,e=((c>0?c-s:0)+t)/2;p=(f-(0!==e?f*e/(e+s):f))/2}let m=Math.max(.001,f*u-i/a$)/u,g=(f-m)/2,x=l+g+p,y=r-g-p,{outerStart:b,outerEnd:v,innerStart:_,innerEnd:w}=function(t,e,i,s){let r=oX(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]),n=(i-e)/2,a=Math.min(n,s*e/2),o=t=>{let e=(i-Math.min(n,t))*s/2;return a7(t,0,Math.min(n,e))};return{outerStart:o(r.outerStart),outerEnd:o(r.outerEnd),innerStart:a7(r.innerStart,0,a),innerEnd:a7(r.innerEnd,0,a)}}(e,d,u,y-x),M=u-b,P=u-v,k=x+b/M,S=y-v/P,T=d+_,E=d+w,j=x+_/T,A=y-w/E;if(t.beginPath(),n){let e=(k+S)/2;if(t.arc(a,o,u,k,e),t.arc(a,o,u,e,S),v>0){let e=ca(P,S,a,o);t.arc(e.x,e.y,v,S,y+aY)}let i=ca(E,y,a,o);if(t.lineTo(i.x,i.y),w>0){let e=ca(E,A,a,o);t.arc(e.x,e.y,w,y+aY,A+Math.PI)}let s=(y-w/d+(x+_/d))/2;if(t.arc(a,o,d,y-w/d,s,!0),t.arc(a,o,d,s,x+_/d,!0),_>0){let e=ca(T,j,a,o);t.arc(e.x,e.y,_,j+Math.PI,x-aY)}let r=ca(M,x,a,o);if(t.lineTo(r.x,r.y),b>0){let e=ca(M,k,a,o);t.arc(e.x,e.y,b,x-aY,k)}}else{t.moveTo(a,o);let e=Math.cos(k)*u+a,i=Math.sin(k)*u+o;t.lineTo(e,i);let s=Math.cos(S)*u+a,r=Math.sin(S)*u+o;t.lineTo(s,r)}t.closePath()}class cl extends hR{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){let{angle:s,distance:r}=a6(this.getProps(["x","y"],i),{x:t,y:e}),{startAngle:n,endAngle:a,innerRadius:o,outerRadius:l,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),c=(this.options.spacing+this.options.borderWidth)/2,u=aP(h,a-n),d=a9(s,n,a)&&n!==a,p=u>=aW||d,f=ot(r,o+c,l+c);return p&&f}getCenterPoint(t){let{x:e,y:i,startAngle:s,endAngle:r,innerRadius:n,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:o,spacing:l}=this.options,h=(s+r)/2,c=(n+a+l+o)/2;return{x:e+Math.cos(h)*c,y:i+Math.sin(h)*c}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){let{options:e,circumference:i}=this,s=(e.offset||0)/4,r=(e.spacing||0)/2,n=e.circular;if(this.pixelMargin=.33*("inner"===e.borderAlign),this.fullCircles=i>aW?Math.floor(i/aW):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();let a=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(a)*s,Math.sin(a)*s);let o=s*(1-Math.sin(Math.min(a$,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,function(t,e,i,s,r){let{fullCircles:n,startAngle:a,circumference:o}=e,l=e.endAngle;if(n){co(t,e,i,s,l,r);for(let e=0;e<n;++e)t.fill();isNaN(o)||(l=a+(o%aW||aW))}co(t,e,i,s,l,r),t.fill()}(t,this,o,r,n),function(t,e,i,s,r){let{fullCircles:n,startAngle:a,circumference:o,options:l}=e,{borderWidth:h,borderJoinStyle:c,borderDash:u,borderDashOffset:d}=l,p="inner"===l.borderAlign;if(!h)return;t.setLineDash(u||[]),t.lineDashOffset=d,p?(t.lineWidth=2*h,t.lineJoin=c||"round"):(t.lineWidth=h,t.lineJoin=c||"bevel");let f=e.endAngle;if(n){co(t,e,i,s,f,r);for(let e=0;e<n;++e)t.stroke();isNaN(o)||(f=a+(o%aW||aW))}p&&function(t,e,i){let{startAngle:s,pixelMargin:r,x:n,y:a,outerRadius:o,innerRadius:l}=e,h=r/o;t.beginPath(),t.arc(n,a,o,s-h,i+h),l>r?(h=r/l,t.arc(n,a,l,i+h,s-h,!0)):t.arc(n,a,r,i+aY,s-aY),t.closePath(),t.clip()}(t,e,f),n||(co(t,e,i,s,f,r),t.stroke())}(t,this,o,r,n),t.restore()}}function ch(t,e,i=e){t.lineCap=aP(i.borderCapStyle,e.borderCapStyle),t.setLineDash(aP(i.borderDash,e.borderDash)),t.lineDashOffset=aP(i.borderDashOffset,e.borderDashOffset),t.lineJoin=aP(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=aP(i.borderWidth,e.borderWidth),t.strokeStyle=aP(i.borderColor,e.borderColor)}function cc(t,e,i){t.lineTo(i.x,i.y)}function cu(t,e,i={}){let s=t.length,{start:r=0,end:n=s-1}=i,{start:a,end:o}=e,l=Math.max(r,a),h=Math.min(n,o);return{count:s,start:l,loop:e.loop,ilen:h<l&&!(r<a&&n<a||r>o&&n>o)?s+h-l:h-l}}function cd(t,e,i,s){let r,n,a,{points:o,options:l}=e,{count:h,start:c,loop:u,ilen:d}=cu(o,i,s),p=l.stepped?oB:l.tension||"monotone"===l.cubicInterpolationMode?o$:cc,{move:f=!0,reverse:m}=s||{};for(r=0;r<=d;++r)(n=o[(c+(m?d-r:r))%h]).skip||(f?(t.moveTo(n.x,n.y),f=!1):p(t,a,n,m,l.stepped),a=n);return u&&p(t,a,n=o[(c+(m?d:0))%h],m,l.stepped),!!u}function cp(t,e,i,s){let r,n,a,o,l,h,c=e.points,{count:u,start:d,ilen:p}=cu(c,i,s),{move:f=!0,reverse:m}=s||{},g=0,x=0,y=t=>(d+(m?p-t:t))%u,b=()=>{o!==l&&(t.lineTo(g,l),t.lineTo(g,o),t.lineTo(g,h))};for(f&&(n=c[y(0)],t.moveTo(n.x,n.y)),r=0;r<=p;++r){if((n=c[y(r)]).skip)continue;let e=n.x,i=n.y,s=0|e;s===a?(i<o?o=i:i>l&&(l=i),g=(x*g+e)/++x):(b(),t.lineTo(e,i),a=s,x=0,o=l=i),h=i}b()}function cf(t){let e=t.options,i=e.borderDash&&e.borderDash.length;return t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i?cd:cp}let cm="function"==typeof Path2D;class cg extends hR{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){let s=i.spanGaps?this._loop:this._fullLoop;!function(t,e,i,s,r){let n,a,o,l;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let i,s,r,n=ln(e),a=t.length,o=Array(a).fill(0),l=Array(a),h=lr(t,0);for(i=0;i<a;++i)if(s=r,r=h,h=lr(t,i+1),r){if(h){let t=h[e]-r[e];o[i]=0!==t?(h[n]-r[n])/t:0}l[i]=s?h?aZ(o[i-1])!==aZ(o[i])?0:(o[i-1]+o[i])/2:o[i-1]:o[i]}!function(t,e,i){let s,r,n,a,o,l=t.length,h=lr(t,0);for(let c=0;c<l-1;++c)if(o=h,h=lr(t,c+1),o&&h){if(aQ(e[c],0,ls)){i[c]=i[c+1]=0;continue}(a=Math.pow(s=i[c]/e[c],2)+Math.pow(r=i[c+1]/e[c],2))<=9||(n=3/Math.sqrt(a),i[c]=s*n*e[c],i[c+1]=r*n*e[c])}}(t,o,l),function(t,e,i="x"){let s,r,n,a=ln(i),o=t.length,l=lr(t,0);for(let h=0;h<o;++h){if(r=n,n=l,l=lr(t,h+1),!n)continue;let o=n[i],c=n[a];r&&(s=(o-r[i])/3,n[`cp1${i}`]=o-s,n[`cp1${a}`]=c-s*e[h]),l&&(s=(l[i]-o)/3,n[`cp2${i}`]=o+s,n[`cp2${a}`]=c+s*e[h])}}(t,l,e)}(t,r);else{let i=s?t[t.length-1]:t[0];for(n=0,a=t.length;n<a;++n)l=function(t,e,i,s){let r=t.skip?e:t,n=i.skip?e:i,a=a3(e,r),o=a3(n,e),l=a/(a+o),h=o/(a+o);l=isNaN(l)?0:l,h=isNaN(h)?0:h;let c=s*l,u=s*h;return{previous:{x:e.x-c*(n.x-r.x),y:e.y-c*(n.y-r.y)},next:{x:e.x+u*(n.x-r.x),y:e.y+u*(n.y-r.y)}}}(i,o=t[n],t[Math.min(n+1,a-!s)%a],e.tension),o.cp1x=l.previous.x,o.cp1y=l.previous.y,o.cp2x=l.next.x,o.cp2y=l.next.y,i=o}e.capBezierPoints&&function(t,e){let i,s,r,n,a,o=oF(t[0],e);for(i=0,s=t.length;i<s;++i)a=n,n=o,o=i<s-1&&oF(t[i+1],e),n&&(r=t[i],a&&(r.cp1x=la(r.cp1x,e.left,e.right),r.cp1y=la(r.cp1y,e.top,e.bottom)),o&&(r.cp2x=la(r.cp2x,e.left,e.right),r.cp2y=la(r.cp2y,e.top,e.bottom)))}(t,i)}(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){let i=t.points,s=t.options.spanGaps,r=i.length;if(!r)return[];let n=!!t._loop,{start:a,end:o}=function(t,e,i,s){let r=0,n=e-1;if(i&&!s)for(;r<e&&!t[r].skip;)r++;for(;r<e&&t[r].skip;)r++;for(r%=e,i&&(n+=r);n>r&&t[n%e].skip;)n--;return{start:r,end:n%=e}}(i,r,n,s);if(!0===s)return lj(t,[{start:a,end:o,loop:n}],i,e);let l=o<a?o+r:o,h=!!t._fullLoop&&0===a&&o===r-1;return lj(t,function(t,e,i,s){let r,n=t.length,a=[],o=e,l=t[e];for(r=e+1;r<=i;++r){let i=t[r%n];i.skip||i.stop?l.skip||(s=!1,a.push({start:e%n,end:(r-1)%n,loop:s}),e=o=i.stop?r:null):(o=r,l.skip&&(e=r)),l=i}return null!==o&&a.push({start:e%n,end:o%n,loop:s}),a}(i,a,l,h),i,e)}(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i,s,r=this.options,n=t[e],a=this.points,o=lE(this,{property:e,start:n,end:n});if(!o.length)return;let l=[],h=r.stepped?lv:r.tension||"monotone"===r.cubicInterpolationMode?l_:lb;for(i=0,s=o.length;i<s;++i){let{start:s,end:c}=o[i],u=a[s],d=a[c];if(u===d){l.push(u);continue}let p=Math.abs((n-u[e])/(d[e]-u[e])),f=h(u,d,p,r.stepped);f[e]=t[e],l.push(f)}return 1===l.length?l[0]:l}pathSegment(t,e,i){return cf(this)(t,this,e,i)}path(t,e,i){let s=this.segments,r=cf(this),n=this._loop;for(let a of(e=e||0,i=i||this.points.length-e,s))n&=r(t,this,a,{start:e,end:e+i-1});return!!n}draw(t,e,i,s){let r=this.options||{};(this.points||[]).length&&r.borderWidth&&(t.save(),function(t,e,i,s){if(cm&&!e.options.segment){let r;(r=e._path)||(r=e._path=new Path2D,e.path(r,i,s)&&r.closePath()),ch(t,e.options),t.stroke(r)}else{let{segments:r,options:n}=e,a=cf(e);for(let o of r)ch(t,n,o.style),t.beginPath(),a(t,e,o,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}}(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function cx(t,e,i,s){let r=t.options,{[i]:n}=t.getProps([i],s);return Math.abs(e-n)<r.radius+r.hitRadius}class cy extends hR{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.options,{x:r,y:n}=this.getProps(["x","y"],i);return Math.pow(t-r,2)+Math.pow(e-n,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return cx(this,t,"x",e)}inYRange(t,e){return cx(this,t,"y",e)}getCenterPoint(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0,i=(e=Math.max(e,e&&t.hoverRadius||0))&&t.borderWidth||0;return(e+i)*2}draw(t,e){let i=this.options;!this.skip&&!(i.radius<.1)&&oF(this,e,this.size(i)/2)&&(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,oL(t,i,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}}function cb(t,e){let i,s,r,n,a,{x:o,y:l,base:h,width:c,height:u}=t.getProps(["x","y","base","width","height"],e);return t.horizontal?(a=u/2,i=Math.min(o,h),s=Math.max(o,h),r=l-a,n=l+a):(i=o-(a=c/2),s=o+a,r=Math.min(l,h),n=Math.max(l,h)),{left:i,top:r,right:s,bottom:n}}function cv(t,e,i,s){return t?0:a7(e,i,s)}function c_(t,e,i,s){let r=null===e,n=null===i,a=t&&!(r&&n)&&cb(t,s);return a&&(r||ot(e,a.left,a.right))&&(n||ot(i,a.top,a.bottom))}function cw(t,e){t.rect(e.x,e.y,e.w,e.h)}function cM(t,e,i={}){let s=t.x!==i.x?-e:0,r=t.y!==i.y?-e:0,n=(t.x+t.w!==i.x+i.w?e:0)-s,a=(t.y+t.h!==i.y+i.h?e:0)-r;return{x:t.x+s,y:t.y+r,w:t.w+n,h:t.h+a,radius:t.radius}}class cP extends hR{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){var e;let{inflateAmount:i,options:{borderColor:s,backgroundColor:r}}=this,{inner:n,outer:a}=function(t){let e=cb(t),i=e.right-e.left,s=e.bottom-e.top,r=function(t,e,i){let s=t.options.borderWidth,r=t.borderSkipped,n=oK(s);return{t:cv(r.top,n.top,0,i),r:cv(r.right,n.right,0,e),b:cv(r.bottom,n.bottom,0,i),l:cv(r.left,n.left,0,e)}}(t,i/2,s/2),n=function(t,e,i){let{enableBorderRadius:s}=t.getProps(["enableBorderRadius"]),r=t.options.borderRadius,n=oG(r),a=Math.min(e,i),o=t.borderSkipped,l=s||a_(r);return{topLeft:cv(!l||o.top||o.left,n.topLeft,0,a),topRight:cv(!l||o.top||o.right,n.topRight,0,a),bottomLeft:cv(!l||o.bottom||o.left,n.bottomLeft,0,a),bottomRight:cv(!l||o.bottom||o.right,n.bottomRight,0,a)}}(t,i/2,s/2);return{outer:{x:e.left,y:e.top,w:i,h:s,radius:n},inner:{x:e.left+r.l,y:e.top+r.t,w:i-r.l-r.r,h:s-r.t-r.b,radius:{topLeft:Math.max(0,n.topLeft-Math.max(r.t,r.l)),topRight:Math.max(0,n.topRight-Math.max(r.t,r.r)),bottomLeft:Math.max(0,n.bottomLeft-Math.max(r.b,r.l)),bottomRight:Math.max(0,n.bottomRight-Math.max(r.b,r.r))}}}}(this),o=(e=a.radius).topLeft||e.topRight||e.bottomLeft||e.bottomRight?oH:cw;t.save(),(a.w!==n.w||a.h!==n.h)&&(t.beginPath(),o(t,cM(a,i,n)),t.clip(),o(t,cM(n,-i,a)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),o(t,cM(n,i)),t.fillStyle=r,t.fill(),t.restore()}inRange(t,e,i){return c_(this,t,e,i)}inXRange(t,e){return c_(this,t,null,e)}inYRange(t,e){return c_(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:s,horizontal:r}=this.getProps(["x","y","base","horizontal"],t);return{x:r?(e+s)/2:e,y:r?i:(i+s)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}let ck=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],cS=ck.map(t=>t.replace("rgb(","rgba(").replace(")",", 0.5)"));function cT(t,e,i,s){if(s)return;let r=e[t],n=i[t];return"angle"===t&&(r=a8(r),n=a8(n)),{property:t,start:r,end:n}}function cE(t,e,i){for(;e>t;e--){let t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function cj(t,e,i,s){return t&&e?s(t[i],e[i]):t?t[i]:e?e[i]:0}function cA(t,e){let i=[],s=!1;return av(t)?(s=!0,i=t):i=function(t,e){let{x:i=null,y:s=null}=t||{},r=e.points,n=[];return e.segments.forEach(({start:t,end:e})=>{e=cE(t,e,r);let a=r[t],o=r[e];null!==s?(n.push({x:a.x,y:s}),n.push({x:o.x,y:s})):null!==i&&(n.push({x:i,y:a.y}),n.push({x:i,y:o.y}))}),n}(t,e),i.length?new cg({points:i,options:{tension:0},_loop:s,_fullLoop:s}):null}class cC{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){let{x:s,y:r,radius:n}=this;return e=e||{start:0,end:aW},t.arc(s,r,n,e.end,e.start,!0),!i.bounds}interpolate(t){let{x:e,y:i,radius:s}=this,r=t.angle;return{x:e+Math.cos(r)*s,y:i+Math.sin(r)*s,angle:r}}}function cD(t,e,i){let{segments:s,points:r}=e,n=!0,a=!1;for(let o of(t.beginPath(),s)){let{start:s,end:l}=o,h=r[s],c=r[cE(s,l,r)];n?(t.moveTo(h.x,h.y),n=!1):(t.lineTo(h.x,i),t.lineTo(h.x,h.y)),(a=!!e.pathSegment(t,o,{move:a}))?t.closePath():t.lineTo(c.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function cN(t,e){let{line:i,target:s,property:r,color:n,scale:a,clip:o}=e;for(let{source:e,target:l,start:h,end:c}of function(t,e,i){let s=t.segments,r=t.points,n=e.points,a=[];for(let t of s){let{start:s,end:o}=t;o=cE(s,o,r);let l=cT(i,r[s],r[o],t.loop);if(!e.segments){a.push({source:t,target:l,start:r[s],end:r[o]});continue}for(let s of lE(e,l)){let e=cT(i,n[s.start],n[s.end],s.loop);for(let n of lT(t,r,e))a.push({source:n,target:s,start:{[i]:cj(l,e,"start",Math.max)},end:{[i]:cj(l,e,"end",Math.min)}})}}return a}(i,s,r)){let u,{style:{backgroundColor:d=n}={}}=e,p=!0!==s;t.save(),t.fillStyle=d,function(t,e,i,s){let r=e.chart.chartArea,{property:n,start:a,end:o}=s||{};if("x"===n||"y"===n){let e,s,l,h;"x"===n?(e=a,s=r.top,l=o,h=r.bottom):(e=r.left,s=a,l=r.right,h=o),t.beginPath(),i&&(e=Math.max(e,i.left),l=Math.min(l,i.right),s=Math.max(s,i.top),h=Math.min(h,i.bottom)),t.rect(e,s,l-e,h-s),t.clip()}}(t,a,o,p&&cT(r,h,c)),t.beginPath();let f=!!i.pathSegment(t,e);if(p){f?t.closePath():cR(t,s,c,r);let e=!!s.pathSegment(t,l,{move:f,reverse:!0});(u=f&&e)||cR(t,s,h,r)}t.closePath(),t.fill(u?"evenodd":"nonzero"),t.restore()}}function cR(t,e,i,s){let r=e.interpolate(i,s);r&&t.lineTo(r.x,r.y)}let cO=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}},cL=(t,e)=>null!==t&&null!==e&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class cV extends hR{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=aT(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(e=>t.filter(e,this.chart.data))),t.sort&&(e=e.sort((e,i)=>t.sort(e,i,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let t,e,{options:i,ctx:s}=this;if(!i.display){this.width=this.height=0;return}let r=i.labels,n=oQ(r.font),a=n.size,o=this._computeTitleHeight(),{boxWidth:l,itemHeight:h}=cO(r,a);s.font=n.string,this.isHorizontal()?(t=this.maxWidth,e=this._fitRows(o,a,l,h)+10):(e=this.maxHeight,t=this._fitCols(o,n,l,h)+10),this.width=Math.min(t,i.maxWidth||this.maxWidth),this.height=Math.min(e,i.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){let{ctx:r,maxWidth:n,options:{labels:{padding:a}}}=this,o=this.legendHitBoxes=[],l=this.lineWidths=[0],h=s+a,c=t;r.textAlign="left",r.textBaseline="middle";let u=-1,d=-h;return this.legendItems.forEach((t,p)=>{let f=i+e/2+r.measureText(t.text).width;(0===p||l[l.length-1]+f+2*a>n)&&(c+=h,l[l.length-(p>0?0:1)]=0,d+=h,u++),o[p]={left:0,top:d,row:u,width:f,height:s},l[l.length-1]+=f+a}),c}_fitCols(t,e,i,s){let{ctx:r,maxHeight:n,options:{labels:{padding:a}}}=this,o=this.legendHitBoxes=[],l=this.columnSizes=[],h=n-t,c=a,u=0,d=0,p=0,f=0;return this.legendItems.forEach((t,n)=>{var m,g,x,y,b,v,_,w,M,P,k,S;let T,E,{itemWidth:j,itemHeight:A}=(m=i,g=e,x=r,y=t,b=s,{itemWidth:(v=y,_=m,w=g,M=x,(T=v.text)&&"string"!=typeof T&&(T=T.reduce((t,e)=>t.length>e.length?t:e)),_+w.size/2+M.measureText(T).width),itemHeight:(P=b,k=y,S=g.lineHeight,E=P,"string"!=typeof k.text&&(E=cF(k,S)),E)});n>0&&d+A+2*a>h&&(c+=u+a,l.push({width:u,height:d}),p+=u+a,f++,u=d=0),o[n]={left:p,top:d,col:f,width:j,height:A},u=Math.max(u,j),d+=A+a}),c+=u,l.push({width:u,height:d}),c}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:r}}=this,n=lw(r,this.left,this.width);if(this.isHorizontal()){let r=0,a=oc(i,this.left+s,this.right-this.lineWidths[r]);for(let o of e)r!==o.row&&(r=o.row,a=oc(i,this.left+s,this.right-this.lineWidths[r])),o.top+=this.top+t+s,o.left=n.leftForLtr(n.x(a),o.width),a+=o.width+s}else{let r=0,a=oc(i,this.top+t+s,this.bottom-this.columnSizes[r].height);for(let o of e)o.col!==r&&(r=o.col,a=oc(i,this.top+t+s,this.bottom-this.columnSizes[r].height)),o.top=a,o.left+=this.left+s,o.left=n.leftForLtr(n.x(o.left),o.width),a+=o.height+s}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){let t=this.ctx;oI(t,this),this._draw(),oz(t)}}_draw(){let t,{options:e,columnSizes:i,lineWidths:s,ctx:r}=this,{align:n,labels:a}=e,o=oD.color,l=lw(e.rtl,this.left,this.width),h=oQ(a.font),{padding:c}=a,u=h.size,d=u/2;this.drawTitle(),r.textAlign=l.textAlign("left"),r.textBaseline="middle",r.lineWidth=.5,r.font=h.string;let{boxWidth:p,boxHeight:f,itemHeight:m}=cO(a,u),g=function(t,e,i){if(isNaN(p)||p<=0||isNaN(f)||f<0)return;r.save();let s=aP(i.lineWidth,1);if(r.fillStyle=aP(i.fillStyle,o),r.lineCap=aP(i.lineCap,"butt"),r.lineDashOffset=aP(i.lineDashOffset,0),r.lineJoin=aP(i.lineJoin,"miter"),r.lineWidth=s,r.strokeStyle=aP(i.strokeStyle,o),r.setLineDash(aP(i.lineDash,[])),a.usePointStyle){let n={radius:f*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:s};oV(r,n,l.xPlus(t,p/2),e+d,a.pointStyleWidth&&p)}else{let n=e+Math.max((u-f)/2,0),a=l.leftForLtr(t,p),o=oG(i.borderRadius);r.beginPath(),Object.values(o).some(t=>0!==t)?oH(r,{x:a,y:n,w:p,h:f,radius:o}):r.rect(a,n,p,f),r.fill(),0!==s&&r.stroke()}r.restore()},x=function(t,e,i){oW(r,i.text,t,e+m/2,h,{strikethrough:i.hidden,textAlign:l.textAlign(i.textAlign)})},y=this.isHorizontal(),b=this._computeTitleHeight();t=y?{x:oc(n,this.left+c,this.right-s[0]),y:this.top+c+b,line:0}:{x:this.left+c,y:oc(n,this.top+b+c,this.bottom-i[0].height),line:0},lM(this.ctx,e.textDirection);let v=m+c;this.legendItems.forEach((o,u)=>{r.strokeStyle=o.fontColor,r.fillStyle=o.fontColor;let f=r.measureText(o.text).width,m=l.textAlign(o.textAlign||(o.textAlign=a.textAlign)),_=p+d+f,w=t.x,M=t.y;if(l.setWidth(this.width),y?u>0&&w+_+c>this.right&&(M=t.y+=v,t.line++,w=t.x=oc(n,this.left+c,this.right-s[t.line])):u>0&&M+v>this.bottom&&(w=t.x=w+i[t.line].width+c,t.line++,M=t.y=oc(n,this.top+b+c,this.bottom-i[t.line].height)),g(l.x(w),M,o),w=ou(m,w+p+d,y?w+_:this.right,e.rtl),x(l.x(w),M,o),y)t.x+=_+c;else if("string"!=typeof o.text){let e=h.lineHeight;t.y+=cF(o,e)+c}else t.y+=v}),lP(this.ctx,e.textDirection)}drawTitle(){let t,e=this.options,i=e.title,s=oQ(i.font),r=oZ(i.padding);if(!i.display)return;let n=lw(e.rtl,this.left,this.width),a=this.ctx,o=i.position,l=s.size/2,h=r.top+l,c=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),t=this.top+h,c=oc(e.align,c,this.right-u);else{let i=this.columnSizes.reduce((t,e)=>Math.max(t,e.height),0);t=h+oc(e.align,this.top,this.bottom-i-e.labels.padding-this._computeTitleHeight())}let d=oc(o,c,c+u);a.textAlign=n.textAlign(oh(o)),a.textBaseline="middle",a.strokeStyle=i.color,a.fillStyle=i.color,a.font=s.string,oW(a,i.text,d,t,s)}_computeTitleHeight(){let t=this.options.title,e=oQ(t.font),i=oZ(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,r;if(ot(t,this.left,this.right)&&ot(e,this.top,this.bottom)){for(i=0,r=this.legendHitBoxes;i<r.length;++i)if(ot(t,(s=r[i]).left,s.left+s.width)&&ot(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){var e,i;let s=this.options;if(e=t.type,i=s,("mousemove"!==e&&"mouseout"!==e||!i.onHover&&!i.onLeave)&&(!i.onClick||"click"!==e&&"mouseup"!==e))return;let r=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){let e=this._hoveredItem,i=cL(e,r);e&&!i&&aT(s.onLeave,[t,e,this],this),this._hoveredItem=r,r&&!i&&aT(s.onHover,[t,r,this],this)}else r&&aT(s.onClick,[t,r,this],this)}}function cF(t,e){return e*(t.text?t.text.length:0)}class cI extends hR{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){let i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;let s=av(i.text)?i.text.length:1;this._padding=oZ(i.padding);let r=s*oQ(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){let t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){let e,i,s,{top:r,left:n,bottom:a,right:o,options:l}=this,h=l.align,c=0;return this.isHorizontal()?(i=oc(h,n,o),s=r+t,e=o-n):("left"===l.position?(i=n+t,s=oc(h,a,r),c=-.5*a$):(i=o-t,s=oc(h,r,a),c=.5*a$),e=a-r),{titleX:i,titleY:s,maxWidth:e,rotation:c}}draw(){let t=this.ctx,e=this.options;if(!e.display)return;let i=oQ(e.font),s=i.lineHeight/2+this._padding.top,{titleX:r,titleY:n,maxWidth:a,rotation:o}=this._drawArgs(s);oW(t,e.text,0,0,i,{color:e.color,maxWidth:a,rotation:o,textAlign:oh(e.align),textBaseline:"middle",translation:[r,n]})}}new WeakMap;let cz={average(t){let e,i;if(!t.length)return!1;let s=new Set,r=0,n=0;for(e=0,i=t.length;e<i;++e){let i=t[e].element;if(i&&i.hasValue()){let t=i.tooltipPosition();s.add(t.x),r+=t.y,++n}}return 0!==n&&0!==s.size&&{x:[...s].reduce((t,e)=>t+e)/s.size,y:r/n}},nearest(t,e){let i,s,r;if(!t.length)return!1;let n=e.x,a=e.y,o=Number.POSITIVE_INFINITY;for(i=0,s=t.length;i<s;++i){let s=t[i].element;if(s&&s.hasValue()){let t=a3(e,s.getCenterPoint());t<o&&(o=t,r=s)}}if(r){let t=r.tooltipPosition();n=t.x,a=t.y}return{x:n,y:a}}};function cB(t,e){return e&&(av(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function c$(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function cW(t,e){let i=t.chart.ctx,{body:s,footer:r,title:n}=t,{boxWidth:a,boxHeight:o}=e,l=oQ(e.bodyFont),h=oQ(e.titleFont),c=oQ(e.footerFont),u=n.length,d=r.length,p=s.length,f=oZ(e.padding),m=f.height,g=0,x=s.reduce((t,e)=>t+e.before.length+e.lines.length+e.after.length,0);x+=t.beforeBody.length+t.afterBody.length,u&&(m+=u*h.lineHeight+(u-1)*e.titleSpacing+e.titleMarginBottom),x&&(m+=p*(e.displayColors?Math.max(o,l.lineHeight):l.lineHeight)+(x-p)*l.lineHeight+(x-1)*e.bodySpacing),d&&(m+=e.footerMarginTop+d*c.lineHeight+(d-1)*e.footerSpacing);let y=0,b=function(t){g=Math.max(g,i.measureText(t).width+y)};return i.save(),i.font=h.string,aE(t.title,b),i.font=l.string,aE(t.beforeBody.concat(t.afterBody),b),y=e.displayColors?a+2+e.boxPadding:0,aE(s,t=>{aE(t.before,b),aE(t.lines,b),aE(t.after,b)}),y=0,i.font=c.string,aE(t.footer,b),i.restore(),{width:g+=f.width,height:m}}function cH(t,e,i){let s=i.yAlign||e.yAlign||function(t,e){let{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||function(t,e,i,s){let{x:r,width:n}=i,{width:a,chartArea:{left:o,right:l}}=t,h="center";return"center"===s?h=r<=(o+l)/2?"left":"right":r<=n/2?h="left":r>=a-n/2&&(h="right"),function(t,e,i,s){let{x:r,width:n}=s,a=i.caretSize+i.caretPadding;if("left"===t&&r+n+a>e.width||"right"===t&&r-n-a<0)return!0}(h,t,e,i)&&(h="center"),h}(t,e,i,s),yAlign:s}}function cU(t,e,i,s){let{caretSize:r,caretPadding:n,cornerRadius:a}=t,{xAlign:o,yAlign:l}=i,h=r+n,{topLeft:c,topRight:u,bottomLeft:d,bottomRight:p}=oG(a),f=function(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}(e,o),m=function(t,e,i){let{y:s,height:r}=t;return"top"===e?s+=i:"bottom"===e?s-=r+i:s-=r/2,s}(e,l,h);return"center"===l?"left"===o?f+=h:"right"===o&&(f-=h):"left"===o?f-=Math.max(c,d)+r:"right"===o&&(f+=Math.max(u,p)+r),{x:a7(f,0,s.width-e.width),y:a7(m,0,s.height-e.height)}}function cq(t,e,i){let s=oZ(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-s.right:t.x+s.left}function cY(t,e){let i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}let cX={beforeTitle:ax,title(t){if(t.length>0){let e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:ax,beforeBody:ax,beforeLabel:ax,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");let i=t.formattedValue;return ab(i)||(e+=i),e},labelColor(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:ax,afterBody:ax,beforeFooter:ax,footer:ax,afterFooter:ax};function cK(t,e,i,s){let r=t[e].call(i,s);return void 0===r?cX[e].call(i,s):r}class cG extends hR{static positioners=cz;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,r=new lF(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){var t;return this.$context||(this.$context=(t=this.chart.getContext(),o0(t,{tooltip:this,tooltipItems:this._tooltipItems,type:"tooltip"})))}getTitle(t,e){let{callbacks:i}=e,s=cK(i,"beforeTitle",this,t),r=cK(i,"title",this,t),n=cK(i,"afterTitle",this,t),a=[];return a=cB(a,c$(s)),a=cB(a,c$(r)),a=cB(a,c$(n))}getBeforeBody(t,e){return cB([],c$(cK(e.callbacks,"beforeBody",this,t)))}getBody(t,e){let{callbacks:i}=e,s=[];return aE(t,t=>{let e={before:[],lines:[],after:[]},r=cY(i,t);cB(e.before,c$(cK(r,"beforeLabel",this,t))),cB(e.lines,cK(r,"label",this,t)),cB(e.after,c$(cK(r,"afterLabel",this,t))),s.push(e)}),s}getAfterBody(t,e){return cB([],c$(cK(e.callbacks,"afterBody",this,t)))}getFooter(t,e){let{callbacks:i}=e,s=cK(i,"beforeFooter",this,t),r=cK(i,"footer",this,t),n=cK(i,"afterFooter",this,t),a=[];return a=cB(a,c$(s)),a=cB(a,c$(r)),a=cB(a,c$(n))}_createItems(t){let e,i,s=this._active,r=this.chart.data,n=[],a=[],o=[],l=[];for(e=0,i=s.length;e<i;++e)l.push(function(t,e){let{element:i,datasetIndex:s,index:r}=e,n=t.getDatasetMeta(s).controller,{label:a,value:o}=n.getLabelAndValue(r);return{chart:t,label:a,parsed:n.getParsed(r),raw:t.data.datasets[s].data[r],formattedValue:o,dataset:n.getDataset(),dataIndex:r,datasetIndex:s,element:i}}(this.chart,s[e]));return t.filter&&(l=l.filter((e,i,s)=>t.filter(e,i,s,r))),t.itemSort&&(l=l.sort((e,i)=>t.itemSort(e,i,r))),aE(l,e=>{let i=cY(t.callbacks,e);n.push(cK(i,"labelColor",this,e)),a.push(cK(i,"labelPointStyle",this,e)),o.push(cK(i,"labelTextColor",this,e))}),this.labelColors=n,this.labelPointStyles=a,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){let i,s=this.options.setContext(this.getContext()),r=this._active,n=[];if(r.length){let t=cz[s.position].call(this,r,this._eventPosition);n=this._createItems(s),this.title=this.getTitle(n,s),this.beforeBody=this.getBeforeBody(n,s),this.body=this.getBody(n,s),this.afterBody=this.getAfterBody(n,s),this.footer=this.getFooter(n,s);let e=this._size=cW(this,s),a=Object.assign({},t,e),o=cH(this.chart,s,a),l=cU(s,a,o,this.chart);this.xAlign=o.xAlign,this.yAlign=o.yAlign,i={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(i={opacity:0});this._tooltipItems=n,this.$context=void 0,i&&this._resolveAnimations().update(this,i),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){let r=this.getCaretPosition(t,i,s);e.lineTo(r.x1,r.y1),e.lineTo(r.x2,r.y2),e.lineTo(r.x3,r.y3)}getCaretPosition(t,e,i){let s,r,n,a,o,l,{xAlign:h,yAlign:c}=this,{caretSize:u,cornerRadius:d}=i,{topLeft:p,topRight:f,bottomLeft:m,bottomRight:g}=oG(d),{x:x,y:y}=t,{width:b,height:v}=e;return"center"===c?(o=y+v/2,"left"===h?(r=(s=x)-u,a=o+u,l=o-u):(r=(s=x+b)+u,a=o-u,l=o+u),n=s):(r="left"===h?x+Math.max(p,m)+u:"right"===h?x+b-Math.max(f,g)-u:this.caretX,"top"===c?(o=(a=y)-u,s=r-u,n=r+u):(o=(a=y+v)+u,s=r+u,n=r-u),l=a),{x1:s,x2:r,x3:n,y1:a,y2:o,y3:l}}drawTitle(t,e,i){let s,r,n,a=this.title,o=a.length;if(o){let l=lw(i.rtl,this.x,this.width);for(n=0,t.x=cq(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",s=oQ(i.titleFont),r=i.titleSpacing,e.fillStyle=i.titleColor,e.font=s.string;n<o;++n)e.fillText(a[n],l.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+r,n+1===o&&(t.y+=i.titleMarginBottom-r)}}_drawColorBox(t,e,i,s,r){let n=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:o,boxWidth:l}=r,h=oQ(r.bodyFont),c=cq(this,"left",r),u=s.x(c),d=o<h.lineHeight?(h.lineHeight-o)/2:0,p=e.y+d;if(r.usePointStyle){let e={radius:Math.min(l,o)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},i=s.leftForLtr(u,l)+l/2,h=p+o/2;t.strokeStyle=r.multiKeyBackground,t.fillStyle=r.multiKeyBackground,oL(t,e,i,h),t.strokeStyle=n.borderColor,t.fillStyle=n.backgroundColor,oL(t,e,i,h)}else{t.lineWidth=a_(n.borderWidth)?Math.max(...Object.values(n.borderWidth)):n.borderWidth||1,t.strokeStyle=n.borderColor,t.setLineDash(n.borderDash||[]),t.lineDashOffset=n.borderDashOffset||0;let e=s.leftForLtr(u,l),i=s.leftForLtr(s.xPlus(u,1),l-2),a=oG(n.borderRadius);Object.values(a).some(t=>0!==t)?(t.beginPath(),t.fillStyle=r.multiKeyBackground,oH(t,{x:e,y:p,w:l,h:o,radius:a}),t.fill(),t.stroke(),t.fillStyle=n.backgroundColor,t.beginPath(),oH(t,{x:i,y:p+1,w:l-2,h:o-2,radius:a}),t.fill()):(t.fillStyle=r.multiKeyBackground,t.fillRect(e,p,l,o),t.strokeRect(e,p,l,o),t.fillStyle=n.backgroundColor,t.fillRect(i,p+1,l-2,o-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let s,r,n,a,o,l,{body:h}=this,{bodySpacing:c,bodyAlign:u,displayColors:d,boxHeight:p,boxWidth:f,boxPadding:m}=i,g=oQ(i.bodyFont),x=g.lineHeight,y=0,b=lw(i.rtl,this.x,this.width),v=function(i){e.fillText(i,b.x(t.x+y),t.y+x/2),t.y+=x+c},_=b.textAlign(u);for(e.textAlign=u,e.textBaseline="middle",e.font=g.string,t.x=cq(this,_,i),e.fillStyle=i.bodyColor,aE(this.beforeBody,v),y=d&&"right"!==_?"center"===u?f/2+m:f+2+m:0,n=0,o=h.length;n<o;++n){for(s=h[n],e.fillStyle=this.labelTextColors[n],aE(s.before,v),r=s.lines,d&&r.length&&(this._drawColorBox(e,t,n,b,i),x=Math.max(g.lineHeight,p)),a=0,l=r.length;a<l;++a)v(r[a]),x=g.lineHeight;aE(s.after,v)}y=0,x=g.lineHeight,aE(this.afterBody,v),t.y-=c}drawFooter(t,e,i){let s,r,n=this.footer,a=n.length;if(a){let o=lw(i.rtl,this.x,this.width);for(t.x=cq(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=o.textAlign(i.footerAlign),e.textBaseline="middle",s=oQ(i.footerFont),e.fillStyle=i.footerColor,e.font=s.string,r=0;r<a;++r)e.fillText(n[r],o.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){let{xAlign:r,yAlign:n}=this,{x:a,y:o}=t,{width:l,height:h}=i,{topLeft:c,topRight:u,bottomLeft:d,bottomRight:p}=oG(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(a+c,o),"top"===n&&this.drawCaret(t,e,i,s),e.lineTo(a+l-u,o),e.quadraticCurveTo(a+l,o,a+l,o+u),"center"===n&&"right"===r&&this.drawCaret(t,e,i,s),e.lineTo(a+l,o+h-p),e.quadraticCurveTo(a+l,o+h,a+l-p,o+h),"bottom"===n&&this.drawCaret(t,e,i,s),e.lineTo(a+d,o+h),e.quadraticCurveTo(a,o+h,a,o+h-d),"center"===n&&"left"===r&&this.drawCaret(t,e,i,s),e.lineTo(a,o+c),e.quadraticCurveTo(a,o,a+c,o),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,s=i&&i.x,r=i&&i.y;if(s||r){let i=cz[t.position].call(this,this._active,this._eventPosition);if(!i)return;let n=this._size=cW(this,t),a=Object.assign({},i,this._size),o=cH(e,t,a),l=cU(t,a,o,e);(s._to!==l.x||r._to!==l.y)&&(this.xAlign=o.xAlign,this.yAlign=o.yAlign,this.width=n.width,this.height=n.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let s={width:this.width,height:this.height},r={x:this.x,y:this.y};i=.001>Math.abs(i)?0:i;let n=oZ(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=i,this.drawBackground(r,t,s,e),lM(t,e.textDirection),r.y+=n.top,this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),lP(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,s=t.map(({datasetIndex:t,index:e})=>{let i=this.chart.getDatasetMeta(t);if(!i)throw Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}),r=!aj(i,s),n=this._positionChanged(s,e);(r||n)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let s=this.options,r=this._active||[],n=this._getActiveElements(t,r,e,i),a=this._positionChanged(n,t),o=e||!aj(n,r)||a;return o&&(this._active=n,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),o}_getActiveElements(t,e,i,s){let r=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter(t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index));let n=this.chart.getElementsAtEventForMode(t,r.mode,r,i);return r.reverse&&n.reverse(),n}_positionChanged(t,e){let{caretX:i,caretY:s,options:r}=this,n=cz[r.position].call(this,t,e);return!1!==n&&(i!==n.x||s!==n.y)}}let cZ=(t,e,i,s)=>("string"==typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i),cQ=(t,e)=>null===t?null:a7(Math.round(t),0,e);function cJ(t){let e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class c0 extends h${static id="category";static defaults={ticks:{callback:cJ}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let t=this.getLabels();for(let{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(ab(t))return null;let i=this.getLabels();return cQ(e=isFinite(e)&&i[e]===t?e:function(t,e,i,s){let r=t.indexOf(e);return -1===r?cZ(t,e,i,s):r!==t.lastIndexOf(e)?i:r}(i,t,aP(e,t),this._addedLabels),i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:s}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,s=[],r=this.getLabels();r=0===t&&e===r.length-1?r:r.slice(t,e+1),this._valueRange=Math.max(r.length-!i,1),this._startValue=this.min-.5*!!i;for(let i=t;i<=e;i++)s.push({value:i});return s}getLabelForValue(t){return cJ.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function c1(t,e,{horizontal:i,minRotation:s}){let r=a2(s),n=(i?Math.sin(r):Math.cos(r))||.001,a=.75*e*(""+t).length;return Math.min(e/n,a)}class c2 extends h${constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return ab(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:s,max:r}=this,n=t=>s=e?s:t,a=t=>r=i?r:t;if(t){let t=aZ(s),e=aZ(r);t<0&&e<0?a(0):t>0&&e>0&&n(0)}if(s===r){let e=0===r?1:Math.abs(.05*r);a(r+e),t||n(s-e)}this.min=s,this.max=r}getTickLimit(){let t,{maxTicksLimit:e,stepSize:i}=this.options.ticks;return i?(t=Math.ceil(this.max/i)-Math.floor(this.min/i)+1)>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${t} ticks. Limiting to 1000.`),t=1e3):(t=this.computeTickLimit(),e=e||11),e&&(t=Math.min(e,t)),t}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit(),s=function(t,e){let i,s,r,n,a=[],{bounds:o,step:l,min:h,max:c,precision:u,count:d,maxTicks:p,maxDigits:f,includeBounds:m}=t,g=l||1,x=p-1,{min:y,max:b}=e,v=!ab(h),_=!ab(c),w=!ab(d),M=(b-y)/(f+1),P=aJ((b-y)/x/g)*g;if(P<1e-14&&!v&&!_)return[{value:y},{value:b}];(n=Math.ceil(b/P)-Math.floor(y/P))>x&&(P=aJ(n*P/x/g)*g),ab(u)||(P=Math.ceil(P*(i=Math.pow(10,u)))/i),"ticks"===o?(s=Math.floor(y/P)*P,r=Math.ceil(b/P)*P):(s=y,r=b),v&&_&&l&&function(t,e){let i=Math.round(t);return i-e<=t&&i+e>=t}((c-h)/l,P/1e3)?(n=Math.round(Math.min((c-h)/P,p)),P=(c-h)/n,s=h,r=c):w?(s=v?h:s,P=((r=_?c:r)-s)/(n=d-1)):n=aQ(n=(r-s)/P,Math.round(n),P/1e3)?Math.round(n):Math.ceil(n);let k=Math.max(a5(P),a5(s));s=Math.round(s*(i=Math.pow(10,ab(u)?k:u)))/i,r=Math.round(r*i)/i;let S=0;for(v&&(m&&s!==h?(a.push({value:h}),s<h&&S++,aQ(Math.round((s+S*P)*i)/i,h,c1(h,M,t))&&S++):s<h&&S++);S<n;++S){let t=Math.round((s+S*P)*i)/i;if(_&&t>c)break;a.push({value:t})}return _&&m&&r!==c?a.length&&aQ(a[a.length-1].value,c,c1(c,M,t))?a[a.length-1].value=c:a.push({value:c}):_&&r!==c||a.push({value:r}),a}({maxTicks:i=Math.max(2,i),bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&a1(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return oP(t,this.chart.options.locale,this.options.ticks.format)}}class c5 extends c2{static id="linear";static defaults={ticks:{callback:oS.formatters.numeric}};determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=aw(t)?t:0,this.max=aw(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=a2(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001;return Math.ceil(e/Math.min(40,this._resolveTickFontOptions(0).lineHeight/s))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}let c6=t=>Math.floor(aG(t)),c3=(t,e)=>Math.pow(10,c6(t)+e);function c4(t){return 1==t/Math.pow(10,c6(t))}function c8(t,e,i){let s=Math.pow(10,i),r=Math.floor(t/s);return Math.ceil(e/s)-r}class c9 extends h${static id="logarithmic";static defaults={ticks:{callback:oS.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=c2.prototype.parse.apply(this,[t,e]);if(0===i){this._zero=!0;return}return aw(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=aw(t)?Math.max(0,t):null,this.max=aw(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!aw(this._userMin)&&(this.min=t===c3(this.min,0)?c3(this.min,-1):c3(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,s=this.max,r=e=>i=t?i:e,n=t=>s=e?s:t;i===s&&(i<=0?(r(1),n(10)):(r(c3(i,-1)),n(c3(s,1)))),i<=0&&r(c3(s,-1)),s<=0&&n(c3(i,1)),this.min=i,this.max=s}buildTicks(){let t=this.options,e=function(t,{min:e,max:i}){e=aM(t.min,e);let s=[],r=c6(e),n=function(t,e){let i=c6(e-t);for(;c8(t,e,i)>10;)i++;for(;10>c8(t,e,i);)i--;return Math.min(i,c6(t))}(e,i),a=n<0?Math.pow(10,Math.abs(n)):1,o=Math.pow(10,n),l=r>n?Math.pow(10,r):0,h=Math.round((e-l)*a)/a,c=Math.floor((e-l)/o/10)*o*10,u=Math.floor((h-c)/Math.pow(10,n)),d=aM(t.min,Math.round((l+c+u*Math.pow(10,n))*a)/a);for(;d<i;)s.push({value:d,major:c4(d),significand:u}),u>=10?u=u<15?15:20:u++,u>=20&&(u=2,a=++n>=0?1:a),d=Math.round((l+c+u*Math.pow(10,n))*a)/a;let p=aM(t.max,d);return s.push({value:p,major:c4(p),significand:u}),s}({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&a1(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":oP(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=aG(t),this._valueRange=aG(this.max)-aG(t)}getPixelForValue(t){return((void 0===t||0===t)&&(t=this.min),null===t||isNaN(t))?NaN:this.getPixelForDecimal(t===this.min?0:(aG(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function c7(t){let e=t.ticks;if(e.display&&t.display){let t=oZ(e.backdropPadding);return aP(e.font&&e.font.size,oD.font.size)+t.height}return 0}function ut(t,e,i,s,r){return t===s||t===r?{start:e-i/2,end:e+i/2}:t<s||t>r?{start:e-i,end:e}:{start:e,end:e+i}}function ue(t,e,i,s){let{ctx:r}=t;if(i)r.arc(t.xCenter,t.yCenter,e,0,aW);else{let i=t.getPointPosition(0,e);r.moveTo(i.x,i.y);for(let n=1;n<s;n++)i=t.getPointPosition(n,e),r.lineTo(i.x,i.y)}}class ui extends c2{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:oS.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=oZ(c7(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=aw(t)&&!isNaN(t)?t:0,this.max=aw(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/c7(this.options))}generateTickLabels(t){c2.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((t,e)=>{let i=aT(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){let t=this.options;t.display&&t.pointLabels.display?function(t){let e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),s=[],r=[],n=t._pointLabels.length,a=t.options.pointLabels,o=a.centerPointLabels?a$/n:0;for(let c=0;c<n;c++){var l,h;let n=a.setContext(t.getPointLabelContext(c));r[c]=n.padding;let u=t.getPointPosition(c,t.drawingArea+r[c],o),d=oQ(n.font),p=(l=t.ctx,h=av(h=t._pointLabels[c])?h:[h],{w:function(t,e,i,s){let r,n,a,o,l,h=(s=s||{}).data=s.data||{},c=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(h=s.data={},c=s.garbageCollect=[],s.font=e),t.save(),t.font=e;let u=0,d=i.length;for(r=0;r<d;r++)if(null==(o=i[r])||av(o)){if(av(o))for(n=0,a=o.length;n<a;n++)null==(l=o[n])||av(l)||(u=oN(t,h,c,u,l))}else u=oN(t,h,c,u,o);t.restore();let p=c.length/2;if(p>i.length){for(r=0;r<p;r++)delete h[c[r]];c.splice(0,p)}return u}(l,d.string,h),h:h.length*d.lineHeight});s[c]=p;let f=a8(t.getIndexAngle(c)+o),m=Math.round(180/a$*f);!function(t,e,i,s,r){let n=Math.abs(Math.sin(i)),a=Math.abs(Math.cos(i)),o=0,l=0;s.start<e.l?(o=(e.l-s.start)/n,t.l=Math.min(t.l,e.l-o)):s.end>e.r&&(o=(s.end-e.r)/n,t.r=Math.max(t.r,e.r+o)),r.start<e.t?(l=(e.t-r.start)/a,t.t=Math.min(t.t,e.t-l)):r.end>e.b&&(l=(r.end-e.b)/a,t.b=Math.max(t.b,e.b+l))}(i,e,f,ut(m,u.x,p.w,0,180),ut(m,u.y,p.h,90,270))}t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){let s,r=[],n=t._pointLabels.length,a=t.options,{centerPointLabels:o,display:l}=a.pointLabels,h={extra:c7(a)/2,additionalAngle:o?a$/n:0};for(let a=0;a<n;a++){h.padding=i[a],h.size=e[a];let n=function(t,e,i){var s,r,n,a,o,l,h;let c=t.drawingArea,{extra:u,additionalAngle:d,padding:p,size:f}=i,m=t.getPointPosition(e,c+u+p,d),g=Math.round(180/a$*a8(m.angle+aY)),x=(s=m.y,r=f.h,90===(n=g)||270===n?s-=r/2:(n>270||n<90)&&(s-=r),s),y=0===(a=g)||180===a?"center":a<180?"left":"right",b=(o=m.x,l=f.w,"right"===(h=y)?o-=l:"center"===h&&(o-=l/2),o);return{visible:!0,x:m.x,y:x,textAlign:y,left:b,top:x,right:b+f.w,bottom:x+f.h}}(t,a,h);r.push(n),"auto"===l&&(n.visible=function(t,e){if(!e)return!0;let{left:i,top:s,right:r,bottom:n}=t;return!(oF({x:i,y:s},e)||oF({x:i,y:n},e)||oF({x:r,y:s},e)||oF({x:r,y:n},e))}(n,s),n.visible&&(s=n))}return r}(t,s,r)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){return a8(t*(aW/(this._pointLabels.length||1))+a2(this.options.startAngle||0))}getDistanceFromCenterForValue(t){if(ab(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(ab(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){var i;let s=e[t];return i=this.getContext(),o0(i,{label:s,index:t,type:"pointLabel"})}}getPointPosition(t,e,i=0){let s=this.getIndexAngle(t)-aY+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:s,bottom:r}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:r}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),ue(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t,e,i,s=this.ctx,r=this.options,{angleLines:n,grid:a,border:o}=r,l=this._pointLabels.length;if(r.pointLabels.display&&function(t,e){let{ctx:i,options:{pointLabels:s}}=t;for(let r=e-1;r>=0;r--){let e=t._pointLabelItems[r];if(!e.visible)continue;let n=s.setContext(t.getPointLabelContext(r));!function(t,e,i){let{left:s,top:r,right:n,bottom:a}=i,{backdropColor:o}=e;if(!ab(o)){let i=oG(e.borderRadius),l=oZ(e.backdropPadding);t.fillStyle=o;let h=s-l.left,c=r-l.top,u=n-s+l.width,d=a-r+l.height;Object.values(i).some(t=>0!==t)?(t.beginPath(),oH(t,{x:h,y:c,w:u,h:d,radius:i}),t.fill()):t.fillRect(h,c,u,d)}}(i,n,e);let a=oQ(n.font),{x:o,y:l,textAlign:h}=e;oW(i,t._pointLabels[r],o,l+a.lineHeight/2,a,{color:n.color,textAlign:h,textBaseline:"middle"})}}(this,l),a.display&&this.ticks.forEach((t,i)=>{if(0!==i||0===i&&this.min<0){e=this.getDistanceFromCenterForValue(t.value);let s=this.getContext(i),r=a.setContext(s),n=o.setContext(s);!function(t,e,i,s,r){let n=t.ctx,a=e.circular,{color:o,lineWidth:l}=e;(a||s)&&o&&l&&!(i<0)&&(n.save(),n.strokeStyle=o,n.lineWidth=l,n.setLineDash(r.dash||[]),n.lineDashOffset=r.dashOffset,n.beginPath(),ue(t,i,a,s),n.closePath(),n.stroke(),n.restore())}(this,r,e,l,n)}}),n.display){for(s.save(),t=l-1;t>=0;t--){let a=n.setContext(this.getPointLabelContext(t)),{color:o,lineWidth:l}=a;l&&o&&(s.lineWidth=l,s.strokeStyle=o,s.setLineDash(a.borderDash),s.lineDashOffset=a.borderDashOffset,e=this.getDistanceFromCenterForValue(r.reverse?this.min:this.max),i=this.getPointPosition(t,e),s.beginPath(),s.moveTo(this.xCenter,this.yCenter),s.lineTo(i.x,i.y),s.stroke())}s.restore()}}drawBorder(){}drawLabels(){let t,e,i=this.ctx,s=this.options,r=s.ticks;if(!r.display)return;let n=this.getIndexAngle(0);i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(n),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((n,a)=>{if(0===a&&this.min>=0&&!s.reverse)return;let o=r.setContext(this.getContext(a)),l=oQ(o.font);if(t=this.getDistanceFromCenterForValue(this.ticks[a].value),o.showLabelBackdrop){i.font=l.string,e=i.measureText(n.label).width,i.fillStyle=o.backdropColor;let s=oZ(o.backdropPadding);i.fillRect(-e/2-s.left,-t-l.size/2-s.top,e+s.width,l.size+s.height)}oW(i,n.label,0,-t,l,{color:o.color,strokeColor:o.textStrokeColor,strokeWidth:o.textStrokeWidth})}),i.restore()}drawTitle(){}}let us={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},ur=Object.keys(us);function un(t,e){return t-e}function ua(t,e){if(ab(e))return null;let i=t._adapter,{parser:s,round:r,isoWeekday:n}=t._parseOpts,a=e;return("function"==typeof s&&(a=s(a)),aw(a)||(a="string"==typeof s?i.parse(a,s):i.parse(a)),null===a)?null:(r&&(a="week"===r&&(a0(n)||!0===n)?i.startOf(a,"isoWeek",n):i.startOf(a,r)),+a)}function uo(t,e,i,s){let r=ur.length;for(let n=ur.indexOf(t);n<r-1;++n){let t=us[ur[n]],r=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(r*t.size))<=s)return ur[n]}return ur[r-1]}function ul(t,e,i){if(i){if(i.length){let{lo:s,hi:r}=oe(i,e);t[i[s]>=e?i[s]:i[r]]=!0}}else t[e]=!0}function uh(t,e,i){let s,r,n=[],a={},o=e.length;for(s=0;s<o;++s)a[r=e[s]]=s,n.push({value:r,major:!1});return 0!==o&&i?function(t,e,i,s){let r,n,a=t._adapter,o=+a.startOf(e[0].value,s),l=e[e.length-1].value;for(r=o;r<=l;r=+a.add(r,1,s))(n=i[r])>=0&&(e[n].major=!0);return e}(t,n,a,i):n}class uc extends h${static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){let i=t.time||(t.time={}),s=this._adapter=new hi._date(t.adapters.date);s.init(e),aR(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:ua(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:s,max:r,minDefined:n,maxDefined:a}=this.getUserBounds();function o(t){n||isNaN(t.min)||(s=Math.min(s,t.min)),a||isNaN(t.max)||(r=Math.max(r,t.max))}n&&a||(o(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&o(this.getMinMax(!1))),s=aw(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),r=aw(r)&&!isNaN(r)?r:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,r-1),this.max=Math.max(s+1,r)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,s="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);let r=this.min,n=function(t,e,i){let s=0,r=t.length;for(;s<r&&t[s]<e;)s++;for(;r>s&&t[r-1]>i;)r--;return s>0||r<t.length?t.slice(s,r):t}(s,r,this.max);return this._unit=e.unit||(i.autoSkip?uo(e.minUnit,this.min,this.max,this._getLabelCapacity(r)):function(t,e,i,s,r){for(let n=ur.length-1;n>=ur.indexOf(i);n--){let i=ur[n];if(us[i].common&&t._adapter.diff(r,s,i)>=e-1)return i}return ur[i?ur.indexOf(i):0]}(this,n.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=ur.indexOf(t)+1,i=ur.length;e<i;++e)if(us[ur[e]].common)return ur[e]}(this._unit):void 0,this.initOffsets(s),t.reverse&&n.reverse(),uh(this,n,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e,i,s=0,r=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),s=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),r=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);let n=t.length<3?.5:.25;s=a7(s,0,n),r=a7(r,0,n),this._offsets={start:s,end:r,factor:1/(s+1+r)}}_generate(){let t,e,i=this._adapter,s=this.min,r=this.max,n=this.options,a=n.time,o=a.unit||uo(a.minUnit,s,r,this._getLabelCapacity(s)),l=aP(n.ticks.stepSize,1),h="week"===o&&a.isoWeekday,c=a0(h)||!0===h,u={},d=s;if(c&&(d=+i.startOf(d,"isoWeek",h)),d=+i.startOf(d,c?"day":o),i.diff(r,s,o)>1e5*l)throw Error(s+" and "+r+" are too far apart with stepSize of "+l+" "+o);let p="data"===n.ticks.source&&this.getDataTimestamps();for(t=d,e=0;t<r;t=+i.add(t,l,o),e++)ul(u,t,p);return(t===r||"ticks"===n.bounds||1===e)&&ul(u,t,p),Object.keys(u).sort(un).map(t=>+t)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){let i=this.options.time.displayFormats,s=this._unit,r=e||i[s];return this._adapter.format(t,r)}_tickFormatFunction(t,e,i,s){let r=this.options,n=r.ticks.callback;if(n)return aT(n,[t,e,i],this);let a=r.time.displayFormats,o=this._unit,l=this._majorUnit,h=o&&a[o],c=l&&a[l],u=i[e],d=l&&c&&u&&u.major;return this._adapter.format(t,s||(d?c:h))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)(s=t[e]).label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,s=a2(this.isHorizontal()?e.maxRotation:e.minRotation),r=Math.cos(s),n=Math.sin(s),a=this._resolveTickFontOptions(0).size;return{w:i*r+a*n,h:i*n+a*r}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,r=this._tickFormatFunction(t,0,uh(this,[t],this._majorUnit),s),n=this._getLabelSize(r),a=Math.floor(this.isHorizontal()?this.width/n.w:this.height/n.h)-1;return a>0?a:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;let s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){let t,e,i=this._cache.labels||[];if(i.length)return i;let s=this.getLabels();for(t=0,e=s.length;t<e;++t)i.push(ua(this,s[t]));return this._cache.labels=this._normalized?i:this.normalize(i)}normalize(t){return oa(t.sort(un))}}function uu(t,e,i){let s,r,n,a,o=0,l=t.length-1;i?(e>=t[o].pos&&e<=t[l].pos&&({lo:o,hi:l}=oi(t,"pos",e)),{pos:s,time:n}=t[o],{pos:r,time:a}=t[l]):(e>=t[o].time&&e<=t[l].time&&({lo:o,hi:l}=oi(t,"time",e)),{time:s,pos:n}=t[o],{time:r,pos:a}=t[l]);let h=r-s;return h?n+(a-n)*(e-s)/h:n}class ud extends uc{static id="timeseries";static defaults=uc.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=uu(e,this.min),this._tableRange=uu(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let e,i,s,{min:r,max:n}=this,a=[],o=[];for(e=0,i=t.length;e<i;++e)(s=t[e])>=r&&s<=n&&a.push(s);if(a.length<2)return[{time:r,pos:0},{time:n,pos:1}];for(e=0,i=a.length;e<i;++e)Math.round((a[e+1]+a[e-1])/2)!==(s=a[e])&&o.push({time:s,pos:e/(i-1)});return o}_generate(){let t=this.min,e=this.max,i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort((t,e)=>t-e)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t}getDecimalForValue(t){return(uu(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return uu(this._table,i*this._tableRange+this._minPos,!0)}}let up="label";function uf(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function um(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:up,s=[];t.datasets=e.map(e=>{let r=t.datasets.find(t=>t[i]===e[i]);return!r||!e.data||s.includes(r)?{...e}:(s.push(r),Object.assign(r,e),r)})}let ug=(0,l.forwardRef)(function(t,e){let{height:i=150,width:s=300,redraw:r=!1,datasetIdKey:n,type:a,data:o,options:h,plugins:c=[],fallbackContent:u,updateMode:d,...p}=t,f=(0,l.useRef)(null),m=(0,l.useRef)(null),g=()=>{f.current&&(m.current=new cr(f.current,{type:a,data:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:up,i={labels:[],datasets:[]};return i.labels=t.labels,um(i,t.datasets,e),i}(o,n),options:h&&{...h},plugins:c}),uf(e,m.current))},x=()=>{uf(e,null),m.current&&(m.current.destroy(),m.current=null)};return(0,l.useEffect)(()=>{!r&&m.current&&h&&function(t,e){let i=t.options;i&&e&&Object.assign(i,e)}(m.current,h)},[r,h]),(0,l.useEffect)(()=>{!r&&m.current&&(m.current.config.data.labels=o.labels)},[r,o.labels]),(0,l.useEffect)(()=>{!r&&m.current&&o.datasets&&um(m.current.config.data,o.datasets,n)},[r,o.datasets]),(0,l.useEffect)(()=>{m.current&&(r?(x(),setTimeout(g)):m.current.update(d))},[r,h,o.labels,o.datasets,d]),(0,l.useEffect)(()=>{m.current&&(x(),setTimeout(g))},[a]),(0,l.useEffect)(()=>(g(),()=>x()),[]),l.createElement("canvas",{ref:f,role:"img",height:i,width:s,...p},u)});function ux(t,e){return cr.register(e),(0,l.forwardRef)((e,i)=>l.createElement(ug,{...e,ref:i,type:t}))}let uy=ux("line",l3),ub=ux("bar",l2);class uv{constructor(){this.cache=new Map,this.CACHE_TTL=3e5}static getInstance(){return uv.instance||(uv.instance=new uv),uv.instance}async fetchLotteryData(){let t="lottery-data",e=this.cache.get(t);if(e&&Date.now()-e.timestamp<this.CACHE_TTL)return e.data;try{let e=await fetch("/api/lottery-data");if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);let i=await e.json();return this.cache.set(t,{data:i,timestamp:Date.now()}),i}catch(t){throw console.error("Failed to fetch lottery data:",t),t}}calculateNumberFrequency(t){let e={},i=6*t.length;t.forEach(t=>{t.result.forEach(t=>{e[t]=(e[t]||0)+1})});let s=[];for(let t=1;t<=55;t++){let r=e[t]||0;s.push({number:t,count:r,percentage:i>0?r/i*100:0})}return s.sort((t,e)=>e.count-t.count)}calculateStatistics(t){let e=this.calculateNumberFrequency(t),i=new Date,s=e=>t.filter(t=>{let s=new Date(t.date);return Math.ceil((i.getTime()-s.getTime())/864e5)<=e});return{totalDraws:t.length,mostFrequent:e.slice(0,10),leastFrequent:e.slice(-10).reverse(),numberDistribution:e,recentTrends:{last30Days:this.calculateNumberFrequency(s(30)),last60Days:this.calculateNumberFrequency(s(60)),last90Days:this.calculateNumberFrequency(s(90))}}}getHotNumbers(t,e=6,i=55){let s=this.calculateNumberFrequency(t).slice(0,e).map(t=>t.number);return this.ensureUniqueNumbers(s,i)}getColdNumbers(t,e=6,i=55){let s=this.calculateNumberFrequency(t).slice(-e).map(t=>t.number).reverse();return this.ensureUniqueNumbers(s,i)}getBalancedNumbers(t,e=55){let i=[...this.getHotNumbers(t,3,e),...this.getColdNumbers(t,3,e)];return this.ensureUniqueNumbers(i,e)}getRecentTrendNumbers(t){return this.calculateStatistics(t).recentTrends.last30Days.slice(0,6).map(t=>t.number)}getRandomNumbers(t=55){let e=[],i=new Set;for(;e.length<6;){let s=Math.floor(Math.random()*t)+1;i.has(s)||(e.push(s),i.add(s))}return e.sort((t,e)=>t-e)}ensureUniqueNumbers(t,e=55){let i=[...new Set(t)];for(;i.length<6;){let t=Math.floor(Math.random()*e)+1;i.includes(t)||i.push(t)}return i.slice(0,6).sort((t,e)=>t-e)}getMathematicalPatternNumbers(t){let e=this.calculateNumberFrequency(t),i=e.reduce((t,e)=>t+e.count,0)/e.length,s=e.filter(t=>Math.abs(t.count-i)<=.2*i);return s.length>=6?s.slice(0,6).map(t=>t.number):e.slice(0,6).map(t=>t.number)}calculateConfidence(t,e){let i=this.calculateStatistics(e),s=i.numberDistribution.reduce((t,e)=>t+e.count,0)/i.numberDistribution.length,r=t.map(t=>i.numberDistribution.find(e=>e.number===t)?.count||0);return Math.min(100,Math.max(0,r.reduce((t,e)=>t+e,0)/r.length/s*50))}getSmartFrequencyNumbers(t){let e=t.slice(0,50),i=t.slice(0,200),s=this.calculateNumberFrequency(e),r=this.calculateNumberFrequency(i);return s.map(t=>{let e=r.find(e=>e.number===t.number);return{number:t.number,score:.7*t.percentage+.3*(e?.percentage||0)}}).sort((t,e)=>e.score-t.score).slice(0,6).map(t=>t.number).sort((t,e)=>t-e)}getGapAnalysisNumbers(t){let e={};for(let i=1;i<=55;i++){let s=[],r=-1;t.forEach((t,e)=>{t.result.includes(i)&&(-1!==r&&s.push(e-r),r=e)});let n=s.length>0?s.reduce((t,e)=>t+e,0)/s.length:0,a=-1===r?t.length:r;e[i]={gaps:s,avgGap:n,currentGap:a}}let i=Object.entries(e).map(([t,e])=>({number:parseInt(t),priority:e.currentGap>=e.avgGap?e.currentGap/e.avgGap:0})).filter(t=>t.priority>1.2).sort((t,e)=>e.priority-t.priority).slice(0,6).map(t=>t.number);if(i.length<6){let e=this.getHotNumbers(t,6-i.length);i.push(...e.filter(t=>!i.includes(t)))}return i.slice(0,6).sort((t,e)=>t-e)}getPatternBasedNumbers(t){let e=t.slice(0,30),i={evenOdd:this.analyzeEvenOddPattern(e),sumRange:this.analyzeSumPattern(e),consecutive:this.analyzeConsecutivePattern(e),endDigits:this.analyzeEndDigitPattern(e)},s=[],r=new Set,n=i.evenOdd.optimalEven,a=6-n,o=0,l=0,h=this.calculateNumberFrequency(t.slice(0,100)).sort((t,e)=>e.count-t.count);for(let t of h){if(s.length>=6)break;let e=t.number%2==0;(e&&o<n||!e&&l<a)&&!r.has(t.number)&&(s.push(t.number),r.add(t.number),e?o++:l++)}for(;s.length<6;)for(let t of h){if(s.length>=6)break;r.has(t.number)||(s.push(t.number),r.add(t.number))}return s.slice(0,6).sort((t,e)=>t-e)}getEnsembleNumbers(t){let e=[{name:"hot",numbers:this.getHotNumbers(t),weight:.25},{name:"smart",numbers:this.getSmartFrequencyNumbers(t),weight:.25},{name:"gap",numbers:this.getGapAnalysisNumbers(t),weight:.25},{name:"pattern",numbers:this.getPatternBasedNumbers(t),weight:.25}],i={};for(let t=1;t<=55;t++)i[t]=0;return e.forEach(t=>{t.numbers.forEach((e,s)=>{i[e]+=t.weight*(6-s)})}),Object.entries(i).sort(([,t],[,e])=>e-t).slice(0,6).map(([t])=>parseInt(t)).sort((t,e)=>t-e)}analyzeEvenOddPattern(t){let e=t.map(t=>{let e=t.result.filter(t=>t%2==0).length;return{even:e,odd:6-e}});return{optimalEven:Math.round(e.reduce((t,e)=>t+e.even,0)/e.length)}}analyzeSumPattern(t){let e=t.map(t=>t.result.reduce((t,e)=>t+e,0));return{optimalSum:Math.round(e.reduce((t,e)=>t+e,0)/e.length),range:{min:Math.min(...e),max:Math.max(...e)}}}analyzeConsecutivePattern(t){let e=t.map(t=>{let e=[...t.result].sort((t,e)=>t-e),i=0;for(let t=0;t<e.length-1;t++)e[t+1]===e[t]+1&&i++;return i}),i=e.reduce((t,e)=>t+e,0)/e.length;return{hasConsecutive:i>.5,avgConsecutive:i}}analyzeEndDigitPattern(t){let e={};return t.forEach(t=>{t.result.forEach(t=>{let i=t%10;e[i]=(e[i]||0)+1})}),e}clearCache(){this.cache.clear()}}cr.register(c0,c5,cP,cg,cy,{id:"title",_element:cI,start(t,e,i){let s=new cI({ctx:t.ctx,options:i,chart:t});hx.configure(t,s,i),hx.addBox(t,s),t.titleBlock=s},stop(t){let e=t.titleBlock;hx.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){let s=t.titleBlock;hx.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},{id:"tooltip",_element:cG,positioners:cz,afterInit(t,e,i){i&&(t.tooltip=new cG({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){let e=t.tooltip;if(e&&e._willRender()){let i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){let i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:cX},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},{id:"legend",_element:cV,start(t,e,i){let s=t.legend=new cV({ctx:t.ctx,options:i,chart:t});hx.configure(t,s,i),hx.addBox(t,s)},stop(t){hx.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){let s=t.legend;hx.configure(t,s,i),s.options=i},afterUpdate(t){let e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){let s=e.datasetIndex,r=i.chart;r.isDatasetVisible(s)?(r.hide(s),e.hidden=!0):(r.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){let e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:s,textAlign:r,color:n,useBorderRadius:a,borderRadius:o}}=t.legend.options;return t._getSortedDatasetMetas().map(t=>{let l=t.controller.getStyle(i?0:void 0),h=oZ(l.borderWidth);return{text:e[t.index].label,fillStyle:l.backgroundColor,fontColor:n,hidden:!t.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:l.borderColor,pointStyle:s||l.pointStyle,rotation:l.rotation,textAlign:r||l.textAlign,borderRadius:a&&(o||l.borderRadius),datasetIndex:t.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}});let u_=(0,l.memo)(function({data:t}){let[e,i]=(0,l.useState)("frequency"),s=uv.getInstance(),r=(0,l.useMemo)(()=>0===t.length?null:s.calculateStatistics(t),[t,s]),n=(0,l.useMemo)(()=>r?{labels:r.numberDistribution.map(t=>t.number.toString()),datasets:[{label:"Frequency",data:r.numberDistribution.map(t=>t.count),backgroundColor:r.numberDistribution.map((t,e)=>e<10?"rgba(239, 68, 68, 0.8)":e>=r.numberDistribution.length-10?"rgba(59, 130, 246, 0.8)":"rgba(156, 163, 175, 0.8)"),borderColor:r.numberDistribution.map((t,e)=>e<10?"rgba(239, 68, 68, 1)":e>=r.numberDistribution.length-10?"rgba(59, 130, 246, 1)":"rgba(156, 163, 175, 1)"),borderWidth:1}]}:null,[r]),a=(0,l.useMemo)(()=>r?{labels:r.numberDistribution.slice(0,20).map(t=>t.number.toString()),datasets:[{label:"All Time",data:r.numberDistribution.slice(0,20).map(t=>t.count),borderColor:"rgba(75, 192, 192, 1)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1},{label:"Last 30 Days",data:r.recentTrends.last30Days.slice(0,20).map(t=>t.count),borderColor:"rgba(255, 99, 132, 1)",backgroundColor:"rgba(255, 99, 132, 0.2)",tension:.1},{label:"Last 90 Days",data:r.recentTrends.last90Days.slice(0,20).map(t=>t.count),borderColor:"rgba(54, 162, 235, 1)",backgroundColor:"rgba(54, 162, 235, 0.2)",tension:.1}]}:null,[r]);if(!r||!t.length)return(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,o.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"Statistics & Analysis"}),(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("div",{className:"text-gray-400 text-4xl mb-2",children:"\uD83D\uDCCA"}),(0,o.jsx)("p",{className:"text-gray-500",children:"No data available for analysis"})]})]});let h={responsive:!0,plugins:{legend:{position:"top"},title:{display:!0,text:"frequency"===e?"Number Frequency Distribution":"Frequency Trends Comparison"},tooltip:{callbacks:{label:function(t){let e=r.numberDistribution.find(e=>e.number.toString()===t.label)?.percentage.toFixed(2);return`${t.dataset.label}: ${t.parsed.y} (${e}%)`}}}},scales:{x:{title:{display:!0,text:"Numbers"}},y:{title:{display:!0,text:"Frequency"},beginAtZero:!0}}};return(0,o.jsxs)(nW,{className:"p-6",gradient:!0,children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,o.jsxs)(nE.h2,{className:"text-xl font-bold text-gray-800 flex items-center",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},children:[(0,o.jsx)(nG,{className:"mr-2 text-blue-600",size:24}),"Statistics & Analysis"]}),(0,o.jsxs)(nE.div,{className:"flex space-x-2",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,o.jsx)(nH,{onClick:()=>i("frequency"),variant:"frequency"===e?"primary":"ghost",size:"sm",icon:(0,o.jsx)(nG,{size:16}),children:"Frequency"}),(0,o.jsx)(nH,{onClick:()=>i("trends"),variant:"trends"===e?"primary":"ghost",size:"sm",icon:(0,o.jsx)(nZ,{size:16}),children:"Trends"})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[(0,o.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3 text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:r.totalDraws}),(0,o.jsx)("div",{className:"text-sm text-blue-800",children:"Total Draws"})]}),(0,o.jsxs)("div",{className:"bg-red-50 rounded-lg p-3 text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-red-600",children:r.mostFrequent[0]?.number}),(0,o.jsx)("div",{className:"text-sm text-red-800",children:"Hottest Number"})]}),(0,o.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3 text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:r.leastFrequent[0]?.number}),(0,o.jsx)("div",{className:"text-sm text-blue-800",children:"Coldest Number"})]}),(0,o.jsxs)("div",{className:"bg-green-50 rounded-lg p-3 text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-green-600",children:Math.round(r.numberDistribution.reduce((t,e)=>t+e.count,0)/55)}),(0,o.jsx)("div",{className:"text-sm text-green-800",children:"Avg Frequency"})]})]}),(0,o.jsx)("div",{className:"h-96",children:"frequency"===e?n&&(0,o.jsx)(ub,{data:n,options:h}):a&&(0,o.jsx)(uy,{data:a,options:h})}),(0,o.jsxs)("div",{className:"mt-4 flex justify-center space-x-6 text-sm",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-4 h-4 bg-red-500 rounded mr-2"}),(0,o.jsx)("span",{children:"Hot Numbers (Top 10)"})]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded mr-2"}),(0,o.jsx)("span",{children:"Cold Numbers (Bottom 10)"})]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-4 h-4 bg-gray-400 rounded mr-2"}),(0,o.jsx)("span",{children:"Normal Numbers"})]})]})]})});class uw extends l.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=e0(t)&&t.offsetWidth||0,s=this.props.sizeRef.current;s.height=e.offsetHeight||0,s.width=e.offsetWidth||0,s.top=e.offsetTop,s.left=e.offsetLeft,s.right=i-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function uM({children:t,isPresent:e,anchorX:i}){let s=(0,l.useId)(),r=(0,l.useRef)(null),n=(0,l.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=(0,l.useContext)(rA);return(0,l.useInsertionEffect)(()=>{let{width:t,height:o,top:l,left:h,right:c}=n.current;if(e||!r.current||!t||!o)return;let u="left"===i?`left: ${h}`:`right: ${c}`;r.current.dataset.motionPopId=s;let d=document.createElement("style");return a&&(d.nonce=a),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${o}px !important;
            ${u}px !important;
            top: ${l}px !important;
          }
        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[e]),(0,o.jsx)(uw,{isPresent:e,childRef:r,sizeRef:n,children:l.cloneElement(t,{ref:r})})}let uP=({children:t,initial:e,isPresent:i,onExitComplete:s,custom:r,presenceAffectsLayout:n,mode:a,anchorX:h})=>{let c=r3(uk),u=(0,l.useId)(),d=!0,p=(0,l.useMemo)(()=>(d=!1,{id:u,initial:e,isPresent:i,custom:r,onExitComplete:t=>{for(let e of(c.set(t,!0),c.values()))if(!e)return;s&&s()},register:t=>(c.set(t,!1),()=>c.delete(t))}),[i,c,s]);return n&&d&&(p={...p}),(0,l.useMemo)(()=>{c.forEach((t,e)=>c.set(e,!1))},[i]),l.useEffect(()=>{i||c.size||!s||s()},[i]),"popLayout"===a&&(t=(0,o.jsx)(uM,{isPresent:i,anchorX:h,children:t})),(0,o.jsx)(ss.Provider,{value:p,children:t})};function uk(){return new Map}let uS=t=>t.key||"";function uT(t){let e=[];return l.Children.forEach(t,t=>{(0,l.isValidElement)(t)&&e.push(t)}),e}let uE=({children:t,custom:e,initial:i=!0,onExitComplete:s,presenceAffectsLayout:r=!0,mode:n="sync",propagate:a=!1,anchorX:h="left"})=>{let[c,u]=sr(a),d=(0,l.useMemo)(()=>uT(t),[t]),p=a&&!c?[]:d.map(uS),f=(0,l.useRef)(!0),m=(0,l.useRef)(d),g=r3(()=>new Map),[x,y]=(0,l.useState)(d),[b,v]=(0,l.useState)(d);rI(()=>{f.current=!1,m.current=d;for(let t=0;t<b.length;t++){let e=uS(b[t]);p.includes(e)?g.delete(e):!0!==g.get(e)&&g.set(e,!1)}},[b,p.length,p.join("-")]);let _=[];if(d!==x){let t=[...d];for(let e=0;e<b.length;e++){let i=b[e],s=uS(i);p.includes(s)||(t.splice(e,0,i),_.push(i))}return"wait"===n&&_.length&&(t=_),v(uT(t)),y(d),null}let{forceRender:w}=(0,l.useContext)(sn);return(0,o.jsx)(o.Fragment,{children:b.map(t=>{let l=uS(t),x=(!a||!!c)&&(d===b||p.includes(l));return(0,o.jsx)(uP,{isPresent:x,initial:(!f.current||!!i)&&void 0,custom:e,presenceAffectsLayout:r,mode:n,onExitComplete:x?void 0:()=>{if(!g.has(l))return;g.set(l,!0);let t=!0;g.forEach(e=>{e||(t=!1)}),t&&(w?.(),v(m.current),a&&u?.(),s&&s())},anchorX:h,children:t},l)})})},uj=nL("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),uA=nL("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),uC=nL("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),uD=nL("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),uN=nL("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);class uR{constructor(){this.predictions=[],this.STORAGE_KEY="vietlott-predictions",this.loadPredictions()}static getInstance(){return uR.instance||(uR.instance=new uR),uR.instance}getNeuralPatternNumbers(t){let e=t.slice(0,50),i=this.analyzePatterns(e),s={frequency:.3,recency:.25,gaps:.2,evenOdd:.15},r={};for(let t=1;t<=55;t++)r[t]=0;this.calculateFrequency(e).forEach(t=>{r[t.number]+=t.percentage*s.frequency}),e.slice(0,10).forEach((t,e)=>{t.result.forEach(t=>{r[t]+=(10-e)*s.recency})}),this.analyzeGaps(t).forEach(t=>{t.expectedNext&&(r[t.number]+=t.probability*s.gaps)});let n=this.getOptimalEvenOddBalance(i);for(let t=1;t<=55;t++){let e=t%2==0;(e&&"even"===n.needMore||!e&&"odd"===n.needMore)&&(r[t]+=s.evenOdd)}let a=this.getOptimalSum(e);return this.generateCandidatesForSum(r,a).slice(0,6)}getFibonacciPatternNumbers(t){let e=this.generateFibonacci(55),i=this.calculateFrequency(t.slice(0,100)),s=e.map(t=>({number:t,score:i.find(e=>e.number===t)?.percentage||0})).sort((t,e)=>e.score-t.score).slice(0,3),r=this.getComplementaryNumbers(s.map(t=>t.number),t,3);return[...s.map(t=>t.number),...r].sort((t,e)=>t-e)}getMLWeightedNumbers(t){let e=this.extractFeatures(t),i=this.calculateMLWeights(e),s=[];for(let t=1;t<=55;t++){let r;r=0+i.frequency*(e.frequency[t]||0)+i.recency*(e.recency[t]||0)+i.pattern*(e.pattern[t]||0)+i.position*(e.position[t]||0),s.push({number:t,weight:r})}return s.sort((t,e)=>e.weight-t.weight).slice(0,6).map(t=>t.number).sort((t,e)=>t-e)}getChaosTheoryNumbers(t){let e=t.slice(0,30),i=this.calculateChaosFactors(e).map(t=>Math.sin(t*Math.PI)),s=[],r=new Set;for(let t=0;t<6;t++){let e=Math.floor(55*Math.abs(i[t%i.length]))+1;for(;r.has(e);)e=e%55+1;s.push(e),r.add(e)}return s.sort((t,e)=>t-e)}storePrediction(t,e,i){let s={id:this.generateId(),date:new Date().toISOString().split("T")[0],predictedNumbers:t,algorithm:e,confidence:i,timestamp:Date.now()};return this.predictions.push(s),this.savePredictions(),s.id}comparePrediction(t,e){let i=this.predictions.find(e=>e.id===t);if(!i)return;let s=i.predictedNumbers.filter(t=>e.result.includes(t)).length;i.actualNumbers=e.result,i.powerNumber=e.powerNumber,i.matches=s,i.accuracy=s/6*100,this.savePredictions()}getAlgorithmPerformance(){return[...new Set(this.predictions.map(t=>t.algorithm))].map(t=>{let e=this.predictions.filter(e=>e.algorithm===t&&void 0!==e.matches);if(0===e.length)return{algorithmName:t,totalPredictions:0,averageMatches:0,bestMatch:0,accuracy:0,confidenceScore:0,lastUpdated:new Date().toISOString()};let i=e.reduce((t,e)=>t+(e.matches||0),0)/e.length,s=Math.max(...e.map(t=>t.matches||0)),r=e.reduce((t,e)=>t+(e.accuracy||0),0)/e.length,n=e.reduce((t,e)=>t+e.confidence,0)/e.length;return{algorithmName:t,totalPredictions:e.length,averageMatches:Math.round(100*i)/100,bestMatch:s,accuracy:Math.round(100*r)/100,confidenceScore:Math.round(100*n)/100,lastUpdated:new Date().toISOString()}})}getPredictions(){return[...this.predictions].sort((t,e)=>e.timestamp-t.timestamp)}analyzePatterns(t){let e=t.flatMap(t=>t.result),i=this.findConsecutivePatterns(e),s=this.analyzeEvenOdd(e),r=t.map(t=>t.result.reduce((t,e)=>t+e,0));return{consecutive:i,evenOdd:s,sumRange:{min:Math.min(...r),max:Math.max(...r),average:r.reduce((t,e)=>t+e,0)/r.length},gaps:this.analyzeNumberGaps(t),repeats:this.findRepeatingPatterns(t)}}calculateFrequency(t){let e={},i=6*t.length;return t.forEach(t=>{t.result.forEach(t=>{e[t]=(e[t]||0)+1})}),Object.entries(e).map(([t,e])=>({number:parseInt(t),percentage:e/i*100}))}analyzeGaps(t){let e={};for(let i=1;i<=55;i++){e[i]=[];let s=-1;t.forEach((t,r)=>{t.result.includes(i)&&(-1!==s&&e[i].push(r-s),s=r)})}return Object.entries(e).map(([e,i])=>{let s=i.length>0?i.reduce((t,e)=>t+e,0)/i.length:0,r=this.getCurrentGap(parseInt(e),t),n=r>=s?Math.min(r/s,2):0;return{number:parseInt(e),gap:r,probability:n,expectedNext:n>1.2}})}getCurrentGap(t,e){for(let i=0;i<e.length;i++)if(e[i].result.includes(t))return i;return e.length}generateFibonacci(t){let e=[1,1];for(;e[e.length-1]<t;)e.push(e[e.length-1]+e[e.length-2]);return e.filter(e=>e<=t)}getComplementaryNumbers(t,e,i){return this.calculateFrequency(e).filter(e=>!t.includes(e.number)).sort((t,e)=>e.percentage-t.percentage).slice(0,i).map(t=>t.number)}extractFeatures(t){return{frequency:this.calculateFrequency(t.slice(0,100)),recency:this.calculateRecency(t.slice(0,20)),pattern:this.calculatePatternScores(t.slice(0,50)),position:this.calculatePositionScores(t.slice(0,30))}}calculateMLWeights(t){return{frequency:.4,recency:.3,pattern:.2,position:.1}}calculateChaosFactors(t){return t.map((t,e)=>t.result.reduce((t,e)=>t+e,0)*this.calculateVariance(t.result)/(e+1))}calculateVariance(t){let e=t.reduce((t,e)=>t+e,0)/t.length;return t.reduce((t,i)=>t+Math.pow(i-e,2),0)/t.length}generateId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}loadPredictions(){}savePredictions(){}findConsecutivePatterns(t){return[]}analyzeEvenOdd(t){let e=t.filter(t=>t%2==0).length;return{even:e,odd:t.length-e}}analyzeNumberGaps(t){return[]}findRepeatingPatterns(t){return[]}getOptimalEvenOddBalance(t){return{needMore:t.evenOdd.even>t.evenOdd.odd?"odd":"even"}}getOptimalSum(t){let e=t.map(t=>t.result.reduce((t,e)=>t+e,0));return e.reduce((t,e)=>t+e,0)/e.length}generateCandidatesForSum(t,e){return Object.entries(t).sort(([,t],[,e])=>e-t).slice(0,12).map(([t])=>parseInt(t))}calculateRecency(t){return{}}calculatePatternScores(t){return{}}calculatePositionScores(t){return{}}}let uO=[{name:"Smart Frequency",description:"Recent + historical frequency analysis",icon:"\uD83E\uDDE0",color:"from-purple-500 to-indigo-500"},{name:"Gap Analysis",description:"Numbers due based on gap patterns",icon:"\uD83D\uDCCA",color:"from-blue-500 to-cyan-500"},{name:"Pattern Recognition",description:"Even/odd, sum, and sequence patterns",icon:"\uD83D\uDD0D",color:"from-green-500 to-emerald-500"},{name:"Ensemble Method",description:"Weighted combination of all algorithms",icon:"\uD83C\uDFAF",color:"from-orange-500 to-red-500"},{name:"Neural Pattern",description:"AI-inspired pattern recognition",icon:"\uD83E\uDD16",color:"from-pink-500 to-purple-500"},{name:"Fibonacci Sequence",description:"Mathematical Fibonacci patterns",icon:"\uD83C\uDF00",color:"from-teal-500 to-blue-500"},{name:"ML Weighted",description:"Machine learning weighted selection",icon:"⚡",color:"from-yellow-500 to-orange-500"},{name:"Chaos Theory",description:"Chaos theory-based prediction",icon:"\uD83C\uDF2A️",color:"from-gray-500 to-slate-600"}];function uL({data:t}){let[e,i]=(0,l.useState)(uO[0].name),[s,r]=(0,l.useState)(null),[n,a]=(0,l.useState)(!1),[h,c]=(0,l.useState)(!1),[u,d]=(0,l.useState)(!1),[p,f]=(0,l.useState)([]),[m,g]=(0,l.useState)([]),[x,y]=(0,l.useState)(""),[b,v]=(0,l.useState)(null),_=uv.getInstance(),w=uR.getInstance(),M=async e=>{if(0===t.length)return;a(!0),await new Promise(t=>setTimeout(t,800));let i=[];switch(e){case"Smart Frequency":i=_.getSmartFrequencyNumbers(t);break;case"Gap Analysis":i=_.getGapAnalysisNumbers(t);break;case"Pattern Recognition":i=_.getPatternBasedNumbers(t);break;case"Ensemble Method":default:i=_.getEnsembleNumbers(t);break;case"Neural Pattern":i=w.getNeuralPatternNumbers(t);break;case"Fibonacci Sequence":i=w.getFibonacciPatternNumbers(t);break;case"ML Weighted":i=w.getMLWeightedNumbers(t);break;case"Chaos Theory":i=w.getChaosTheoryNumbers(t)}let s=Math.round(_.calculateConfidence(i,t)),n=uO.find(t=>t.name===e);r({numbers:i,confidence:s,reasoning:n?.description||""}),a(!1)},P=t=>{i(t)},k=async()=>{s&&(await navigator.clipboard.writeText(s.numbers.join(", ")),c(!0),setTimeout(()=>c(!1),2e3))},S=()=>{f(w.getPredictions())},T=()=>{g(w.getAlgorithmPerformance())};return t.length?(0,o.jsxs)(nW,{className:"p-6",gradient:!0,children:[(0,o.jsxs)(nE.h2,{className:"text-xl font-bold text-gray-800 mb-6 flex items-center",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},children:[(0,o.jsx)(nF,{className:"mr-2 text-green-600",size:24}),"Number Suggestions"]}),(0,o.jsxs)(nE.div,{className:"mb-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3 flex items-center",children:[(0,o.jsx)(uj,{className:"mr-2",size:16}),"Choose Algorithm:"]}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:uO.map((t,i)=>(0,o.jsxs)(nE.button,{onClick:()=>P(t.name),className:`
                p-3 rounded-lg border-2 transition-all duration-200 text-left
                ${e===t.name?`border-blue-500 bg-gradient-to-r ${t.color} text-white shadow-lg`:"border-gray-200 bg-white hover:border-gray-300 hover:shadow-md"}
              `,initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.1*i},whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,o.jsxs)("div",{className:"flex items-center mb-1",children:[(0,o.jsx)("span",{className:"text-lg mr-2",children:t.icon}),(0,o.jsx)("span",{className:"font-medium",children:t.name})]}),(0,o.jsx)("p",{className:`text-xs ${e===t.name?"text-white/80":"text-gray-500"}`,children:t.description})]},t.name))})]}),(0,o.jsx)(uE,{mode:"wait",children:n?(0,o.jsxs)(nE.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-center py-8",children:[(0,o.jsx)(nE.div,{className:"w-16 h-16 border-4 border-green-200 border-t-green-600 rounded-full mx-auto mb-4",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),(0,o.jsx)("p",{className:"text-gray-600 font-medium",children:"Generating suggestions..."})]},"generating"):s?(0,o.jsxs)(nE.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{delay:.3},children:[(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,o.jsxs)("h3",{className:"font-semibold text-gray-800 flex items-center",children:[(0,o.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),"Your Lucky Numbers:"]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsx)(nH,{onClick:()=>{s&&(v(w.storePrediction(s.numbers,e,s.confidence)),S())},variant:"success",size:"sm",icon:(0,o.jsx)(uA,{size:16}),disabled:!s||null!==b,children:b?"Saved":"Save"}),(0,o.jsx)(nH,{onClick:()=>{M(e)},variant:"secondary",size:"sm",icon:(0,o.jsx)(nV,{size:16}),loading:n,children:"Refresh"})]})]}),(0,o.jsx)("div",{className:"flex justify-center space-x-3 mb-6",children:s.numbers.map((t,e)=>(0,o.jsx)(nX,{number:t,variant:"suggested",size:"lg",delay:.1*e},e))})]}),(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"bg-white/60 rounded-xl p-4 border border-white/20",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,o.jsxs)("span",{className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,o.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),"Confidence Level:"]}),(0,o.jsxs)("span",{className:"text-lg font-bold text-gray-800",children:[s.confidence,"%"]})]}),(0,o.jsx)(({confidence:t})=>(0,o.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 overflow-hidden",children:(0,o.jsx)(nE.div,{className:`h-3 rounded-full ${t>=70?"bg-gradient-to-r from-green-400 to-green-600":t>=40?"bg-gradient-to-r from-yellow-400 to-yellow-600":"bg-gradient-to-r from-red-400 to-red-600"}`,initial:{width:0},animate:{width:`${t}%`},transition:{duration:1,delay:.5}})}),{confidence:s.confidence}),(0,o.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Based on ",t.length," historical draws analysis"]})]}),(0,o.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200",children:[(0,o.jsxs)("h4",{className:"font-medium text-gray-800 mb-2 flex items-center",children:[(0,o.jsx)("span",{className:"text-blue-600 mr-2",children:"ℹ️"}),"Algorithm Insights:"]}),(0,o.jsx)("p",{className:"text-sm text-gray-700 mb-3",children:s.reasoning}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-600",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("span",{className:"w-1 h-1 bg-blue-500 rounded-full mr-2"}),t.length," draws analyzed"]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("span",{className:"w-1 h-1 bg-blue-500 rounded-full mr-2"}),"Numbers sorted ascending"]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("span",{className:"w-1 h-1 bg-blue-500 rounded-full mr-2"}),"Frequency-based confidence"]})]})]})]}),(0,o.jsx)(nE.div,{className:"mt-6 pt-4 border-t border-gray-200",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("span",{className:"text-sm text-gray-600 flex items-center",children:[(0,o.jsx)(uC,{size:14,className:"mr-2"}),"Quick copy:"]}),(0,o.jsx)(nH,{onClick:k,variant:"ghost",size:"sm",icon:h?(0,o.jsx)(uD,{size:16,className:"text-green-600"}):(0,o.jsx)(uC,{size:16}),className:h?"text-green-600 border-green-300":"",children:h?"Copied!":s.numbers.join(", ")})]})}),b&&(0,o.jsxs)(nE.div,{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-xl",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{delay:.5},children:[(0,o.jsxs)("h4",{className:"font-medium text-yellow-800 mb-3 flex items-center",children:[(0,o.jsx)(uN,{className:"mr-2",size:16}),"Compare with Actual Result"]}),(0,o.jsx)("p",{className:"text-sm text-yellow-700 mb-3",children:"Enter the actual lottery result to track algorithm performance:"}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsx)("input",{type:"text",value:x,onChange:t=>y(t.target.value),placeholder:"e.g., 09 37 42 45 46 50 14",className:"flex-1 px-3 py-2 border border-yellow-300 rounded-lg text-sm focus:ring-2 focus:ring-yellow-500 focus:border-transparent"}),(0,o.jsx)(nH,{onClick:()=>{if(x&&b)try{let t=x.trim().split(/\s+/),e=t.slice(0,6).map(t=>parseInt(t)),i=t.length>6?parseInt(t[6]):void 0,s={id:"manual",date:new Date().toISOString().split("T")[0],result:e,powerNumber:i};w.comparePrediction(b,s),S(),T(),y(""),v(null)}catch(t){console.error("Error parsing actual result:",t)}},variant:"primary",size:"sm",disabled:!x.trim(),children:"Compare"})]})]}),(0,o.jsx)(nE.div,{className:"mt-6 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.7},children:(0,o.jsxs)(nH,{onClick:()=>d(!u),variant:"ghost",size:"sm",icon:(0,o.jsx)(nZ,{size:16}),children:[u?"Hide":"Show"," Algorithm Performance"]})}),(0,o.jsx)(uE,{children:u&&(0,o.jsxs)(nE.div,{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:[(0,o.jsxs)("h4",{className:"font-medium text-blue-800 mb-4 flex items-center",children:[(0,o.jsx)(nZ,{className:"mr-2",size:16}),"Algorithm Performance Comparison"]}),m.length>0?(0,o.jsx)("div",{className:"space-y-3",children:m.sort((t,e)=>e.averageMatches-t.averageMatches).map((t,i)=>(0,o.jsxs)("div",{className:`p-3 rounded-lg border ${t.algorithmName===e?"border-blue-400 bg-blue-100":"border-blue-200 bg-white"}`,children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,o.jsxs)("span",{className:"font-medium text-blue-900",children:["#",i+1," ",t.algorithmName]}),(0,o.jsxs)("span",{className:"text-sm text-blue-600",children:[t.totalPredictions," predictions"]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-xs",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:"text-blue-600",children:"Avg Matches:"}),(0,o.jsx)("span",{className:"font-bold ml-1",children:t.averageMatches})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:"text-blue-600",children:"Best:"}),(0,o.jsxs)("span",{className:"font-bold ml-1",children:[t.bestMatch,"/6"]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:"text-blue-600",children:"Accuracy:"}),(0,o.jsxs)("span",{className:"font-bold ml-1",children:[t.accuracy,"%"]})]})]})]},t.algorithmName))}):(0,o.jsx)("p",{className:"text-blue-600 text-sm text-center py-4",children:"No performance data yet. Save predictions and compare with actual results to see algorithm performance."})]})}),p.length>0&&(0,o.jsxs)(nE.div,{className:"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-xl",initial:{opacity:0},animate:{opacity:1},transition:{delay:.9},children:[(0,o.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"Recent Predictions"}),(0,o.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:p.slice(0,5).map(t=>(0,o.jsxs)("div",{className:"flex justify-between items-center p-2 bg-white rounded border text-xs",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:"font-medium",children:t.algorithm}),(0,o.jsx)("span",{className:"text-gray-500 ml-2",children:t.date})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("span",{className:"text-gray-600",children:t.predictedNumbers.join(", ")}),void 0!==t.matches&&(0,o.jsxs)("span",{className:`px-2 py-1 rounded text-xs font-bold ${t.matches>=3?"bg-green-100 text-green-800":t.matches>=1?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:[t.matches,"/6"]})]})]},t.id))})]})]},"suggestion"):null})]}):(0,o.jsxs)(nW,{className:"p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-bold text-gray-800 mb-4 flex items-center",children:[(0,o.jsx)(nF,{className:"mr-2 text-green-600",size:24}),"Number Suggestions"]}),(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("div",{className:"text-gray-400 text-4xl mb-2",children:"\uD83C\uDFB2"}),(0,o.jsx)("p",{className:"text-gray-500",children:"No data available for suggestions"})]})]})}function uV({data:t}){let[e,i]=(0,l.useState)(1),[s,r]=(0,l.useState)(""),[n,a]=(0,l.useState)("date"),[h,c]=(0,l.useState)("desc"),u=[...t.filter(t=>t.id.toLowerCase().includes(s.toLowerCase())||t.date.includes(s)||t.result.some(t=>t.toString().includes(s)))].sort((t,e)=>{let i=0;return i="date"===n?new Date(t.date).getTime()-new Date(e.date).getTime():t.id.localeCompare(e.id),"asc"===h?i:-i}),d=Math.ceil(u.length/10),p=(e-1)*10,f=u.slice(p,p+10),m=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),g=({number:t,size:e="sm"})=>(0,o.jsx)("div",{className:`
        rounded-full bg-blue-500 text-white font-bold flex items-center justify-center
        ${"sm"===e?"w-8 h-8 text-sm":"w-6 h-6 text-xs"}
      `,children:t}),x=t=>{n===t?c("asc"===h?"desc":"asc"):(a(t),c("desc")),i(1)};return t.length?(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-bold text-gray-800 mb-4 flex items-center",children:[(0,o.jsx)("span",{className:"mr-2",children:"\uD83D\uDCCB"}),"Historical Data"]}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,o.jsx)("div",{className:"flex-1",children:(0,o.jsx)("input",{type:"text",placeholder:"Search by draw ID, date, or numbers...",value:s,onChange:t=>{r(t.target.value),i(1)},className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})}),(0,o.jsxs)("div",{className:"flex gap-2",children:[(0,o.jsxs)("button",{onClick:()=>x("date"),className:`px-3 py-2 rounded text-sm transition-colors flex items-center ${"date"===n?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:["Date ","date"===n&&("asc"===h?"↑":"↓")]}),(0,o.jsxs)("button",{onClick:()=>x("id"),className:`px-3 py-2 rounded text-sm transition-colors flex items-center ${"id"===n?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:["Draw ID ","id"===n&&("asc"===h?"↑":"↓")]})]})]}),(0,o.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:["Showing ",p+1,"-",Math.min(p+10,u.length)," of ",u.length," results",s&&` (filtered from ${t.length} total)`]}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full",children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,o.jsx)("th",{className:"text-left py-3 px-2 font-semibold text-gray-700",children:"Draw ID"}),(0,o.jsx)("th",{className:"text-left py-3 px-2 font-semibold text-gray-700",children:"Date"}),(0,o.jsx)("th",{className:"text-left py-3 px-2 font-semibold text-gray-700",children:"Numbers"}),(0,o.jsx)("th",{className:"text-left py-3 px-2 font-semibold text-gray-700",children:"Power"})]})}),(0,o.jsx)("tbody",{children:f.map((t,e)=>(0,o.jsxs)("tr",{className:`border-b border-gray-100 hover:bg-gray-50 transition-colors ${e%2==0?"bg-white":"bg-gray-50"}`,children:[(0,o.jsx)("td",{className:"py-3 px-2 font-mono text-sm",children:t.id}),(0,o.jsx)("td",{className:"py-3 px-2 text-sm",children:m(t.date)}),(0,o.jsx)("td",{className:"py-3 px-2",children:(0,o.jsx)("div",{className:"flex space-x-1",children:t.result.map((t,e)=>(0,o.jsx)(g,{number:t,size:"xs"},e))})}),(0,o.jsx)("td",{className:"py-3 px-2",children:t.powerNumber&&(0,o.jsx)("div",{className:"w-6 h-6 rounded-full bg-red-500 text-white font-bold flex items-center justify-center text-xs",children:t.powerNumber})})]},t.id))})]})}),d>1&&(0,o.jsxs)("div",{className:"flex justify-center items-center space-x-2 mt-6",children:[(0,o.jsx)("button",{onClick:()=>i(Math.max(1,e-1)),disabled:1===e,className:"px-3 py-1 rounded text-sm bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,o.jsx)("div",{className:"flex space-x-1",children:Array.from({length:Math.min(5,d)},(t,s)=>{let r;return r=d<=5||e<=3?s+1:e>=d-2?d-4+s:e-2+s,(0,o.jsx)("button",{onClick:()=>i(r),className:`px-3 py-1 rounded text-sm transition-colors ${e===r?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:r},r)})}),(0,o.jsx)("button",{onClick:()=>i(Math.min(d,e+1)),disabled:e===d,className:"px-3 py-1 rounded text-sm bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]}):(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,o.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"Historical Data"}),(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("div",{className:"text-gray-400 text-4xl mb-2",children:"\uD83D\uDCCB"}),(0,o.jsx)("p",{className:"text-gray-500",children:"No historical data available"})]})]})}function uF({size:t="md",color:e="blue-600",text:i="Loading..."}){return(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-4",children:[(0,o.jsx)(nE.div,{className:`${{sm:"w-8 h-8",md:"w-12 h-12",lg:"w-16 h-16"}[t]} border-4 border-gray-200 border-t-${e} rounded-full`,animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),(0,o.jsx)(nE.p,{className:`${{sm:"text-sm",md:"text-base",lg:"text-lg"}[t]} text-gray-600 font-medium`,initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:i})]})}function uI(){let[t,e]=(0,l.useState)([]),[i,s]=(0,l.useState)(!0),[r,n]=(0,l.useState)(null),[a,h]=(0,l.useState)(!1),[c,u]=(0,l.useState)("power655"),d=uv.getInstance(),p=n$.getInstance(),f=async(t=!1,i)=>{try{s(!t),h(t),n(null),t&&d.clearCache();let r=i||c,a=await fetch(`/api/lottery-data?type=${r}`);if(!a.ok)throw Error(`HTTP error! status: ${a.status}`);let o=await a.json();e(o)}catch(t){n(t instanceof Error?t.message:"An error occurred")}finally{s(!1),h(!1)}};return i?(0,o.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center",children:(0,o.jsx)(uF,{size:"lg",text:"Loading lottery data..."})}):r?(0,o.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-white to-pink-50 flex items-center justify-center",children:(0,o.jsxs)(nE.div,{className:"text-center max-w-md mx-auto p-8",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},children:[(0,o.jsx)(nE.div,{className:"text-red-500 text-6xl mb-4",animate:{rotate:[0,-10,10,-10,0]},transition:{duration:.5,delay:.2},children:"⚠️"}),(0,o.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Error Loading Data"}),(0,o.jsx)("p",{className:"text-gray-600 mb-6",children:r}),(0,o.jsx)(nH,{onClick:()=>f(!0),variant:"primary",icon:(0,o.jsx)(nV,{size:16}),loading:a,children:"Retry"})]})}):(0,o.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,o.jsx)(nE.header,{className:"bg-white/80 backdrop-blur-sm shadow-lg border-b border-white/20",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)(nE.h1,{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent flex items-center",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,o.jsx)(nF,{className:"mr-3 text-blue-600",size:32}),"Vietlott Analyzer"]}),(0,o.jsx)(nE.p,{className:"text-gray-600 mt-2 text-lg",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.3},children:"AI-powered analysis for Power 6/55 & Mega 6/45"})]}),(0,o.jsxs)(nE.div,{className:"flex items-center space-x-4",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.4},children:[(0,o.jsxs)("div",{className:"text-right",children:[(0,o.jsx)("p",{className:"text-sm text-gray-500",children:"Total Draws"}),(0,o.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:t.length})]}),(0,o.jsx)(nH,{onClick:()=>f(!0),variant:"secondary",size:"sm",icon:(0,o.jsx)(nV,{size:16}),loading:a,children:"Refresh Data"})]})]})})}),(0,o.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,o.jsx)(nE.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,o.jsx)(nU,{currentType:c,onTypeChange:t=>{u(t),p.setCurrentLotteryType(t),f(!0,t)}})}),(0,o.jsxs)(nE.div,{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,o.jsx)(nK,{data:t}),(0,o.jsx)(uL,{data:t})]}),(0,o.jsx)(nE.div,{className:"mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:(0,o.jsx)(u_,{data:t})}),(0,o.jsx)(nE.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.9},children:(0,o.jsx)(uV,{data:t})})]}),(0,o.jsx)(nE.footer,{className:"bg-white/60 backdrop-blur-sm border-t border-white/20 mt-16",initial:{opacity:0},animate:{opacity:1},transition:{delay:1.1},children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,o.jsxs)("div",{className:"text-center text-gray-600",children:[(0,o.jsx)("p",{className:"text-sm",children:"⚠️ This application is for educational and entertainment purposes only."}),(0,o.jsx)("p",{className:"text-xs mt-1",children:"Lottery numbers are random. Past results do not guarantee future outcomes. Please gamble responsibly."})]})})})]})}},5362:t=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{function t(t,e){void 0===e&&(e={});for(var i=function(t){for(var e=[],i=0;i<t.length;){var s=t[i];if("*"===s||"+"===s||"?"===s){e.push({type:"MODIFIER",index:i,value:t[i++]});continue}if("\\"===s){e.push({type:"ESCAPED_CHAR",index:i++,value:t[i++]});continue}if("{"===s){e.push({type:"OPEN",index:i,value:t[i++]});continue}if("}"===s){e.push({type:"CLOSE",index:i,value:t[i++]});continue}if(":"===s){for(var r="",n=i+1;n<t.length;){var a=t.charCodeAt(n);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){r+=t[n++];continue}break}if(!r)throw TypeError("Missing parameter name at "+i);e.push({type:"NAME",index:i,value:r}),i=n;continue}if("("===s){var o=1,l="",n=i+1;if("?"===t[n])throw TypeError('Pattern cannot start with "?" at '+n);for(;n<t.length;){if("\\"===t[n]){l+=t[n++]+t[n++];continue}if(")"===t[n]){if(0==--o){n++;break}}else if("("===t[n]&&(o++,"?"!==t[n+1]))throw TypeError("Capturing groups are not allowed at "+n);l+=t[n++]}if(o)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);e.push({type:"PATTERN",index:i,value:l}),i=n;continue}e.push({type:"CHAR",index:i,value:t[i++]})}return e.push({type:"END",index:i,value:""}),e}(t),s=e.prefixes,n=void 0===s?"./":s,a="[^"+r(e.delimiter||"/#?")+"]+?",o=[],l=0,h=0,c="",u=function(t){if(h<i.length&&i[h].type===t)return i[h++].value},d=function(t){var e=u(t);if(void 0!==e)return e;var s=i[h];throw TypeError("Unexpected "+s.type+" at "+s.index+", expected "+t)},p=function(){for(var t,e="";t=u("CHAR")||u("ESCAPED_CHAR");)e+=t;return e};h<i.length;){var f=u("CHAR"),m=u("NAME"),g=u("PATTERN");if(m||g){var x=f||"";-1===n.indexOf(x)&&(c+=x,x=""),c&&(o.push(c),c=""),o.push({name:m||l++,prefix:x,suffix:"",pattern:g||a,modifier:u("MODIFIER")||""});continue}var y=f||u("ESCAPED_CHAR");if(y){c+=y;continue}if(c&&(o.push(c),c=""),u("OPEN")){var x=p(),b=u("NAME")||"",v=u("PATTERN")||"",_=p();d("CLOSE"),o.push({name:b||(v?l++:""),pattern:b&&!v?a:v,prefix:x,suffix:_,modifier:u("MODIFIER")||""});continue}d("END")}return o}function i(t,e){void 0===e&&(e={});var i=n(e),s=e.encode,r=void 0===s?function(t){return t}:s,a=e.validate,o=void 0===a||a,l=t.map(function(t){if("object"==typeof t)return RegExp("^(?:"+t.pattern+")$",i)});return function(e){for(var i="",s=0;s<t.length;s++){var n=t[s];if("string"==typeof n){i+=n;continue}var a=e?e[n.name]:void 0,h="?"===n.modifier||"*"===n.modifier,c="*"===n.modifier||"+"===n.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+n.name+'" to not repeat, but got an array');if(0===a.length){if(h)continue;throw TypeError('Expected "'+n.name+'" to not be empty')}for(var u=0;u<a.length;u++){var d=r(a[u],n);if(o&&!l[s].test(d))throw TypeError('Expected all "'+n.name+'" to match "'+n.pattern+'", but got "'+d+'"');i+=n.prefix+d+n.suffix}continue}if("string"==typeof a||"number"==typeof a){var d=r(String(a),n);if(o&&!l[s].test(d))throw TypeError('Expected "'+n.name+'" to match "'+n.pattern+'", but got "'+d+'"');i+=n.prefix+d+n.suffix;continue}if(!h){var p=c?"an array":"a string";throw TypeError('Expected "'+n.name+'" to be '+p)}}return i}}function s(t,e,i){void 0===i&&(i={});var s=i.decode,r=void 0===s?function(t){return t}:s;return function(i){var s=t.exec(i);if(!s)return!1;for(var n=s[0],a=s.index,o=Object.create(null),l=1;l<s.length;l++)!function(t){if(void 0!==s[t]){var i=e[t-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=s[t].split(i.prefix+i.suffix).map(function(t){return r(t,i)}):o[i.name]=r(s[t],i)}}(l);return{path:n,index:a,params:o}}}function r(t){return t.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function n(t){return t&&t.sensitive?"":"i"}function a(t,e,i){void 0===i&&(i={});for(var s=i.strict,a=void 0!==s&&s,o=i.start,l=i.end,h=i.encode,c=void 0===h?function(t){return t}:h,u="["+r(i.endsWith||"")+"]|$",d="["+r(i.delimiter||"/#?")+"]",p=void 0===o||o?"^":"",f=0;f<t.length;f++){var m=t[f];if("string"==typeof m)p+=r(c(m));else{var g=r(c(m.prefix)),x=r(c(m.suffix));if(m.pattern)if(e&&e.push(m),g||x)if("+"===m.modifier||"*"===m.modifier){var y="*"===m.modifier?"?":"";p+="(?:"+g+"((?:"+m.pattern+")(?:"+x+g+"(?:"+m.pattern+"))*)"+x+")"+y}else p+="(?:"+g+"("+m.pattern+")"+x+")"+m.modifier;else p+="("+m.pattern+")"+m.modifier;else p+="(?:"+g+x+")"+m.modifier}}if(void 0===l||l)a||(p+=d+"?"),p+=i.endsWith?"(?="+u+")":"$";else{var b=t[t.length-1],v="string"==typeof b?d.indexOf(b[b.length-1])>-1:void 0===b;a||(p+="(?:"+d+"(?="+u+"))?"),v||(p+="(?="+d+"|"+u+")")}return new RegExp(p,n(i))}function o(e,i,s){if(e instanceof RegExp){if(!i)return e;var r=e.source.match(/\((?!\?)/g);if(r)for(var l=0;l<r.length;l++)i.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return e}return Array.isArray(e)?RegExp("(?:"+e.map(function(t){return o(t,i,s).source}).join("|")+")",n(s)):a(t(e,s),i,s)}Object.defineProperty(e,"__esModule",{value:!0}),e.parse=t,e.compile=function(e,s){return i(t(e,s),s)},e.tokensToFunction=i,e.match=function(t,e){var i=[];return s(o(t,i,e),i,e)},e.regexpToFunction=s,e.tokensToRegexp=a,e.pathToRegexp=o})(),t.exports=e})()},5520:(t,e,i)=>{Promise.resolve().then(i.bind(i,5034))},5526:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{compileNonPath:function(){return c},matchHas:function(){return h},parseDestination:function(){return u},prepareDestination:function(){return d}});let s=i(5362),r=i(3293),n=i(6759),a=i(1437),o=i(8212);function l(t){return t.replace(/__ESC_COLON_/gi,":")}function h(t,e,i,s){void 0===i&&(i=[]),void 0===s&&(s=[]);let r={},n=i=>{let s,n=i.key;switch(i.type){case"header":n=n.toLowerCase(),s=t.headers[n];break;case"cookie":s="cookies"in t?t.cookies[i.key]:(0,o.getCookieParser)(t.headers)()[i.key];break;case"query":s=e[n];break;case"host":{let{host:e}=(null==t?void 0:t.headers)||{};s=null==e?void 0:e.split(":",1)[0].toLowerCase()}}if(!i.value&&s)return r[function(t){let e="";for(let i=0;i<t.length;i++){let s=t.charCodeAt(i);(s>64&&s<91||s>96&&s<123)&&(e+=t[i])}return e}(n)]=s,!0;if(s){let t=RegExp("^"+i.value+"$"),e=Array.isArray(s)?s.slice(-1)[0].match(t):s.match(t);if(e)return Array.isArray(e)&&(e.groups?Object.keys(e.groups).forEach(t=>{r[t]=e.groups[t]}):"host"===i.type&&e[0]&&(r.host=e[0])),!0}return!1};return!(!i.every(t=>n(t))||s.some(t=>n(t)))&&r}function c(t,e){if(!t.includes(":"))return t;for(let i of Object.keys(e))t.includes(":"+i)&&(t=t.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return t=t.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,s.compile)("/"+t,{validate:!1})(e).slice(1)}function u(t){let e=t.destination;for(let i of Object.keys({...t.params,...t.query}))i&&(e=e.replace(RegExp(":"+(0,r.escapeStringRegexp)(i),"g"),"__ESC_COLON_"+i));let i=(0,n.parseUrl)(e),s=i.pathname;s&&(s=l(s));let a=i.href;a&&(a=l(a));let o=i.hostname;o&&(o=l(o));let h=i.hash;return h&&(h=l(h)),{...i,pathname:s,hostname:o,href:a,hash:h}}function d(t){let e,i,r=Object.assign({},t.query),n=u(t),{hostname:o,query:h}=n,d=n.pathname;n.hash&&(d=""+d+n.hash);let p=[],f=[];for(let t of((0,s.pathToRegexp)(d,f),f))p.push(t.name);if(o){let t=[];for(let e of((0,s.pathToRegexp)(o,t),t))p.push(e.name)}let m=(0,s.compile)(d,{validate:!1});for(let[i,r]of(o&&(e=(0,s.compile)(o,{validate:!1})),Object.entries(h)))Array.isArray(r)?h[i]=r.map(e=>c(l(e),t.params)):"string"==typeof r&&(h[i]=c(l(r),t.params));let g=Object.keys(t.params).filter(t=>"nextInternalLocale"!==t);if(t.appendParamsToQuery&&!g.some(t=>p.includes(t)))for(let e of g)e in h||(h[e]=t.params[e]);if((0,a.isInterceptionRouteAppPath)(d))for(let e of d.split("/")){let i=a.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));if(i){"(..)(..)"===i?(t.params["0"]="(..)",t.params["1"]="(..)"):t.params["0"]=i;break}}try{let[s,r]=(i=m(t.params)).split("#",2);e&&(n.hostname=e(t.params)),n.pathname=s,n.hash=(r?"#":"")+(r||""),delete n.search}catch(t){if(t.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw t}return n.query={...r,...n.query},{newUrl:i,destQuery:h,parsedDestination:n}}},5531:(t,e)=>{"use strict";function i(t){return t.startsWith("/")?t:"/"+t}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},5792:(t,e,i)=>{Promise.resolve().then(i.bind(i,1204))},6341:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{getPreviouslyRevalidatedTags:function(){return x},getUtils:function(){return g},interpolateDynamicPath:function(){return f},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return p}});let s=i(9551),r=i(1959),n=i(2437),a=i(4396),o=i(8034),l=i(5526),h=i(2887),c=i(4722),u=i(6143),d=i(7912);function p(t,e,i){let r=(0,s.parse)(t.url,!0);for(let t of(delete r.search,Object.keys(r.query))){let s=t!==u.NEXT_QUERY_PARAM_PREFIX&&t.startsWith(u.NEXT_QUERY_PARAM_PREFIX),n=t!==u.NEXT_INTERCEPTION_MARKER_PREFIX&&t.startsWith(u.NEXT_INTERCEPTION_MARKER_PREFIX);(s||n||e.includes(t)||i&&Object.keys(i.groups).includes(t))&&delete r.query[t]}t.url=(0,s.format)(r)}function f(t,e,i){if(!i)return t;for(let s of Object.keys(i.groups)){let r,{optional:n,repeat:a}=i.groups[s],o=`[${a?"...":""}${s}]`;n&&(o=`[${o}]`);let l=e[s];r=Array.isArray(l)?l.map(t=>t&&encodeURIComponent(t)).join("/"):l?encodeURIComponent(l):"",t=t.replaceAll(o,r)}return t}function m(t,e,i,s){let r={};for(let n of Object.keys(e.groups)){let a=t[n];"string"==typeof a?a=(0,c.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(c.normalizeRscURL));let o=i[n],l=e.groups[n].optional;if((Array.isArray(o)?o.some(t=>Array.isArray(a)?a.some(e=>e.includes(t)):null==a?void 0:a.includes(t)):null==a?void 0:a.includes(o))||void 0===a&&!(l&&s))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${n}]]`))&&(a=void 0,delete t[n]),a&&"string"==typeof a&&e.groups[n].repeat&&(a=a.split("/")),a&&(r[n]=a)}return{params:r,hasValidParams:!0}}function g({page:t,i18n:e,basePath:i,rewrites:s,pageIsDynamic:c,trailingSlash:u,caseSensitive:g}){let x,y,b;return c&&(x=(0,a.getNamedRouteRegex)(t,{prefixRouteKeys:!1}),b=(y=(0,o.getRouteMatcher)(x))(t)),{handleRewrites:function(a,o){let d={},p=o.pathname,f=s=>{let h=(0,n.getPathMatch)(s.source+(u?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let f=h(o.pathname);if((s.has||s.missing)&&f){let t=(0,l.matchHas)(a,o.query,s.has,s.missing);t?Object.assign(f,t):f=!1}if(f){let{parsedDestination:n,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:s.destination,params:f,query:o.query});if(n.protocol)return!0;if(Object.assign(d,a,f),Object.assign(o.query,n.query),delete n.query,Object.assign(o,n),!(p=o.pathname))return!1;if(i&&(p=p.replace(RegExp(`^${i}`),"")||"/"),e){let t=(0,r.normalizeLocalePath)(p,e.locales);p=t.pathname,o.query.nextInternalLocale=t.detectedLocale||f.nextInternalLocale}if(p===t)return!0;if(c&&y){let t=y(p);if(t)return o.query={...o.query,...t},!0}}return!1};for(let t of s.beforeFiles||[])f(t);if(p!==t){let e=!1;for(let t of s.afterFiles||[])if(e=f(t))break;if(!e&&!(()=>{let e=(0,h.removeTrailingSlash)(p||"");return e===(0,h.removeTrailingSlash)(t)||(null==y?void 0:y(e))})()){for(let t of s.fallback||[])if(e=f(t))break}}return d},defaultRouteRegex:x,dynamicRouteMatcher:y,defaultRouteMatches:b,getParamsFromRouteMatches:function(t){if(!x)return null;let{groups:e,routeKeys:i}=x,s=(0,o.getRouteMatcher)({re:{exec:t=>{let s=Object.fromEntries(new URLSearchParams(t));for(let[t,e]of Object.entries(s)){let i=(0,d.normalizeNextQueryParam)(t);i&&(s[i]=e,delete s[t])}let r={};for(let t of Object.keys(i)){let n=i[t];if(!n)continue;let a=e[n],o=s[t];if(!a.optional&&!o)return null;r[a.pos]=o}return r}},groups:e})(t);return s||null},normalizeDynamicRouteParams:(t,e)=>x&&b?m(t,x,b,e):{params:{},hasValidParams:!1},normalizeVercelUrl:(t,e)=>p(t,e,x),interpolateDynamicPath:(t,e)=>f(t,e,x)}}function x(t,e){return"string"==typeof t[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&t[u.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===e?t[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:t=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{e.parse=function(e,i){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},n=e.split(s),a=(i||{}).decode||t,o=0;o<n.length;o++){var l=n[o],h=l.indexOf("=");if(!(h<0)){var c=l.substr(0,h).trim(),u=l.substr(++h,l.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==r[c]&&(r[c]=function(t,e){try{return e(t)}catch(e){return t}}(u,a))}}return r},e.serialize=function(t,e,s){var n=s||{},a=n.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(t))throw TypeError("argument name is invalid");var o=a(e);if(o&&!r.test(o))throw TypeError("argument val is invalid");var l=t+"="+o;if(null!=n.maxAge){var h=n.maxAge-0;if(isNaN(h)||!isFinite(h))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(h)}if(n.domain){if(!r.test(n.domain))throw TypeError("option domain is invalid");l+="; Domain="+n.domain}if(n.path){if(!r.test(n.path))throw TypeError("option path is invalid");l+="; Path="+n.path}if(n.expires){if("function"!=typeof n.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(l+="; HttpOnly"),n.secure&&(l+="; Secure"),n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var t=decodeURIComponent,i=encodeURIComponent,s=/; */,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),t.exports=e})()},6730:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>d,tree:()=>h});var s=i(5239),r=i(8088),n=i(8170),a=i.n(n),o=i(893),l={};for(let t in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>o[t]);i.d(e,l);let h={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1204)),"C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\page.tsx"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Work\\Automation\\Draff\\vietlott-analyzer\\src\\app\\page.tsx"],u={require:i,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},6759:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parseUrl",{enumerable:!0,get:function(){return n}});let s=i(2785),r=i(3736);function n(t){if(t.startsWith("/"))return(0,r.parseRelativeUrl)(t);let e=new URL(t);return{hash:e.hash,hostname:e.hostname,href:e.href,pathname:e.pathname,port:e.port,protocol:e.protocol,query:(0,s.searchParamsToUrlQuery)(e.searchParams),search:e.search}}},8034:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getRouteMatcher",{enumerable:!0,get:function(){return r}});let s=i(4827);function r(t){let{re:e,groups:i}=t;return t=>{let r=e.exec(t);if(!r)return!1;let n=t=>{try{return decodeURIComponent(t)}catch(t){throw Object.defineProperty(new s.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[t,e]of Object.entries(i)){let i=r[e.pos];void 0!==i&&(e.repeat?a[t]=i.split("/").map(t=>n(t)):a[t]=n(i))}return a}}},8212:(t,e,i)=>{"use strict";function s(t){return function(){let{cookie:e}=t;if(!e)return{};let{parse:s}=i(6415);return s(Array.isArray(e)?e.join("; "):e)}}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getCookieParser",{enumerable:!0,get:function(){return s}})},8304:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return u},isMetadataRoute:function(){return d},isMetadataRouteFile:function(){return h},isStaticMetadataRoute:function(){return c}});let s=i(2958),r=i(4722),n=i(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(t,e)=>e&&0!==e.length?`(?:\\.(${t.join("|")})|(\\.(${e.join("|")})))`:`(\\.(?:${t.join("|")}))`;function h(t,e,i){let r=(i?"":"?")+"$",n=`\\d?${i?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(e.concat("txt"),null)}${r}`),RegExp(`^[\\\\/]manifest${l(e.concat("webmanifest","json"),null)}${r}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],e)}${r}`),RegExp(`[\\\\/]${a.icon.filename}${n}${l(a.icon.extensions,e)}${r}`),RegExp(`[\\\\/]${a.apple.filename}${n}${l(a.apple.extensions,e)}${r}`),RegExp(`[\\\\/]${a.openGraph.filename}${n}${l(a.openGraph.extensions,e)}${r}`),RegExp(`[\\\\/]${a.twitter.filename}${n}${l(a.twitter.extensions,e)}${r}`)],h=(0,s.normalizePathSep)(t);return o.some(t=>t.test(h))}function c(t){let e=t.replace(/\/route$/,"");return(0,n.isAppRouteRoute)(t)&&h(e,[],!0)&&"/robots.txt"!==e&&"/manifest.webmanifest"!==e&&!e.endsWith("/sitemap.xml")}function u(t){return!(0,n.isAppRouteRoute)(t)&&h(t,[],!1)}function d(t){let e=(0,r.normalizeAppPath)(t).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==e[0]&&(e="/"+e),(0,n.isAppRouteRoute)(t)&&h(e,[],!1)}},9121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9403:()=>{},9551:t=>{"use strict";t.exports=require("url")},9821:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,7924,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))}};var e=require("../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[447,145],()=>i(6730));module.exports=s})();