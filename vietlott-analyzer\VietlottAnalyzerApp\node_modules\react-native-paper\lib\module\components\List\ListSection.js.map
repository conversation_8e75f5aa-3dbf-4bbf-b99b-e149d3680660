{"version": 3, "names": ["React", "StyleSheet", "View", "ListSubheader", "useInternalTheme", "ListSection", "children", "title", "titleStyle", "style", "theme", "themeOverrides", "rest", "viewProps", "createElement", "_extends", "styles", "container", "displayName", "create", "marginVertical"], "sourceRoot": "../../../../src", "sources": ["components/List/ListSection.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAEEC,UAAU,EAEVC,IAAI,QAEC,cAAc;AAErB,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,gBAAgB,QAAQ,oBAAoB;AAuBrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,KAAK;EACLC,UAAU;EACVC,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,KAAK;EACX,MAAMF,KAAK,GAAGN,gBAAgB,CAACO,cAAc,CAAC;EAC9C,MAAME,SAAS,GAAG;IAAE,GAAGD,IAAI;IAAEF;EAAM,CAAC;EAEpC,oBACEV,KAAA,CAAAc,aAAA,CAACZ,IAAI,EAAAa,QAAA,KAAKF,SAAS;IAAEJ,KAAK,EAAE,CAACO,MAAM,CAACC,SAAS,EAAER,KAAK;EAAE,IACnDF,KAAK,gBACJP,KAAA,CAAAc,aAAA,CAACX,aAAa;IAACM,KAAK,EAAED,UAAW;IAACE,KAAK,EAAEA;EAAM,GAC5CH,KACY,CAAC,GACd,IAAI,EACPD,QACG,CAAC;AAEX,CAAC;AAEDD,WAAW,CAACa,WAAW,GAAG,cAAc;AAExC,MAAMF,MAAM,GAAGf,UAAU,CAACkB,MAAM,CAAC;EAC/BF,SAAS,EAAE;IACTG,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,eAAef,WAAW", "ignoreList": []}