{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_AnimatedText", "_helpers", "e", "__esModule", "default", "InputLabel", "props", "labeled", "wiggle", "error", "focused", "opacity", "labelLayoutWidth", "labelLayoutHeight", "labelBackground", "label", "labelError", "onLayoutAnimatedText", "onLabelTextLayout", "hasActiveOutline", "activeColor", "placeholder<PERSON><PERSON><PERSON>", "baseLabelTranslateX", "baseLabelTranslateY", "font", "fontSize", "lineHeight", "fontWeight", "placeholderOpacity", "wiggleOffsetX", "labelScale", "topPosition", "paddingLeft", "paddingRight", "backgroundColor", "roundness", "placeholderColor", "errorColor", "labelTranslationXOffset", "maxFontSizeMultiplier", "testID", "isV3", "inputContainerLayout", "scaledLabel", "INPUT_PADDING_HORIZONTAL", "getConstants", "width", "useWindowDimensions", "isWeb", "Platform", "OS", "paddingOffset", "labelTranslationX", "transform", "translateX", "interpolate", "inputRange", "outputRange", "labelStyle", "translateY", "scale", "labelWidth", "commonStyles", "top", "max<PERSON><PERSON><PERSON>", "textColor", "createElement", "View", "pointerEvents", "style", "StyleSheet", "absoluteFill", "styles", "overflow", "labelContainer", "Animated", "variant", "onLayout", "onTextLayout", "color", "numberOfLines", "create", "zIndex", "_default", "exports", "React", "memo"], "sourceRoot": "../../../../../src", "sources": ["components/TextInput/Label/InputLabel.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AASA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAA0C,SAAAD,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAG1C,MAAMG,UAAU,GAAIC,KAAsB,IAAK;EAC7C,MAAM;IACJC,OAAO;IACPC,MAAM;IACNC,KAAK;IACLC,OAAO;IACPC,OAAO;IACPC,gBAAgB;IAChBC,iBAAiB;IACjBC,eAAe;IACfC,KAAK;IACLC,UAAU;IACVC,oBAAoB;IACpBC,iBAAiB;IACjBC,gBAAgB;IAChBC,WAAW;IACXC,gBAAgB;IAChBC,mBAAmB;IACnBC,mBAAmB;IACnBC,IAAI;IACJC,QAAQ;IACRC,UAAU;IACVC,UAAU;IACVC,kBAAkB;IAClBC,aAAa;IACbC,UAAU;IACVC,WAAW;IACXC,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,SAAS;IACTC,gBAAgB;IAChBC,UAAU;IACVC,uBAAuB;IACvBC,qBAAqB;IACrBC,MAAM;IACNC,IAAI;IACJC,oBAAoB;IACpBC;EACF,CAAC,GAAGrC,KAAK;EAET,MAAM;IAAEsC;EAAyB,CAAC,GAAG,IAAAC,qBAAY,EAACJ,IAAI,CAAC;EACvD,MAAM;IAAEK;EAAM,CAAC,GAAG,IAAAC,gCAAmB,EAAC,CAAC;EAEvC,MAAMC,KAAK,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;EAEnC,MAAMC,aAAa,GACjBnB,WAAW,IAAIC,YAAY,GAAG;IAAED,WAAW;IAAEC;EAAa,CAAC,GAAG,CAAC,CAAC;EAElE,MAAMmB,iBAAiB,GAAG;IACxBC,SAAS,EAAE,CACT;MACE;MACAC,UAAU,EAAE/C,OAAO,CAACgD,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,WAAW,EAAE,CAACnC,mBAAmB,EAAEgB,uBAAuB,IAAI,CAAC;MACjE,CAAC;IACH,CAAC;EAEL,CAAC;EAED,MAAMoB,UAAU,GAAG;IACjB,GAAGlC,IAAI;IACPC,QAAQ;IACRC,UAAU;IACVC,UAAU;IACVhB,OAAO,EAAEJ,OAAO,CAACgD,WAAW,CAAC;MAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAACtC,gBAAgB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;IAC3C,CAAC,CAAC;IACFkC,SAAS,EAAE,CACT;MACE;MACAC,UAAU,EAAE9C,MAAM,GACdC,KAAK,CAAC8C,WAAW,CAAC;QAChBC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACvBC,WAAW,EAAE,CAAC,CAAC,EAAE5B,aAAa,EAAE,CAAC;MACnC,CAAC,CAAC,GACF;IACN,CAAC,EACD;MACE;MACA8B,UAAU,EACRpC,mBAAmB,KAAK,CAAC,GACrBhB,OAAO,CAACgD,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,WAAW,EAAE,CAAClC,mBAAmB,EAAE,CAAC;MACtC,CAAC,CAAC,GACF;IACR,CAAC,EACD;MACE;MACAqC,KAAK,EACH9B,UAAU,KAAK,CAAC,GACZvB,OAAO,CAACgD,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,WAAW,EAAE,CAAC3B,UAAU,EAAE,CAAC;MAC7B,CAAC,CAAC,GACFvB;IACR,CAAC;EAEL,CAAC;EAED,MAAMsD,UAAU,GACd,CAACnB,oBAAoB,CAACI,KAAK,GAAGF,wBAAwB,GAAG,CAAC,KACzDD,WAAW,GAAGb,UAAU,GAAG,CAAC,CAAC;EAEhC,MAAMgC,YAAY,GAAG,CACnBzC,gBAAgB,EAChB;IACE0C,GAAG,EAAEhC;EACP,CAAC,EACD;IACEiC,QAAQ,EAAEH;EACZ,CAAC,EACDH,UAAU,EACVP,aAAa,IAAI,CAAC,CAAC,CACpB;EAED,MAAMc,SAAS,GACbjD,UAAU,IAAIqB,UAAU,GAAGA,UAAU,GAAGD,gBAC3B;EAEf;IAAA;IACE;IACA;IACAxC,MAAA,CAAAQ,OAAA,CAAA8D,aAAA,CAACnE,YAAA,CAAAoE,IAAI;MACHC,aAAa,EAAC,MAAM;MACpBC,KAAK,EAAE,CAACC,uBAAU,CAACC,YAAY,EAAEC,MAAM,CAACC,QAAQ,EAAED,MAAM,CAACE,cAAc;IAAE,gBAEzE9E,MAAA,CAAAQ,OAAA,CAAA8D,aAAA,CAACnE,YAAA,CAAA4E,QAAQ,CAACR,IAAI;MACZC,aAAa,EAAC,MAAM;MACpBC,KAAK,EAAE,CACLC,uBAAU,CAACC,YAAY,EACvB,CAACvB,KAAK,IAAI;QAAEF;MAAM,CAAC,EACnB;QAAEnC;MAAQ,CAAC,EACXyC,iBAAiB;IACjB,gBAEFxD,MAAA,CAAAQ,OAAA,CAAA8D,aAAA,CAACnE,YAAA,CAAAoE,IAAI;MACHE,KAAK,EAAE;QACLvB,KAAK,EAAEe;MACT;IAAE,GAED/C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAG;MACjBP,OAAO;MACPK,gBAAgB;MAChBC,iBAAiB;MACjB6C,UAAU;MACVrC,gBAAgB;MAChBC,mBAAmB;MACnBS,WAAW;MACXhB,KAAK;MACLmB,eAAe;MACfC,SAAS;MACTI,qBAAqB,EAAEA,qBAAqB;MAC5CC;IACF,CAAC,CAAC,eACF5C,MAAA,CAAAQ,OAAA,CAAA8D,aAAA,CAAClE,aAAA,CAAAI,OAAY;MACXwE,OAAO,EAAC,WAAW;MACnBC,QAAQ,EAAE5D,oBAAqB;MAC/B6D,YAAY,EAAE5D,iBAAkB;MAChCmD,KAAK,EAAE,CACLP,YAAY,EACZ;QACEiB,KAAK,EAAE3D;MACT,CAAC,CACD;MACF4D,aAAa,EAAE,CAAE;MACjBzC,qBAAqB,EAAEA,qBAAsB;MAC7CC,MAAM,EAAE,GAAGA,MAAM;IAAgB,GAEhCzB,KACW,CAAC,eACfnB,MAAA,CAAAQ,OAAA,CAAA8D,aAAA,CAAClE,aAAA,CAAAI,OAAY;MACXwE,OAAO,EAAElE,OAAO,GAAG,WAAW,GAAG,WAAY;MAC7C2D,KAAK,EAAE,CACLP,YAAY,EACZ;QACEiB,KAAK,EAAEd,SAAS;QAChBtD,OAAO,EAAEiB;MACX,CAAC,CACD;MACFoD,aAAa,EAAE,CAAE;MACjBzC,qBAAqB,EAAEA,qBAAsB;MAC7CC,MAAM,EAAE,GAAGA,MAAM;IAAkB,GAElCzB,KACW,CACV,CACO,CACX;EAAC;AAEX,CAAC;AAED,MAAMyD,MAAM,GAAGF,uBAAU,CAACW,MAAM,CAAC;EAC/BR,QAAQ,EAAE;IACRA,QAAQ,EAAE;EACZ,CAAC;EACDC,cAAc,EAAE;IACdQ,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAhF,OAAA,gBAEYiF,cAAK,CAACC,IAAI,CAACjF,UAAU,CAAC", "ignoreList": []}