{"version": 3, "names": ["React", "Platform", "StyleSheet", "color", "Pressable", "getTouchableRippleColors", "SettingsContext", "useInternalTheme", "forwardRef", "has<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "TouchableRipple", "style", "background", "_background", "borderless", "disabled", "disabledProp", "rippleColor", "underlayColor", "_underlayColor", "children", "theme", "themeOverrides", "rest", "ref", "calculatedRippleColor", "hoverColor", "fade", "rgb", "string", "rippleEffectEnabled", "useContext", "onPress", "onLongPress", "onPressIn", "onPressOut", "handlePressIn", "useCallback", "e", "centered", "button", "currentTarget", "window", "getComputedStyle", "dimensions", "getBoundingClientRect", "touchX", "touchY", "changedTouches", "touches", "nativeEvent", "touch", "width", "height", "locationX", "pageX", "locationY", "pageY", "size", "Math", "min", "max", "container", "document", "createElement", "setAttribute", "Object", "assign", "position", "pointerEvents", "top", "left", "right", "bottom", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomRightRadius", "borderBottomLeftRadius", "overflow", "ripple", "backgroundColor", "borderRadius", "transitionProperty", "transitionDuration", "transitionTimingFunction", "transform<PERSON><PERSON>in", "transform", "opacity", "append<PERSON><PERSON><PERSON>", "requestAnimationFrame", "handlePressOut", "containers", "querySelectorAll", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_extends", "state", "styles", "touchable", "hovered", "Children", "only", "supported", "create", "OS", "cursor", "transition", "Component"], "sourceRoot": "../../../../src", "sources": ["components/TouchableRipple/TouchableRipple.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAGEC,QAAQ,EAERC,UAAU,QAGL,cAAc;AAErB,OAAOC,KAAK,MAAM,OAAO;AAGzB,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,wBAAwB,QAAQ,SAAS;AAClD,SAAmBC,eAAe,QAAQ,qBAAqB;AAC/D,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAOC,eAAe,MAAM,6BAA6B;AA4DzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CACtB;EACEC,KAAK;EACLC,UAAU,EAAEC,WAAW;EACvBC,UAAU,GAAG,KAAK;EAClBC,QAAQ,EAAEC,YAAY;EACtBC,WAAW;EACXC,aAAa,EAAEC,cAAc;EAC7BC,QAAQ;EACRC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,EACRC,GAA6B,KAC1B;EACH,MAAMH,KAAK,GAAGd,gBAAgB,CAACe,cAAc,CAAC;EAC9C,MAAM;IAAEG;EAAsB,CAAC,GAAGpB,wBAAwB,CAAC;IACzDgB,KAAK;IACLJ;EACF,CAAC,CAAC;EACF,MAAMS,UAAU,GAAGvB,KAAK,CAACsB,qBAAqB,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACxE,MAAM;IAAEC;EAAoB,CAAC,GAAG9B,KAAK,CAAC+B,UAAU,CAAWzB,eAAe,CAAC;EAE3E,MAAM;IAAE0B,OAAO;IAAEC,WAAW;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGZ,IAAI;EAE5D,MAAMa,aAAa,GAAGpC,KAAK,CAACqC,WAAW,CACpCC,CAAM,IAAK;IACVJ,SAAS,aAATA,SAAS,eAATA,SAAS,CAAGI,CAAC,CAAC;IAEd,IAAIR,mBAAmB,EAAE;MACvB,MAAM;QAAES;MAAS,CAAC,GAAGhB,IAAI;MAEzB,MAAMiB,MAAM,GAAGF,CAAC,CAACG,aAAa;MAC9B,MAAM9B,KAAK,GAAG+B,MAAM,CAACC,gBAAgB,CAACH,MAAM,CAAC;MAC7C,MAAMI,UAAU,GAAGJ,MAAM,CAACK,qBAAqB,CAAC,CAAC;MAEjD,IAAIC,MAAM;MACV,IAAIC,MAAM;MAEV,MAAM;QAAEC,cAAc;QAAEC;MAAQ,CAAC,GAAGX,CAAC,CAACY,WAAW;MACjD,MAAMC,KAAK,GAAG,CAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,CAAC,CAAC,MAAID,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAG,CAAC,CAAC;;MAEjD;MACA,IAAIT,QAAQ,IAAI,CAACY,KAAK,EAAE;QACtBL,MAAM,GAAGF,UAAU,CAACQ,KAAK,GAAG,CAAC;QAC7BL,MAAM,GAAGH,UAAU,CAACS,MAAM,GAAG,CAAC;MAChC,CAAC,MAAM;QACLP,MAAM,GAAGK,KAAK,CAACG,SAAS,IAAIhB,CAAC,CAACiB,KAAK;QACnCR,MAAM,GAAGI,KAAK,CAACK,SAAS,IAAIlB,CAAC,CAACmB,KAAK;MACrC;;MAEA;MACA,MAAMC,IAAI,GAAGnB,QAAQ;MACjB;MACAoB,IAAI,CAACC,GAAG,CAAChB,UAAU,CAACQ,KAAK,EAAER,UAAU,CAACS,MAAM,CAAC,GAAG,GAAG;MACnD;MACAM,IAAI,CAACE,GAAG,CAACjB,UAAU,CAACQ,KAAK,EAAER,UAAU,CAACS,MAAM,CAAC,GAAG,CAAC;;MAErD;MACA,MAAMS,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAEhDF,SAAS,CAACG,YAAY,CAAC,mBAAmB,EAAE,EAAE,CAAC;MAE/CC,MAAM,CAACC,MAAM,CAACL,SAAS,CAACnD,KAAK,EAAE;QAC7ByD,QAAQ,EAAE,UAAU;QACpBC,aAAa,EAAE,MAAM;QACrBC,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,mBAAmB,EAAE/D,KAAK,CAAC+D,mBAAmB;QAC9CC,oBAAoB,EAAEhE,KAAK,CAACgE,oBAAoB;QAChDC,uBAAuB,EAAEjE,KAAK,CAACiE,uBAAuB;QACtDC,sBAAsB,EAAElE,KAAK,CAACkE,sBAAsB;QACpDC,QAAQ,EAAEvC,QAAQ,GAAG,SAAS,GAAG;MACnC,CAAC,CAAC;;MAEF;MACA,MAAMwC,MAAM,GAAGhB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAE7CE,MAAM,CAACC,MAAM,CAACY,MAAM,CAACpE,KAAK,EAAE;QAC1ByD,QAAQ,EAAE,UAAU;QACpBC,aAAa,EAAE,MAAM;QACrBW,eAAe,EAAEvD,qBAAqB;QACtCwD,YAAY,EAAE,KAAK;QAEnB;QACAC,kBAAkB,EAAE,mBAAmB;QACvCC,kBAAkB,EAAE,GAAGxB,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,IAAI;QACpD0B,wBAAwB,EAAE,QAAQ;QAClCC,eAAe,EAAE,QAAQ;QAEzB;QACAC,SAAS,EAAE,mDAAmD;QAC9DC,OAAO,EAAE,KAAK;QAEd;QACAhB,IAAI,EAAE,GAAGzB,MAAM,IAAI;QACnBwB,GAAG,EAAE,GAAGvB,MAAM,IAAI;QAClBK,KAAK,EAAE,GAAGM,IAAI,IAAI;QAClBL,MAAM,EAAE,GAAGK,IAAI;MACjB,CAAC,CAAC;;MAEF;MACAI,SAAS,CAAC0B,WAAW,CAACT,MAAM,CAAC;MAC7BvC,MAAM,CAACgD,WAAW,CAAC1B,SAAS,CAAC;;MAE7B;MACA;MACA;MACA2B,qBAAqB,CAAC,MAAM;QAC1BA,qBAAqB,CAAC,MAAM;UAC1BvB,MAAM,CAACC,MAAM,CAACY,MAAM,CAACpE,KAAK,EAAE;YAC1B2E,SAAS,EAAE,6CAA6C;YACxDC,OAAO,EAAE;UACX,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACrD,SAAS,EAAEX,IAAI,EAAEO,mBAAmB,EAAEL,qBAAqB,CAC9D,CAAC;EAED,MAAMiE,cAAc,GAAG1F,KAAK,CAACqC,WAAW,CACrCC,CAAM,IAAK;IACVH,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAGG,CAAC,CAAC;IAEf,IAAIR,mBAAmB,EAAE;MACvB,MAAM6D,UAAU,GAAGrD,CAAC,CAACG,aAAa,CAACmD,gBAAgB,CACjD,qBACF,CAAkB;MAElBH,qBAAqB,CAAC,MAAM;QAC1BA,qBAAqB,CAAC,MAAM;UAC1BE,UAAU,CAACE,OAAO,CAAE/B,SAAS,IAAK;YAChC,MAAMiB,MAAM,GAAGjB,SAAS,CAACgC,UAA6B;YAEtD5B,MAAM,CAACC,MAAM,CAACY,MAAM,CAACpE,KAAK,EAAE;cAC1BwE,kBAAkB,EAAE,OAAO;cAC3BI,OAAO,EAAE;YACX,CAAC,CAAC;;YAEF;YACAQ,UAAU,CAAC,MAAM;cACf,MAAM;gBAAEC;cAAW,CAAC,GAAGlC,SAAS;cAEhC,IAAIkC,UAAU,EAAE;gBACdA,UAAU,CAACC,WAAW,CAACnC,SAAS,CAAC;cACnC;YACF,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAAC3B,UAAU,EAAEL,mBAAmB,CAClC,CAAC;EAED,MAAMoE,qBAAqB,GAAGzF,eAAe,CAAC;IAC5CuB,OAAO;IACPC,WAAW;IACXC,SAAS;IACTC;EACF,CAAC,CAAC;EAEF,MAAMpB,QAAQ,GAAGC,YAAY,IAAI,CAACkF,qBAAqB;EAEvD,oBACElG,KAAA,CAAAgE,aAAA,CAAC5D,SAAS,EAAA+F,QAAA,KACJ5E,IAAI;IACRC,GAAG,EAAEA,GAAI;IACTU,SAAS,EAAEE,aAAc;IACzBD,UAAU,EAAEuD,cAAe;IAC3B3E,QAAQ,EAAEA,QAAS;IACnBJ,KAAK,EAAGyF,KAAK,IAAK,CAChBC,MAAM,CAACC,SAAS,EAChBxF,UAAU,IAAIuF,MAAM,CAACvF,UAAU;IAC/B;IACA;IACAsF,KAAK,CAACG,OAAO,IAAI;MAAEvB,eAAe,EAAEtD;IAAW,CAAC,EAChDX,QAAQ,IAAIsF,MAAM,CAACtF,QAAQ,EAC3B,OAAOJ,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACyF,KAAK,CAAC,GAAGzF,KAAK;EAClD,IAEAyF,KAAK,IACLpG,KAAK,CAACwG,QAAQ,CAACC,IAAI,CACjB,OAAOrF,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACgF,KAAK,CAAC,GAAGhF,QACrD,CAEO,CAAC;AAEhB,CAAC;;AAED;AACA;AACA;AACAV,eAAe,CAACgG,SAAS,GAAG,IAAI;AAEhC,MAAML,MAAM,GAAGnG,UAAU,CAACyG,MAAM,CAAC;EAC/BL,SAAS,EAAE;IACTlC,QAAQ,EAAE,UAAU;IACpB,IAAInE,QAAQ,CAAC2G,EAAE,KAAK,KAAK,IAAI;MAC3BC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EACD/F,QAAQ,EAAE;IACR,IAAId,QAAQ,CAAC2G,EAAE,KAAK,KAAK,IAAI;MAC3BC,MAAM,EAAE;IACV,CAAC;EACH,CAAC;EACD/F,UAAU,EAAE;IACVgE,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEF,MAAMiC,SAAS,GAAGvG,UAAU,CAACE,eAAe,CAAC;AAE7C,eAAeqG,SAAS", "ignoreList": []}