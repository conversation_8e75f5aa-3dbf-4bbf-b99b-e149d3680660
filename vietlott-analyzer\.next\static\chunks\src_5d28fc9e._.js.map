{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/LatestResults.tsx"], "sourcesContent": ["import { LotteryResult } from '@/types/lottery';\n\ninterface LatestResultsProps {\n  data: LotteryResult[];\n}\n\nexport default function LatestResults({ data }: LatestResultsProps) {\n  const latestResults = data.slice(0, 5); // Show last 5 results\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const NumberBall = ({ number, isPower = false }: { number: number; isPower?: boolean }) => (\n    <div\n      className={`\n        w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm\n        ${isPower \n          ? 'bg-red-500 border-2 border-red-600' \n          : 'bg-blue-500 border-2 border-blue-600'\n        }\n        shadow-lg\n      `}\n    >\n      {number}\n    </div>\n  );\n\n  if (!latestResults.length) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <h2 className=\"text-xl font-bold text-gray-800 mb-4\">Latest Results</h2>\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-400 text-4xl mb-2\">🎱</div>\n          <p className=\"text-gray-500\">No lottery results available</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h2 className=\"text-xl font-bold text-gray-800 mb-4 flex items-center\">\n        <span className=\"mr-2\">🎯</span>\n        Latest Results\n      </h2>\n      \n      <div className=\"space-y-4\">\n        {latestResults.map((result, index) => (\n          <div\n            key={result.id}\n            className={`\n              p-4 rounded-lg border-2 transition-all duration-200\n              ${index === 0 \n                ? 'border-blue-200 bg-blue-50' \n                : 'border-gray-200 bg-gray-50'\n              }\n            `}\n          >\n            <div className=\"flex justify-between items-start mb-3\">\n              <div>\n                <h3 className=\"font-semibold text-gray-800\">\n                  Draw #{result.id}\n                  {index === 0 && (\n                    <span className=\"ml-2 px-2 py-1 bg-blue-500 text-white text-xs rounded-full\">\n                      Latest\n                    </span>\n                  )}\n                </h3>\n                <p className=\"text-sm text-gray-600\">{formatDate(result.date)}</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-2 mb-2\">\n              <span className=\"text-sm font-medium text-gray-700\">Numbers:</span>\n              <div className=\"flex space-x-1\">\n                {result.result.map((number, numIndex) => (\n                  <NumberBall key={numIndex} number={number} />\n                ))}\n              </div>\n            </div>\n            \n            {result.powerNumber && (\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm font-medium text-gray-700\">Power:</span>\n                <NumberBall number={result.powerNumber} isPower={true} />\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"mt-4 pt-4 border-t border-gray-200\">\n        <p className=\"text-xs text-gray-500 text-center\">\n          Showing {latestResults.length} most recent draws\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAMe,SAAS,cAAc,EAAE,IAAI,EAAsB;IAChE,MAAM,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,sBAAsB;IAE9D,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,UAAU,KAAK,EAAyC,iBACpF,6LAAC;YACC,WAAW,CAAC;;QAEV,EAAE,UACE,uCACA,uCACH;;MAEH,CAAC;sBAEA;;;;;;IAIL,IAAI,CAAC,cAAc,MAAM,EAAE;QACzB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAuC;;;;;;8BACrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA8B;;;;;;sCAC7C,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC;wBAAK,WAAU;kCAAO;;;;;;oBAAS;;;;;;;0BAIlC,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,6LAAC;wBAEC,WAAW,CAAC;;cAEV,EAAE,UAAU,IACR,+BACA,6BACH;YACH,CAAC;;0CAED,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;gDAA8B;gDACnC,OAAO,EAAE;gDACf,UAAU,mBACT,6LAAC;oDAAK,WAAU;8DAA6D;;;;;;;;;;;;sDAKjF,6LAAC;4CAAE,WAAU;sDAAyB,WAAW,OAAO,IAAI;;;;;;;;;;;;;;;;;0CAIhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,6LAAC;wCAAI,WAAU;kDACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,yBAC1B,6LAAC;gDAA0B,QAAQ;+CAAlB;;;;;;;;;;;;;;;;4BAKtB,OAAO,WAAW,kBACjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,6LAAC;wCAAW,QAAQ,OAAO,WAAW;wCAAE,SAAS;;;;;;;;;;;;;uBAnChD,OAAO,EAAE;;;;;;;;;;0BA0CpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAoC;wBACtC,cAAc,MAAM;wBAAC;;;;;;;;;;;;;;;;;;AAKxC;KAlGwB", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/utils/dataAnalysis.ts"], "sourcesContent": ["import { LotteryResult, NumberFrequency, LotteryStatistics, SuggestionAlgorithm } from '@/types/lottery';\n\nexport function calculateNumberFrequency(data: LotteryResult[]): NumberFrequency[] {\n  const frequency: { [key: number]: number } = {};\n  const totalNumbers = data.length * 6; // 6 numbers per draw\n\n  // Count frequency of each number\n  data.forEach(result => {\n    result.result.forEach(number => {\n      frequency[number] = (frequency[number] || 0) + 1;\n    });\n  });\n\n  // Convert to array and calculate percentages\n  const frequencyArray: NumberFrequency[] = [];\n  for (let i = 1; i <= 55; i++) {\n    const count = frequency[i] || 0;\n    frequencyArray.push({\n      number: i,\n      count,\n      percentage: totalNumbers > 0 ? (count / totalNumbers) * 100 : 0,\n    });\n  }\n\n  return frequencyArray.sort((a, b) => b.count - a.count);\n}\n\nexport function calculateStatistics(data: LotteryResult[]): LotteryStatistics {\n  const allFrequency = calculateNumberFrequency(data);\n  \n  // Get data for different time periods\n  const now = new Date();\n  const last30Days = data.filter(result => {\n    const resultDate = new Date(result.date);\n    const diffTime = now.getTime() - resultDate.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays <= 30;\n  });\n  \n  const last60Days = data.filter(result => {\n    const resultDate = new Date(result.date);\n    const diffTime = now.getTime() - resultDate.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays <= 60;\n  });\n  \n  const last90Days = data.filter(result => {\n    const resultDate = new Date(result.date);\n    const diffTime = now.getTime() - resultDate.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays <= 90;\n  });\n\n  return {\n    totalDraws: data.length,\n    mostFrequent: allFrequency.slice(0, 10),\n    leastFrequent: allFrequency.slice(-10).reverse(),\n    numberDistribution: allFrequency,\n    recentTrends: {\n      last30Days: calculateNumberFrequency(last30Days),\n      last60Days: calculateNumberFrequency(last60Days),\n      last90Days: calculateNumberFrequency(last90Days),\n    },\n  };\n}\n\n// Suggestion algorithms\nexport const suggestionAlgorithms: SuggestionAlgorithm[] = [\n  {\n    name: 'Hot Numbers',\n    description: 'Based on most frequently drawn numbers',\n    generate: (data: LotteryResult[]) => {\n      const frequency = calculateNumberFrequency(data);\n      return frequency.slice(0, 6).map(f => f.number);\n    },\n  },\n  {\n    name: 'Cold Numbers',\n    description: 'Based on least frequently drawn numbers',\n    generate: (data: LotteryResult[]) => {\n      const frequency = calculateNumberFrequency(data);\n      return frequency.slice(-6).map(f => f.number).reverse();\n    },\n  },\n  {\n    name: 'Balanced Mix',\n    description: 'Combination of hot and cold numbers',\n    generate: (data: LotteryResult[]) => {\n      const frequency = calculateNumberFrequency(data);\n      const hot = frequency.slice(0, 3).map(f => f.number);\n      const cold = frequency.slice(-3).map(f => f.number).reverse();\n      return [...hot, ...cold].sort((a, b) => a - b);\n    },\n  },\n  {\n    name: 'Recent Trends',\n    description: 'Based on numbers trending in last 30 days',\n    generate: (data: LotteryResult[]) => {\n      const stats = calculateStatistics(data);\n      return stats.recentTrends.last30Days.slice(0, 6).map(f => f.number);\n    },\n  },\n  {\n    name: 'Random Selection',\n    description: 'Completely random numbers',\n    generate: () => {\n      const numbers: number[] = [];\n      const used = new Set<number>();\n      \n      while (numbers.length < 6) {\n        const num = Math.floor(Math.random() * 55) + 1;\n        if (!used.has(num)) {\n          numbers.push(num);\n          used.add(num);\n        }\n      }\n      \n      return numbers.sort((a, b) => a - b);\n    },\n  },\n  {\n    name: 'Mathematical Pattern',\n    description: 'Based on mathematical distribution patterns',\n    generate: (data: LotteryResult[]) => {\n      const frequency = calculateNumberFrequency(data);\n      const avgFrequency = frequency.reduce((sum, f) => sum + f.count, 0) / frequency.length;\n      \n      // Select numbers close to average frequency\n      const balanced = frequency.filter(f => \n        Math.abs(f.count - avgFrequency) <= avgFrequency * 0.2\n      );\n      \n      if (balanced.length >= 6) {\n        return balanced.slice(0, 6).map(f => f.number);\n      }\n      \n      // Fallback to top numbers if not enough balanced numbers\n      return frequency.slice(0, 6).map(f => f.number);\n    },\n  },\n];\n\nexport function generateSuggestions(data: LotteryResult[], algorithmName?: string) {\n  const algorithm = algorithmName \n    ? suggestionAlgorithms.find(a => a.name === algorithmName)\n    : suggestionAlgorithms[0];\n  \n  if (!algorithm) {\n    throw new Error('Algorithm not found');\n  }\n  \n  const numbers = algorithm.generate(data);\n  const stats = calculateStatistics(data);\n  \n  // Calculate confidence based on frequency of suggested numbers\n  const avgFrequency = stats.numberDistribution.reduce((sum, f) => sum + f.count, 0) / stats.numberDistribution.length;\n  const suggestedFrequencies = numbers.map(num => \n    stats.numberDistribution.find(f => f.number === num)?.count || 0\n  );\n  const avgSuggestedFreq = suggestedFrequencies.reduce((sum, f) => sum + f, 0) / suggestedFrequencies.length;\n  const confidence = Math.min(100, Math.max(0, (avgSuggestedFreq / avgFrequency) * 50));\n  \n  return {\n    numbers,\n    algorithm: algorithm.name,\n    confidence: Math.round(confidence),\n    reasoning: algorithm.description,\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAEO,SAAS,yBAAyB,IAAqB;IAC5D,MAAM,YAAuC,CAAC;IAC9C,MAAM,eAAe,KAAK,MAAM,GAAG,GAAG,qBAAqB;IAE3D,iCAAiC;IACjC,KAAK,OAAO,CAAC,CAAA;QACX,OAAO,MAAM,CAAC,OAAO,CAAC,CAAA;YACpB,SAAS,CAAC,OAAO,GAAG,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI;QACjD;IACF;IAEA,6CAA6C;IAC7C,MAAM,iBAAoC,EAAE;IAC5C,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;QAC5B,MAAM,QAAQ,SAAS,CAAC,EAAE,IAAI;QAC9B,eAAe,IAAI,CAAC;YAClB,QAAQ;YACR;YACA,YAAY,eAAe,IAAI,AAAC,QAAQ,eAAgB,MAAM;QAChE;IACF;IAEA,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACxD;AAEO,SAAS,oBAAoB,IAAqB;IACvD,MAAM,eAAe,yBAAyB;IAE9C,sCAAsC;IACtC,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,KAAK,MAAM,CAAC,CAAA;QAC7B,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;QACvC,MAAM,WAAW,IAAI,OAAO,KAAK,WAAW,OAAO;QACnD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO,YAAY;IACrB;IAEA,MAAM,aAAa,KAAK,MAAM,CAAC,CAAA;QAC7B,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;QACvC,MAAM,WAAW,IAAI,OAAO,KAAK,WAAW,OAAO;QACnD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO,YAAY;IACrB;IAEA,MAAM,aAAa,KAAK,MAAM,CAAC,CAAA;QAC7B,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;QACvC,MAAM,WAAW,IAAI,OAAO,KAAK,WAAW,OAAO;QACnD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO,YAAY;IACrB;IAEA,OAAO;QACL,YAAY,KAAK,MAAM;QACvB,cAAc,aAAa,KAAK,CAAC,GAAG;QACpC,eAAe,aAAa,KAAK,CAAC,CAAC,IAAI,OAAO;QAC9C,oBAAoB;QACpB,cAAc;YACZ,YAAY,yBAAyB;YACrC,YAAY,yBAAyB;YACrC,YAAY,yBAAyB;QACvC;IACF;AACF;AAGO,MAAM,uBAA8C;IACzD;QACE,MAAM;QACN,aAAa;QACb,UAAU,CAAC;YACT,MAAM,YAAY,yBAAyB;YAC3C,OAAO,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QAChD;IACF;IACA;QACE,MAAM;QACN,aAAa;QACb,UAAU,CAAC;YACT,MAAM,YAAY,yBAAyB;YAC3C,OAAO,UAAU,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,EAAE,OAAO;QACvD;IACF;IACA;QACE,MAAM;QACN,aAAa;QACb,UAAU,CAAC;YACT,MAAM,YAAY,yBAAyB;YAC3C,MAAM,MAAM,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YACnD,MAAM,OAAO,UAAU,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,EAAE,OAAO;YAC3D,OAAO;mBAAI;mBAAQ;aAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAC9C;IACF;IACA;QACE,MAAM;QACN,aAAa;QACb,UAAU,CAAC;YACT,MAAM,QAAQ,oBAAoB;YAClC,OAAO,MAAM,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QACpE;IACF;IACA;QACE,MAAM;QACN,aAAa;QACb,UAAU;YACR,MAAM,UAAoB,EAAE;YAC5B,MAAM,OAAO,IAAI;YAEjB,MAAO,QAAQ,MAAM,GAAG,EAAG;gBACzB,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAC7C,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM;oBAClB,QAAQ,IAAI,CAAC;oBACb,KAAK,GAAG,CAAC;gBACX;YACF;YAEA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QACpC;IACF;IACA;QACE,MAAM;QACN,aAAa;QACb,UAAU,CAAC;YACT,MAAM,YAAY,yBAAyB;YAC3C,MAAM,eAAe,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,KAAK,UAAU,MAAM;YAEtF,4CAA4C;YAC5C,MAAM,WAAW,UAAU,MAAM,CAAC,CAAA,IAChC,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,eAAe;YAGrD,IAAI,SAAS,MAAM,IAAI,GAAG;gBACxB,OAAO,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAC/C;YAEA,yDAAyD;YACzD,OAAO,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QAChD;IACF;CACD;AAEM,SAAS,oBAAoB,IAAqB,EAAE,aAAsB;IAC/E,MAAM,YAAY,gBACd,qBAAqB,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,iBAC1C,oBAAoB,CAAC,EAAE;IAE3B,IAAI,CAAC,WAAW;QACd,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,UAAU,QAAQ,CAAC;IACnC,MAAM,QAAQ,oBAAoB;IAElC,+DAA+D;IAC/D,MAAM,eAAe,MAAM,kBAAkB,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,KAAK,MAAM,kBAAkB,CAAC,MAAM;IACpH,MAAM,uBAAuB,QAAQ,GAAG,CAAC,CAAA,MACvC,MAAM,kBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,MAAM,SAAS;IAEjE,MAAM,mBAAmB,qBAAqB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG,KAAK,qBAAqB,MAAM;IAC1G,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,AAAC,mBAAmB,eAAgB;IAEjF,OAAO;QACL;QACA,WAAW,UAAU,IAAI;QACzB,YAAY,KAAK,KAAK,CAAC;QACvB,WAAW,UAAU,WAAW;IAClC;AACF", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/StatisticsChart.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  LineElement,\n  PointElement,\n} from 'chart.js';\nimport { Bar, Line } from 'react-chartjs-2';\nimport { LotteryResult } from '@/types/lottery';\nimport { calculateStatistics } from '@/utils/dataAnalysis';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface StatisticsChartProps {\n  data: LotteryResult[];\n}\n\nexport default function StatisticsChart({ data }: StatisticsChartProps) {\n  const [chartType, setChartType] = useState<'frequency' | 'trends'>('frequency');\n  const [statistics, setStatistics] = useState(() => \n    data.length > 0 ? calculateStatistics(data) : null\n  );\n\n  useEffect(() => {\n    if (data.length > 0) {\n      setStatistics(calculateStatistics(data));\n    }\n  }, [data]);\n\n  if (!statistics || !data.length) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <h2 className=\"text-xl font-bold text-gray-800 mb-4\">Statistics & Analysis</h2>\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-400 text-4xl mb-2\">📊</div>\n          <p className=\"text-gray-500\">No data available for analysis</p>\n        </div>\n      </div>\n    );\n  }\n\n  const frequencyChartData = {\n    labels: statistics.numberDistribution.map(item => item.number.toString()),\n    datasets: [\n      {\n        label: 'Frequency',\n        data: statistics.numberDistribution.map(item => item.count),\n        backgroundColor: statistics.numberDistribution.map((item, index) => {\n          if (index < 10) return 'rgba(239, 68, 68, 0.8)'; // Hot numbers - red\n          if (index >= statistics.numberDistribution.length - 10) return 'rgba(59, 130, 246, 0.8)'; // Cold numbers - blue\n          return 'rgba(156, 163, 175, 0.8)'; // Normal numbers - gray\n        }),\n        borderColor: statistics.numberDistribution.map((item, index) => {\n          if (index < 10) return 'rgba(239, 68, 68, 1)';\n          if (index >= statistics.numberDistribution.length - 10) return 'rgba(59, 130, 246, 1)';\n          return 'rgba(156, 163, 175, 1)';\n        }),\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const trendsChartData = {\n    labels: statistics.numberDistribution.slice(0, 20).map(item => item.number.toString()),\n    datasets: [\n      {\n        label: 'All Time',\n        data: statistics.numberDistribution.slice(0, 20).map(item => item.count),\n        borderColor: 'rgba(75, 192, 192, 1)',\n        backgroundColor: 'rgba(75, 192, 192, 0.2)',\n        tension: 0.1,\n      },\n      {\n        label: 'Last 30 Days',\n        data: statistics.recentTrends.last30Days.slice(0, 20).map(item => item.count),\n        borderColor: 'rgba(255, 99, 132, 1)',\n        backgroundColor: 'rgba(255, 99, 132, 0.2)',\n        tension: 0.1,\n      },\n      {\n        label: 'Last 90 Days',\n        data: statistics.recentTrends.last90Days.slice(0, 20).map(item => item.count),\n        borderColor: 'rgba(54, 162, 235, 1)',\n        backgroundColor: 'rgba(54, 162, 235, 0.2)',\n        tension: 0.1,\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n      title: {\n        display: true,\n        text: chartType === 'frequency' ? 'Number Frequency Distribution' : 'Frequency Trends Comparison',\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            const percentage = statistics.numberDistribution.find(\n              item => item.number.toString() === context.label\n            )?.percentage.toFixed(2);\n            return `${context.dataset.label}: ${context.parsed.y} (${percentage}%)`;\n          },\n        },\n      },\n    },\n    scales: {\n      x: {\n        title: {\n          display: true,\n          text: 'Numbers',\n        },\n      },\n      y: {\n        title: {\n          display: true,\n          text: 'Frequency',\n        },\n        beginAtZero: true,\n      },\n    },\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h2 className=\"text-xl font-bold text-gray-800 flex items-center\">\n          <span className=\"mr-2\">📊</span>\n          Statistics & Analysis\n        </h2>\n        \n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => setChartType('frequency')}\n            className={`px-3 py-1 rounded text-sm transition-colors ${\n              chartType === 'frequency'\n                ? 'bg-blue-500 text-white'\n                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n          >\n            Frequency\n          </button>\n          <button\n            onClick={() => setChartType('trends')}\n            className={`px-3 py-1 rounded text-sm transition-colors ${\n              chartType === 'trends'\n                ? 'bg-blue-500 text-white'\n                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n          >\n            Trends\n          </button>\n        </div>\n      </div>\n\n      {/* Summary Stats */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n        <div className=\"bg-blue-50 rounded-lg p-3 text-center\">\n          <div className=\"text-2xl font-bold text-blue-600\">{statistics.totalDraws}</div>\n          <div className=\"text-sm text-blue-800\">Total Draws</div>\n        </div>\n        <div className=\"bg-red-50 rounded-lg p-3 text-center\">\n          <div className=\"text-2xl font-bold text-red-600\">{statistics.mostFrequent[0]?.number}</div>\n          <div className=\"text-sm text-red-800\">Hottest Number</div>\n        </div>\n        <div className=\"bg-blue-50 rounded-lg p-3 text-center\">\n          <div className=\"text-2xl font-bold text-blue-600\">{statistics.leastFrequent[0]?.number}</div>\n          <div className=\"text-sm text-blue-800\">Coldest Number</div>\n        </div>\n        <div className=\"bg-green-50 rounded-lg p-3 text-center\">\n          <div className=\"text-2xl font-bold text-green-600\">\n            {Math.round(statistics.numberDistribution.reduce((sum, item) => sum + item.count, 0) / 55)}\n          </div>\n          <div className=\"text-sm text-green-800\">Avg Frequency</div>\n        </div>\n      </div>\n\n      {/* Chart */}\n      <div className=\"h-96\">\n        {chartType === 'frequency' ? (\n          <Bar data={frequencyChartData} options={chartOptions} />\n        ) : (\n          <Line data={trendsChartData} options={chartOptions} />\n        )}\n      </div>\n\n      {/* Legend */}\n      <div className=\"mt-4 flex justify-center space-x-6 text-sm\">\n        <div className=\"flex items-center\">\n          <div className=\"w-4 h-4 bg-red-500 rounded mr-2\"></div>\n          <span>Hot Numbers (Top 10)</span>\n        </div>\n        <div className=\"flex items-center\">\n          <div className=\"w-4 h-4 bg-blue-500 rounded mr-2\"></div>\n          <span>Cold Numbers (Bottom 10)</span>\n        </div>\n        <div className=\"flex items-center\">\n          <div className=\"w-4 h-4 bg-gray-400 rounded mr-2\"></div>\n          <span>Normal Numbers</span>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAWA;AAEA;;;AAhBA;;;;;AAkBA,+JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,+JAAA,CAAA,gBAAa,EACb,+JAAA,CAAA,cAAW,EACX,+JAAA,CAAA,aAAU,EACV,+JAAA,CAAA,cAAW,EACX,+JAAA,CAAA,eAAY,EACZ,+JAAA,CAAA,QAAK,EACL,+JAAA,CAAA,UAAO,EACP,+JAAA,CAAA,SAAM;AAOO,SAAS,gBAAgB,EAAE,IAAI,EAAwB;;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;oCAAE,IAC3C,KAAK,MAAM,GAAG,IAAI,CAAA,GAAA,+HAA<PERSON>,CAAA,sBAAmB,AAAD,EAAE,QAAQ;;IAGhD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,KAAK,MAAM,GAAG,GAAG;gBACnB,cAAc,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE;YACpC;QACF;oCAAG;QAAC;KAAK;IAET,IAAI,CAAC,cAAc,CAAC,KAAK,MAAM,EAAE;QAC/B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAuC;;;;;;8BACrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA8B;;;;;;sCAC7C,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,qBAAqB;QACzB,QAAQ,WAAW,kBAAkB,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,QAAQ;QACtE,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,WAAW,kBAAkB,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;gBAC1D,iBAAiB,WAAW,kBAAkB,CAAC,GAAG,CAAC,CAAC,MAAM;oBACxD,IAAI,QAAQ,IAAI,OAAO,0BAA0B,oBAAoB;oBACrE,IAAI,SAAS,WAAW,kBAAkB,CAAC,MAAM,GAAG,IAAI,OAAO,2BAA2B,sBAAsB;oBAChH,OAAO,4BAA4B,wBAAwB;gBAC7D;gBACA,aAAa,WAAW,kBAAkB,CAAC,GAAG,CAAC,CAAC,MAAM;oBACpD,IAAI,QAAQ,IAAI,OAAO;oBACvB,IAAI,SAAS,WAAW,kBAAkB,CAAC,MAAM,GAAG,IAAI,OAAO;oBAC/D,OAAO;gBACT;gBACA,aAAa;YACf;SACD;IACH;IAEA,MAAM,kBAAkB;QACtB,QAAQ,WAAW,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,QAAQ;QACnF,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,WAAW,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;gBACvE,aAAa;gBACb,iBAAiB;gBACjB,SAAS;YACX;YACA;gBACE,OAAO;gBACP,MAAM,WAAW,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;gBAC5E,aAAa;gBACb,iBAAiB;gBACjB,SAAS;YACX;YACA;gBACE,OAAO;gBACP,MAAM,WAAW,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;gBAC5E,aAAa;gBACb,iBAAiB;gBACjB,SAAS;YACX;SACD;IACH;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM,cAAc,cAAc,kCAAkC;YACtE;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,MAAM,aAAa,WAAW,kBAAkB,CAAC,IAAI,CACnD,CAAA,OAAQ,KAAK,MAAM,CAAC,QAAQ,OAAO,QAAQ,KAAK,GAC/C,WAAW,QAAQ;wBACtB,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC;oBACzE;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,OAAO;oBACL,SAAS;oBACT,MAAM;gBACR;YACF;YACA,GAAG;gBACD,OAAO;oBACL,SAAS;oBACT,MAAM;gBACR;gBACA,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAK,WAAU;0CAAO;;;;;;4BAAS;;;;;;;kCAIlC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,4CAA4C,EACtD,cAAc,cACV,2BACA,+CACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,4CAA4C,EACtD,cAAc,WACV,2BACA,+CACJ;0CACH;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAoC,WAAW,UAAU;;;;;;0CACxE,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAmC,WAAW,YAAY,CAAC,EAAE,EAAE;;;;;;0CAC9E,6LAAC;gCAAI,WAAU;0CAAuB;;;;;;;;;;;;kCAExC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAoC,WAAW,aAAa,CAAC,EAAE,EAAE;;;;;;0CAChF,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,CAAC,WAAW,kBAAkB,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE,KAAK;;;;;;0CAEzF,6LAAC;gCAAI,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;0BAK5C,6LAAC;gBAAI,WAAU;0BACZ,cAAc,4BACb,6LAAC,yJAAA,CAAA,MAAG;oBAAC,MAAM;oBAAoB,SAAS;;;;;yCAExC,6LAAC,yJAAA,CAAA,OAAI;oBAAC,MAAM;oBAAiB,SAAS;;;;;;;;;;;0BAK1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GA9LwB;KAAA", "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/NumberSuggestion.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { LotteryResult } from '@/types/lottery';\nimport { generateSuggestions, suggestionAlgorithms } from '@/utils/dataAnalysis';\n\ninterface NumberSuggestionProps {\n  data: LotteryResult[];\n}\n\nexport default function NumberSuggestion({ data }: NumberSuggestionProps) {\n  const [selectedAlgorithm, setSelectedAlgorithm] = useState(suggestionAlgorithms[0].name);\n  const [suggestion, setSuggestion] = useState(() => \n    data.length > 0 ? generateSuggestions(data, selectedAlgorithm) : null\n  );\n\n  const handleAlgorithmChange = (algorithmName: string) => {\n    setSelectedAlgorithm(algorithmName);\n    if (data.length > 0) {\n      setSuggestion(generateSuggestions(data, algorithmName));\n    }\n  };\n\n  const handleRefresh = () => {\n    if (data.length > 0) {\n      setSuggestion(generateSuggestions(data, selectedAlgorithm));\n    }\n  };\n\n  const NumberBall = ({ number }: { number: number }) => (\n    <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center text-white font-bold text-lg shadow-lg border-2 border-green-500\">\n      {number}\n    </div>\n  );\n\n  const ConfidenceBar = ({ confidence }: { confidence: number }) => (\n    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n      <div\n        className={`h-2 rounded-full transition-all duration-500 ${\n          confidence >= 70 ? 'bg-green-500' :\n          confidence >= 40 ? 'bg-yellow-500' : 'bg-red-500'\n        }`}\n        style={{ width: `${confidence}%` }}\n      />\n    </div>\n  );\n\n  if (!data.length) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <h2 className=\"text-xl font-bold text-gray-800 mb-4\">Number Suggestions</h2>\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-400 text-4xl mb-2\">🎲</div>\n          <p className=\"text-gray-500\">No data available for suggestions</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h2 className=\"text-xl font-bold text-gray-800 mb-4 flex items-center\">\n        <span className=\"mr-2\">🎲</span>\n        Number Suggestions\n      </h2>\n\n      {/* Algorithm Selection */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Algorithm:\n        </label>\n        <select\n          value={selectedAlgorithm}\n          onChange={(e) => handleAlgorithmChange(e.target.value)}\n          className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          {suggestionAlgorithms.map((algorithm) => (\n            <option key={algorithm.name} value={algorithm.name}>\n              {algorithm.name}\n            </option>\n          ))}\n        </select>\n        <p className=\"text-xs text-gray-500 mt-1\">\n          {suggestionAlgorithms.find(a => a.name === selectedAlgorithm)?.description}\n        </p>\n      </div>\n\n      {suggestion && (\n        <>\n          {/* Suggested Numbers */}\n          <div className=\"mb-6\">\n            <div className=\"flex justify-between items-center mb-3\">\n              <h3 className=\"font-semibold text-gray-800\">Suggested Numbers:</h3>\n              <button\n                onClick={handleRefresh}\n                className=\"px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors flex items-center\"\n              >\n                <span className=\"mr-1\">🔄</span>\n                Refresh\n              </button>\n            </div>\n            \n            <div className=\"flex justify-center space-x-2 mb-4\">\n              {suggestion.numbers.map((number, index) => (\n                <NumberBall key={index} number={number} />\n              ))}\n            </div>\n          </div>\n\n          {/* Confidence and Details */}\n          <div className=\"space-y-4\">\n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">Confidence:</span>\n                <span className=\"text-sm font-bold text-gray-800\">{suggestion.confidence}%</span>\n              </div>\n              <ConfidenceBar confidence={suggestion.confidence} />\n            </div>\n\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h4 className=\"font-medium text-gray-800 mb-2\">Algorithm Details:</h4>\n              <p className=\"text-sm text-gray-600 mb-2\">{suggestion.reasoning}</p>\n              <div className=\"text-xs text-gray-500\">\n                <p>• Based on analysis of {data.length} historical draws</p>\n                <p>• Numbers are sorted in ascending order</p>\n                <p>• Confidence calculated using frequency analysis</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Copy */}\n          <div className=\"mt-4 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Quick copy:</span>\n              <button\n                onClick={() => {\n                  navigator.clipboard.writeText(suggestion.numbers.join(', '));\n                }}\n                className=\"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded hover:bg-gray-200 transition-colors\"\n              >\n                {suggestion.numbers.join(', ')}\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAUe,SAAS,iBAAiB,EAAE,IAAI,EAAyB;;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,+HAAA,CAAA,uBAAoB,CAAC,EAAE,CAAC,IAAI;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;qCAAE,IAC3C,KAAK,MAAM,GAAG,IAAI,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,qBAAqB;;IAGnE,MAAM,wBAAwB,CAAC;QAC7B,qBAAqB;QACrB,IAAI,KAAK,MAAM,GAAG,GAAG;YACnB,cAAc,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;QAC1C;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,KAAK,MAAM,GAAG,GAAG;YACnB,cAAc,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;QAC1C;IACF;IAEA,MAAM,aAAa,CAAC,EAAE,MAAM,EAAsB,iBAChD,6LAAC;YAAI,WAAU;sBACZ;;;;;;IAIL,MAAM,gBAAgB,CAAC,EAAE,UAAU,EAA0B,iBAC3D,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBACC,WAAW,CAAC,6CAA6C,EACvD,cAAc,KAAK,iBACnB,cAAc,KAAK,kBAAkB,cACrC;gBACF,OAAO;oBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;gBAAC;;;;;;;;;;;IAKvC,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAuC;;;;;;8BACrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA8B;;;;;;sCAC7C,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC;wBAAK,WAAU;kCAAO;;;;;;oBAAS;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;wBACrD,WAAU;kCAET,+HAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,CAAC,0BACzB,6LAAC;gCAA4B,OAAO,UAAU,IAAI;0CAC/C,UAAU,IAAI;+BADJ,UAAU,IAAI;;;;;;;;;;kCAK/B,6LAAC;wBAAE,WAAU;kCACV,+HAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,oBAAoB;;;;;;;;;;;;YAIlE,4BACC;;kCAEE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAS;;;;;;;;;;;;;0CAKpC,6LAAC;gCAAI,WAAU;0CACZ,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC/B,6LAAC;wCAAuB,QAAQ;uCAAf;;;;;;;;;;;;;;;;kCAMvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,6LAAC;gDAAK,WAAU;;oDAAmC,WAAW,UAAU;oDAAC;;;;;;;;;;;;;kDAE3E,6LAAC;wCAAc,YAAY,WAAW,UAAU;;;;;;;;;;;;0CAGlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAE,WAAU;kDAA8B,WAAW,SAAS;;;;;;kDAC/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAE;oDAAwB,KAAK,MAAM;oDAAC;;;;;;;0DACvC,6LAAC;0DAAE;;;;;;0DACH,6LAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAMT,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,6LAAC;oCACC,SAAS;wCACP,UAAU,SAAS,CAAC,SAAS,CAAC,WAAW,OAAO,CAAC,IAAI,CAAC;oCACxD;oCACA,WAAU;8CAET,WAAW,OAAO,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GA1IwB;KAAA", "debugId": null}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/HistoricalData.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { LotteryResult } from '@/types/lottery';\n\ninterface HistoricalDataProps {\n  data: LotteryResult[];\n}\n\nexport default function HistoricalData({ data }: HistoricalDataProps) {\n  const [currentPage, setCurrentPage] = useState(1);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortBy, setSortBy] = useState<'date' | 'id'>('date');\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');\n  \n  const itemsPerPage = 10;\n\n  // Filter and sort data\n  const filteredData = data.filter(result => \n    result.id.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    result.date.includes(searchTerm) ||\n    result.result.some(num => num.toString().includes(searchTerm))\n  );\n\n  const sortedData = [...filteredData].sort((a, b) => {\n    let comparison = 0;\n    \n    if (sortBy === 'date') {\n      comparison = new Date(a.date).getTime() - new Date(b.date).getTime();\n    } else {\n      comparison = a.id.localeCompare(b.id);\n    }\n    \n    return sortOrder === 'asc' ? comparison : -comparison;\n  });\n\n  // Pagination\n  const totalPages = Math.ceil(sortedData.length / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const paginatedData = sortedData.slice(startIndex, startIndex + itemsPerPage);\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const NumberBall = ({ number, size = 'sm' }: { number: number; size?: 'sm' | 'xs' }) => (\n    <div\n      className={`\n        rounded-full bg-blue-500 text-white font-bold flex items-center justify-center\n        ${size === 'sm' ? 'w-8 h-8 text-sm' : 'w-6 h-6 text-xs'}\n      `}\n    >\n      {number}\n    </div>\n  );\n\n  const handleSort = (field: 'date' | 'id') => {\n    if (sortBy === field) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(field);\n      setSortOrder('desc');\n    }\n    setCurrentPage(1);\n  };\n\n  if (!data.length) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <h2 className=\"text-xl font-bold text-gray-800 mb-4\">Historical Data</h2>\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-400 text-4xl mb-2\">📋</div>\n          <p className=\"text-gray-500\">No historical data available</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h2 className=\"text-xl font-bold text-gray-800 mb-4 flex items-center\">\n        <span className=\"mr-2\">📋</span>\n        Historical Data\n      </h2>\n\n      {/* Controls */}\n      <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n        <div className=\"flex-1\">\n          <input\n            type=\"text\"\n            placeholder=\"Search by draw ID, date, or numbers...\"\n            value={searchTerm}\n            onChange={(e) => {\n              setSearchTerm(e.target.value);\n              setCurrentPage(1);\n            }}\n            className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n        <div className=\"flex gap-2\">\n          <button\n            onClick={() => handleSort('date')}\n            className={`px-3 py-2 rounded text-sm transition-colors flex items-center ${\n              sortBy === 'date'\n                ? 'bg-blue-500 text-white'\n                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n          >\n            Date {sortBy === 'date' && (sortOrder === 'asc' ? '↑' : '↓')}\n          </button>\n          <button\n            onClick={() => handleSort('id')}\n            className={`px-3 py-2 rounded text-sm transition-colors flex items-center ${\n              sortBy === 'id'\n                ? 'bg-blue-500 text-white'\n                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n          >\n            Draw ID {sortBy === 'id' && (sortOrder === 'asc' ? '↑' : '↓')}\n          </button>\n        </div>\n      </div>\n\n      {/* Results count */}\n      <div className=\"mb-4 text-sm text-gray-600\">\n        Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, sortedData.length)} of {sortedData.length} results\n        {searchTerm && ` (filtered from ${data.length} total)`}\n      </div>\n\n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"w-full\">\n          <thead>\n            <tr className=\"border-b border-gray-200\">\n              <th className=\"text-left py-3 px-2 font-semibold text-gray-700\">Draw ID</th>\n              <th className=\"text-left py-3 px-2 font-semibold text-gray-700\">Date</th>\n              <th className=\"text-left py-3 px-2 font-semibold text-gray-700\">Numbers</th>\n              <th className=\"text-left py-3 px-2 font-semibold text-gray-700\">Power</th>\n            </tr>\n          </thead>\n          <tbody>\n            {paginatedData.map((result, index) => (\n              <tr\n                key={result.id}\n                className={`border-b border-gray-100 hover:bg-gray-50 transition-colors ${\n                  index % 2 === 0 ? 'bg-white' : 'bg-gray-50'\n                }`}\n              >\n                <td className=\"py-3 px-2 font-mono text-sm\">{result.id}</td>\n                <td className=\"py-3 px-2 text-sm\">{formatDate(result.date)}</td>\n                <td className=\"py-3 px-2\">\n                  <div className=\"flex space-x-1\">\n                    {result.result.map((number, numIndex) => (\n                      <NumberBall key={numIndex} number={number} size=\"xs\" />\n                    ))}\n                  </div>\n                </td>\n                <td className=\"py-3 px-2\">\n                  {result.powerNumber && (\n                    <div className=\"w-6 h-6 rounded-full bg-red-500 text-white font-bold flex items-center justify-center text-xs\">\n                      {result.powerNumber}\n                    </div>\n                  )}\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"flex justify-center items-center space-x-2 mt-6\">\n          <button\n            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\n            disabled={currentPage === 1}\n            className=\"px-3 py-1 rounded text-sm bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Previous\n          </button>\n          \n          <div className=\"flex space-x-1\">\n            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              \n              return (\n                <button\n                  key={pageNum}\n                  onClick={() => setCurrentPage(pageNum)}\n                  className={`px-3 py-1 rounded text-sm transition-colors ${\n                    currentPage === pageNum\n                      ? 'bg-blue-500 text-white'\n                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                  }`}\n                >\n                  {pageNum}\n                </button>\n              );\n            })}\n          </div>\n          \n          <button\n            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}\n            disabled={currentPage === totalPages}\n            className=\"px-3 py-1 rounded text-sm bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Next\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,eAAe,EAAE,IAAI,EAAuB;;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,MAAM,eAAe;IAErB,uBAAuB;IACvB,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,SAC/B,OAAO,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,OAAO,IAAI,CAAC,QAAQ,CAAC,eACrB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,GAAG,QAAQ,CAAC;IAGpD,MAAM,aAAa;WAAI;KAAa,CAAC,IAAI,CAAC,CAAC,GAAG;QAC5C,IAAI,aAAa;QAEjB,IAAI,WAAW,QAAQ;YACrB,aAAa,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;QACpE,OAAO;YACL,aAAa,EAAE,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE;QACtC;QAEA,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEA,aAAa;IACb,MAAM,aAAa,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;IACjD,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,gBAAgB,WAAW,KAAK,CAAC,YAAY,aAAa;IAEhE,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,IAAI,EAA0C,iBACjF,6LAAC;YACC,WAAW,CAAC;;QAEV,EAAE,SAAS,OAAO,oBAAoB,kBAAkB;MAC1D,CAAC;sBAEA;;;;;;IAIL,MAAM,aAAa,CAAC;QAClB,IAAI,WAAW,OAAO;YACpB,aAAa,cAAc,QAAQ,SAAS;QAC9C,OAAO;YACL,UAAU;YACV,aAAa;QACf;QACA,eAAe;IACjB;IAEA,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAuC;;;;;;8BACrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA8B;;;;;;sCAC7C,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC;wBAAK,WAAU;kCAAO;;;;;;oBAAS;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC;gCACT,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC5B,eAAe;4BACjB;4BACA,WAAU;;;;;;;;;;;kCAGd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,WAAW;gCAC1B,WAAW,CAAC,8DAA8D,EACxE,WAAW,SACP,2BACA,+CACJ;;oCACH;oCACO,WAAW,UAAU,CAAC,cAAc,QAAQ,MAAM,GAAG;;;;;;;0CAE7D,6LAAC;gCACC,SAAS,IAAM,WAAW;gCAC1B,WAAW,CAAC,8DAA8D,EACxE,WAAW,OACP,2BACA,+CACJ;;oCACH;oCACU,WAAW,QAAQ,CAAC,cAAc,QAAQ,MAAM,GAAG;;;;;;;;;;;;;;;;;;;0BAMlE,6LAAC;gBAAI,WAAU;;oBAA6B;oBACjC,aAAa;oBAAE;oBAAE,KAAK,GAAG,CAAC,aAAa,cAAc,WAAW,MAAM;oBAAE;oBAAK,WAAW,MAAM;oBAAC;oBACvG,cAAc,CAAC,gBAAgB,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;;;;;;;0BAIxD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;sCACC,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;;;;;;;;;;;;sCAGpE,6LAAC;sCACE,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,6LAAC;oCAEC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,IAAI,aAAa,cAC/B;;sDAEF,6LAAC;4CAAG,WAAU;sDAA+B,OAAO,EAAE;;;;;;sDACtD,6LAAC;4CAAG,WAAU;sDAAqB,WAAW,OAAO,IAAI;;;;;;sDACzD,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;0DACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,yBAC1B,6LAAC;wDAA0B,QAAQ;wDAAQ,MAAK;uDAA/B;;;;;;;;;;;;;;;sDAIvB,6LAAC;4CAAG,WAAU;sDACX,OAAO,WAAW,kBACjB,6LAAC;gDAAI,WAAU;0DACZ,OAAO,WAAW;;;;;;;;;;;;mCAjBpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;YA4BvB,aAAa,mBACZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wBACxD,UAAU,gBAAgB;wBAC1B,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;wBAAY,GAAG,CAAC,GAAG;4BACnD,IAAI;4BACJ,IAAI,cAAc,GAAG;gCACnB,UAAU,IAAI;4BAChB,OAAO,IAAI,eAAe,GAAG;gCAC3B,UAAU,IAAI;4BAChB,OAAO,IAAI,eAAe,aAAa,GAAG;gCACxC,UAAU,aAAa,IAAI;4BAC7B,OAAO;gCACL,UAAU,cAAc,IAAI;4BAC9B;4BAEA,qBACE,6LAAC;gCAEC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,4CAA4C,EACtD,gBAAgB,UACZ,2BACA,+CACJ;0CAED;+BARI;;;;;wBAWX;;;;;;kCAGF,6LAAC;wBACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,YAAY,cAAc;wBACjE,UAAU,gBAAgB;wBAC1B,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX;GAzNwB;KAAA", "debugId": null}}, {"offset": {"line": 1660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport LatestResults from \"@/components/LatestResults\";\nimport StatisticsChart from \"@/components/StatisticsChart\";\nimport NumberSuggestion from \"@/components/NumberSuggestion\";\nimport HistoricalData from \"@/components/HistoricalData\";\nimport { LotteryResult } from \"@/types/lottery\";\n\nexport default function Home() {\n  const [lotteryData, setLotteryData] = useState<LotteryResult[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(\"/api/lottery-data\");\n        if (!response.ok) {\n          throw new Error(\"Failed to fetch lottery data\");\n        }\n        const data = await response.json();\n        setLotteryData(data);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : \"An error occurred\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-lg text-gray-600\">Loading lottery data...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h1 className=\"text-2xl font-bold text-gray-800 mb-2\">\n            Error Loading Data\n          </h1>\n          <p className=\"text-gray-600\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">\n            🎲 Vietlott Power 6/55 Analyzer\n          </h1>\n          <p className=\"text-gray-600 mt-1\">\n            Historical analysis and number suggestions for Vietnam lottery\n          </p>\n        </div>\n      </header>\n\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n          <LatestResults data={lotteryData} />\n          <NumberSuggestion data={lotteryData} />\n        </div>\n\n        <div className=\"mb-8\">\n          <StatisticsChart data={lotteryData} />\n        </div>\n\n        <div>\n          <HistoricalData data={lotteryData} />\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;4CAAY;oBAChB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBACA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,eAAe;oBACjB,EAAE,OAAO,KAAK;wBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAChD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;yBAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAiB;;;;;;kCAC9B,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAGjD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;0BAMtC,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAa;gCAAC,MAAM;;;;;;0CACrB,6LAAC,yIAAA,CAAA,UAAgB;gCAAC,MAAM;;;;;;;;;;;;kCAG1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,wIAAA,CAAA,UAAe;4BAAC,MAAM;;;;;;;;;;;kCAGzB,6LAAC;kCACC,cAAA,6LAAC,uIAAA,CAAA,UAAc;4BAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;AAKhC;GArFwB;KAAA", "debugId": null}}]}