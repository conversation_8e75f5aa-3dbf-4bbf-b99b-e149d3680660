"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, Co<PERSON>, Check, Lightbulb } from "lucide-react";
import { LotteryResult } from "@/types/lottery";
import { LotteryDataService } from "@/services/LotteryDataService";
import Card from "@/components/ui/Card";
import NumberBall from "@/components/ui/NumberBall";
import Button from "@/components/ui/Button";

interface NumberSuggestionProps {
  data: LotteryResult[];
}

interface SuggestionAlgorithm {
  name: string;
  description: string;
  icon: string;
  color: string;
}

const algorithms: SuggestionAlgorithm[] = [
  {
    name: "Hot Numbers",
    description: "Most frequently drawn numbers",
    icon: "🔥",
    color: "from-red-500 to-orange-500",
  },
  {
    name: "Cold Numbers",
    description: "Least frequently drawn numbers",
    icon: "❄️",
    color: "from-blue-500 to-cyan-500",
  },
  {
    name: "Balanced Mix",
    description: "Combination of hot and cold numbers",
    icon: "⚖️",
    color: "from-purple-500 to-pink-500",
  },
  {
    name: "Recent Trends",
    description: "Trending in last 30 days",
    icon: "📈",
    color: "from-green-500 to-emerald-500",
  },
  {
    name: "Random Selection",
    description: "Completely random numbers",
    icon: "🎲",
    color: "from-gray-500 to-gray-600",
  },
  {
    name: "Mathematical Pattern",
    description: "Statistical distribution based",
    icon: "🧮",
    color: "from-indigo-500 to-blue-500",
  },
];

export default function NumberSuggestion({ data }: NumberSuggestionProps) {
  const [selectedAlgorithm, setSelectedAlgorithm] = useState(
    algorithms[0].name
  );
  const [suggestion, setSuggestion] = useState<{
    numbers: number[];
    confidence: number;
    reasoning: string;
  } | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [copied, setCopied] = useState(false);
  const dataService = LotteryDataService.getInstance();

  useEffect(() => {
    if (data.length > 0) {
      generateSuggestion(selectedAlgorithm);
    }
  }, [data, selectedAlgorithm]);

  const generateSuggestion = async (algorithmName: string) => {
    if (data.length === 0) return;

    setIsGenerating(true);

    // Simulate processing time for better UX
    await new Promise((resolve) => setTimeout(resolve, 500));

    let numbers: number[] = [];

    switch (algorithmName) {
      case "Hot Numbers":
        numbers = dataService.getHotNumbers(data);
        break;
      case "Cold Numbers":
        numbers = dataService.getColdNumbers(data);
        break;
      case "Balanced Mix":
        numbers = dataService.getBalancedNumbers(data);
        break;
      case "Recent Trends":
        numbers = dataService.getRecentTrendNumbers(data);
        break;
      case "Random Selection":
        numbers = dataService.getRandomNumbers();
        break;
      case "Mathematical Pattern":
        numbers = dataService.getMathematicalPatternNumbers(data);
        break;
    }

    const confidence = Math.round(
      dataService.calculateConfidence(numbers, data)
    );
    const algorithm = algorithms.find((a) => a.name === algorithmName);

    setSuggestion({
      numbers,
      confidence,
      reasoning: algorithm?.description || "",
    });

    setIsGenerating(false);
  };

  const handleAlgorithmChange = (algorithmName: string) => {
    setSelectedAlgorithm(algorithmName);
  };

  const handleRefresh = () => {
    generateSuggestion(selectedAlgorithm);
  };

  const handleCopy = async () => {
    if (suggestion) {
      await navigator.clipboard.writeText(suggestion.numbers.join(", "));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const ConfidenceBar = ({ confidence }: { confidence: number }) => (
    <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
      <motion.div
        className={`h-3 rounded-full ${
          confidence >= 70
            ? "bg-gradient-to-r from-green-400 to-green-600"
            : confidence >= 40
            ? "bg-gradient-to-r from-yellow-400 to-yellow-600"
            : "bg-gradient-to-r from-red-400 to-red-600"
        }`}
        initial={{ width: 0 }}
        animate={{ width: `${confidence}%` }}
        transition={{ duration: 1, delay: 0.5 }}
      />
    </div>
  );

  if (!data.length) {
    return (
      <Card className="p-6">
        <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
          <Sparkles className="mr-2 text-green-600" size={24} />
          Number Suggestions
        </h2>
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-2">🎲</div>
          <p className="text-gray-500">No data available for suggestions</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6" gradient>
      <motion.h2
        className="text-xl font-bold text-gray-800 mb-6 flex items-center"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Sparkles className="mr-2 text-green-600" size={24} />
        Number Suggestions
      </motion.h2>

      {/* Algorithm Selection */}
      <motion.div
        className="mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <label className="block text-sm font-medium text-gray-700 mb-3 flex items-center">
          <Lightbulb className="mr-2" size={16} />
          Choose Algorithm:
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {algorithms.map((algorithm, index) => (
            <motion.button
              key={algorithm.name}
              onClick={() => handleAlgorithmChange(algorithm.name)}
              className={`
                p-3 rounded-lg border-2 transition-all duration-200 text-left
                ${
                  selectedAlgorithm === algorithm.name
                    ? `border-blue-500 bg-gradient-to-r ${algorithm.color} text-white shadow-lg`
                    : "border-gray-200 bg-white hover:border-gray-300 hover:shadow-md"
                }
              `}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 * index }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center mb-1">
                <span className="text-lg mr-2">{algorithm.icon}</span>
                <span className="font-medium">{algorithm.name}</span>
              </div>
              <p
                className={`text-xs ${
                  selectedAlgorithm === algorithm.name
                    ? "text-white/80"
                    : "text-gray-500"
                }`}
              >
                {algorithm.description}
              </p>
            </motion.button>
          ))}
        </div>
      </motion.div>

      <AnimatePresence mode="wait">
        {isGenerating ? (
          <motion.div
            key="generating"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center py-8"
          >
            <motion.div
              className="w-16 h-16 border-4 border-green-200 border-t-green-600 rounded-full mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <p className="text-gray-600 font-medium">
              Generating suggestions...
            </p>
          </motion.div>
        ) : suggestion ? (
          <motion.div
            key="suggestion"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ delay: 0.3 }}
          >
            {/* Suggested Numbers */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-semibold text-gray-800 flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Your Lucky Numbers:
                </h3>
                <Button
                  onClick={handleRefresh}
                  variant="secondary"
                  size="sm"
                  icon={<RefreshCw size={16} />}
                  loading={isGenerating}
                >
                  Refresh
                </Button>
              </div>

              <div className="flex justify-center space-x-3 mb-6">
                {suggestion.numbers.map((number, index) => (
                  <NumberBall
                    key={index}
                    number={number}
                    variant="suggested"
                    size="lg"
                    delay={0.1 * index}
                  />
                ))}
              </div>
            </div>

            {/* Confidence and Details */}
            <div className="space-y-6">
              <div className="bg-white/60 rounded-xl p-4 border border-white/20">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-sm font-medium text-gray-700 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Confidence Level:
                  </span>
                  <span className="text-lg font-bold text-gray-800">
                    {suggestion.confidence}%
                  </span>
                </div>
                <ConfidenceBar confidence={suggestion.confidence} />
                <p className="text-xs text-gray-500 mt-2">
                  Based on {data.length} historical draws analysis
                </p>
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
                <h4 className="font-medium text-gray-800 mb-2 flex items-center">
                  <span className="text-blue-600 mr-2">ℹ️</span>
                  Algorithm Insights:
                </h4>
                <p className="text-sm text-gray-700 mb-3">
                  {suggestion.reasoning}
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-600">
                  <div className="flex items-center">
                    <span className="w-1 h-1 bg-blue-500 rounded-full mr-2"></span>
                    {data.length} draws analyzed
                  </div>
                  <div className="flex items-center">
                    <span className="w-1 h-1 bg-blue-500 rounded-full mr-2"></span>
                    Numbers sorted ascending
                  </div>
                  <div className="flex items-center">
                    <span className="w-1 h-1 bg-blue-500 rounded-full mr-2"></span>
                    Frequency-based confidence
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Copy */}
            <motion.div
              className="mt-6 pt-4 border-t border-gray-200"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
            >
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 flex items-center">
                  <Copy size={14} className="mr-2" />
                  Quick copy:
                </span>
                <Button
                  onClick={handleCopy}
                  variant="ghost"
                  size="sm"
                  icon={
                    copied ? (
                      <Check size={16} className="text-green-600" />
                    ) : (
                      <Copy size={16} />
                    )
                  }
                  className={copied ? "text-green-600 border-green-300" : ""}
                >
                  {copied ? "Copied!" : suggestion.numbers.join(", ")}
                </Button>
              </div>
            </motion.div>
          </motion.div>
        ) : null}
      </AnimatePresence>
    </Card>
  );
}
