"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, Co<PERSON>, Check, Lightbulb } from "lucide-react";
import { LotteryResult } from "@/types/lottery";
import { LotteryDataService } from "@/services/LotteryDataService";
import Card from "@/components/ui/Card";
import NumberBall from "@/components/ui/NumberBall";
import Button from "@/components/ui/Button";

interface NumberSuggestionProps {
  data: LotteryResult[];
}

interface SuggestionAlgorithm {
  name: string;
  description: string;
  icon: string;
  color: string;
}

const algorithms: SuggestionAlgorithm[] = [
  {
    name: "Hot Numbers",
    description: "Most frequently drawn numbers",
    icon: "🔥",
    color: "from-red-500 to-orange-500",
  },
  {
    name: "Cold Numbers",
    description: "Least frequently drawn numbers",
    icon: "❄️",
    color: "from-blue-500 to-cyan-500",
  },
  {
    name: "Balanced Mix",
    description: "Combination of hot and cold numbers",
    icon: "⚖️",
    color: "from-purple-500 to-pink-500",
  },
  {
    name: "Recent Trends",
    description: "Trending in last 30 days",
    icon: "📈",
    color: "from-green-500 to-emerald-500",
  },
  {
    name: "Random Selection",
    description: "Completely random numbers",
    icon: "🎲",
    color: "from-gray-500 to-gray-600",
  },
  {
    name: "Mathematical Pattern",
    description: "Statistical distribution based",
    icon: "🧮",
    color: "from-indigo-500 to-blue-500",
  },
];

export default function NumberSuggestion({ data }: NumberSuggestionProps) {
  const [selectedAlgorithm, setSelectedAlgorithm] = useState(
    algorithms[0].name
  );
  const [suggestion, setSuggestion] = useState<{
    numbers: number[];
    confidence: number;
    reasoning: string;
  } | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [copied, setCopied] = useState(false);
  const dataService = LotteryDataService.getInstance();

  useEffect(() => {
    if (data.length > 0) {
      generateSuggestion(selectedAlgorithm);
    }
  }, [data, selectedAlgorithm]);

  const generateSuggestion = async (algorithmName: string) => {
    if (data.length === 0) return;

    setIsGenerating(true);

    // Simulate processing time for better UX
    await new Promise((resolve) => setTimeout(resolve, 500));

    let numbers: number[] = [];

    switch (algorithmName) {
      case "Hot Numbers":
        numbers = dataService.getHotNumbers(data);
        break;
      case "Cold Numbers":
        numbers = dataService.getColdNumbers(data);
        break;
      case "Balanced Mix":
        numbers = dataService.getBalancedNumbers(data);
        break;
      case "Recent Trends":
        numbers = dataService.getRecentTrendNumbers(data);
        break;
      case "Random Selection":
        numbers = dataService.getRandomNumbers();
        break;
      case "Mathematical Pattern":
        numbers = dataService.getMathematicalPatternNumbers(data);
        break;
    }

    const confidence = Math.round(
      dataService.calculateConfidence(numbers, data)
    );
    const algorithm = algorithms.find((a) => a.name === algorithmName);

    setSuggestion({
      numbers,
      confidence,
      reasoning: algorithm?.description || "",
    });

    setIsGenerating(false);
  };

  const handleAlgorithmChange = (algorithmName: string) => {
    setSelectedAlgorithm(algorithmName);
  };

  const handleRefresh = () => {
    generateSuggestion(selectedAlgorithm);
  };

  const handleCopy = async () => {
    if (suggestion) {
      await navigator.clipboard.writeText(suggestion.numbers.join(", "));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const ConfidenceBar = ({ confidence }: { confidence: number }) => (
    <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
      <motion.div
        className={`h-3 rounded-full ${
          confidence >= 70
            ? "bg-gradient-to-r from-green-400 to-green-600"
            : confidence >= 40
            ? "bg-gradient-to-r from-yellow-400 to-yellow-600"
            : "bg-gradient-to-r from-red-400 to-red-600"
        }`}
        initial={{ width: 0 }}
        animate={{ width: `${confidence}%` }}
        transition={{ duration: 1, delay: 0.5 }}
      />
    </div>
  );

  if (!data.length) {
    return (
      <Card className="p-6">
        <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
          <Sparkles className="mr-2 text-green-600" size={24} />
          Number Suggestions
        </h2>
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-2">🎲</div>
          <p className="text-gray-500">No data available for suggestions</p>
        </div>
      </Card>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
        <span className="mr-2">🎲</span>
        Number Suggestions
      </h2>

      {/* Algorithm Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Algorithm:
        </label>
        <select
          value={selectedAlgorithm}
          onChange={(e) => handleAlgorithmChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {suggestionAlgorithms.map((algorithm) => (
            <option key={algorithm.name} value={algorithm.name}>
              {algorithm.name}
            </option>
          ))}
        </select>
        <p className="text-xs text-gray-500 mt-1">
          {
            suggestionAlgorithms.find((a) => a.name === selectedAlgorithm)
              ?.description
          }
        </p>
      </div>

      {suggestion && (
        <>
          {/* Suggested Numbers */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-semibold text-gray-800">
                Suggested Numbers:
              </h3>
              <button
                onClick={handleRefresh}
                className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors flex items-center"
              >
                <span className="mr-1">🔄</span>
                Refresh
              </button>
            </div>

            <div className="flex justify-center space-x-2 mb-4">
              {suggestion.numbers.map((number, index) => (
                <NumberBall key={index} number={number} />
              ))}
            </div>
          </div>

          {/* Confidence and Details */}
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Confidence:
                </span>
                <span className="text-sm font-bold text-gray-800">
                  {suggestion.confidence}%
                </span>
              </div>
              <ConfidenceBar confidence={suggestion.confidence} />
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-800 mb-2">
                Algorithm Details:
              </h4>
              <p className="text-sm text-gray-600 mb-2">
                {suggestion.reasoning}
              </p>
              <div className="text-xs text-gray-500">
                <p>• Based on analysis of {data.length} historical draws</p>
                <p>• Numbers are sorted in ascending order</p>
                <p>• Confidence calculated using frequency analysis</p>
              </div>
            </div>
          </div>

          {/* Quick Copy */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Quick copy:</span>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(suggestion.numbers.join(", "));
                }}
                className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded hover:bg-gray-200 transition-colors"
              >
                {suggestion.numbers.join(", ")}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
