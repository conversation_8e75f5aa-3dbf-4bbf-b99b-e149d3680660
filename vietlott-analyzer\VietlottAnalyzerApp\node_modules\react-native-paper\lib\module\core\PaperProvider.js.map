{"version": 3, "names": ["React", "AccessibilityInfo", "Appearance", "SafeAreaProviderCompat", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultThemesByVersion", "ThemeProvider", "MaterialCommunityIcon", "PortalHost", "addEventListener", "PaperProvider", "props", "isOnlyVersionInTheme", "theme", "Object", "keys", "length", "version", "colorSchemeName", "getColorScheme", "reduceMotionEnabled", "setReduceMotionEnabled", "useState", "colorScheme", "setColorScheme", "handleAppearanceChange", "preferences", "useEffect", "subscription", "_subscription", "remove", "appearanceSubscription", "addChangeListener", "removeChangeListener", "useMemo", "_props$theme", "_props$theme2", "themeVersion", "scheme", "defaultThemeBase", "extendedThemeBase", "animation", "scale", "isV3", "children", "settings", "settingsValue", "icon", "rippleEffectEnabled", "createElement", "value"], "sourceRoot": "../../../src", "sources": ["core/PaperProvider.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,iBAAiB,EACjBC,UAAU,QAGL,cAAc;AAErB,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,QAAQ,IAAIC,gBAAgB,QAAkB,YAAY;AACnE,SAASC,sBAAsB,EAAEC,aAAa,QAAQ,WAAW;AACjE,OAAOC,qBAAqB,MAAM,qCAAqC;AACvE,OAAOC,UAAU,MAAM,iCAAiC;AAExD,SAASC,gBAAgB,QAAQ,2BAA2B;AAQ5D,MAAMC,aAAa,GAAIC,KAAY,IAAK;EACtC,MAAMC,oBAAoB,GACxBD,KAAK,CAACE,KAAK,IAAIC,MAAM,CAACC,IAAI,CAACJ,KAAK,CAACE,KAAK,CAAC,CAACG,MAAM,KAAK,CAAC,IAAIL,KAAK,CAACE,KAAK,CAACI,OAAO;EAE7E,MAAMC,eAAe,GAClB,CAAC,CAACP,KAAK,CAACE,KAAK,IAAID,oBAAoB,MAAKX,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,cAAc,CAAC,CAAC,KACvE,OAAO;EAET,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GACjDtB,KAAK,CAACuB,QAAQ,CAAU,KAAK,CAAC;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GACjCzB,KAAK,CAACuB,QAAQ,CAAkBJ,eAAe,CAAC;EAElD,MAAMO,sBAAsB,GAC1BC,WAA6C,IAC1C;IACH,MAAM;MAAEH;IAAY,CAAC,GAAGG,WAAW;IACnCF,cAAc,CAACD,WAAW,CAAC;EAC7B,CAAC;EAEDxB,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,IAAIC,YAAiD;IAErD,IAAI,CAACjB,KAAK,CAACE,KAAK,EAAE;MAChBe,YAAY,GAAGnB,gBAAgB,CAC7BT,iBAAiB,EACjB,qBAAqB,EACrBqB,sBACF,CAAC;IACH;IACA,OAAO,MAAM;MACX,IAAI,CAACV,KAAK,CAACE,KAAK,EAAE;QAAA,IAAAgB,aAAA;QAChB,CAAAA,aAAA,GAAAD,YAAY,cAAAC,aAAA,eAAZA,aAAA,CAAcC,MAAM,CAAC,CAAC;MACxB;IACF,CAAC;EACH,CAAC,EAAE,CAACnB,KAAK,CAACE,KAAK,CAAC,CAAC;EAEjBd,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,IAAII,sBAA2D;IAC/D,IAAI,CAACpB,KAAK,CAACE,KAAK,IAAID,oBAAoB,EAAE;MACxCmB,sBAAsB,GAAG9B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE+B,iBAAiB,CACpDP,sBACF,CAAwC;IAC1C;IACA,OAAO,MAAM;MACX,IAAI,CAACd,KAAK,CAACE,KAAK,IAAID,oBAAoB,EAAE;QACxC,IAAImB,sBAAsB,EAAE;UAC1BA,sBAAsB,CAACD,MAAM,CAAC,CAAC;QACjC,CAAC,MAAM;UACL;UACA7B,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEgC,oBAAoB,CAACR,sBAAsB,CAAC;QAC1D;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACd,KAAK,CAACE,KAAK,EAAED,oBAAoB,CAAC,CAAC;EAEvC,MAAMC,KAAK,GAAGd,KAAK,CAACmC,OAAO,CAAC,MAAM;IAAA,IAAAC,YAAA,EAAAC,aAAA;IAChC,MAAMC,YAAY,GAAG,EAAAF,YAAA,GAAAxB,KAAK,CAACE,KAAK,cAAAsB,YAAA,uBAAXA,YAAA,CAAalB,OAAO,KAAI,CAAC;IAC9C,MAAMqB,MAAM,GAAGf,WAAW,IAAI,OAAO;IACrC,MAAMgB,gBAAgB,GAAGlC,sBAAsB,CAACgC,YAAY,CAAC,CAACC,MAAM,CAAC;IAErE,MAAME,iBAAiB,GAAG;MACxB,GAAGD,gBAAgB;MACnB,GAAG5B,KAAK,CAACE,KAAK;MACdI,OAAO,EAAEoB,YAAY;MACrBI,SAAS,EAAE;QACT,KAAAL,aAAA,GAAGzB,KAAK,CAACE,KAAK,cAAAuB,aAAA,uBAAXA,aAAA,CAAaK,SAAS;QACzBC,KAAK,EAAEtB,mBAAmB,GAAG,CAAC,GAAG;MACnC;IACF,CAAC;IAED,OAAO;MACL,GAAGoB,iBAAiB;MACpBG,IAAI,EAAEH,iBAAiB,CAACvB,OAAO,KAAK;IACtC,CAAC;EACH,CAAC,EAAE,CAACM,WAAW,EAAEZ,KAAK,CAACE,KAAK,EAAEO,mBAAmB,CAAC,CAAC;EAEnD,MAAM;IAAEwB,QAAQ;IAAEC;EAAS,CAAC,GAAGlC,KAAK;EAEpC,MAAMmC,aAAa,GAAG/C,KAAK,CAACmC,OAAO,CACjC,OAAO;IACLa,IAAI,EAAExC,qBAAqB;IAC3ByC,mBAAmB,EAAE,IAAI;IACzB,GAAGH;EACL,CAAC,CAAC,EACF,CAACA,QAAQ,CACX,CAAC;EAED,oBACE9C,KAAA,CAAAkD,aAAA,CAAC/C,sBAAsB,qBACrBH,KAAA,CAAAkD,aAAA,CAACzC,UAAU,qBACTT,KAAA,CAAAkD,aAAA,CAAC7C,gBAAgB;IAAC8C,KAAK,EAAEJ;EAAc,gBACrC/C,KAAA,CAAAkD,aAAA,CAAC3C,aAAa;IAACO,KAAK,EAAEA;EAAM,GAAE+B,QAAwB,CACtC,CACR,CACU,CAAC;AAE7B,CAAC;AAED,eAAelC,aAAa", "ignoreList": []}