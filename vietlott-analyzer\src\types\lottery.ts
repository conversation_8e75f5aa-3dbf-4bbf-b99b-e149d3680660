export interface LotteryResult {
  id: string;
  date: string;
  result: number[];
  powerNumber?: number;
  jackpot1?: string;
  jackpot2?: string;
  processTime?: string;
}

export interface NumberFrequency {
  number: number;
  count: number;
  percentage: number;
}

export interface LotteryStatistics {
  totalDraws: number;
  mostFrequent: NumberFrequency[];
  leastFrequent: NumberFrequency[];
  numberDistribution: NumberFrequency[];
  recentTrends: {
    last30Days: NumberFrequency[];
    last60Days: NumberFrequency[];
    last90Days: NumberFrequency[];
  };
}

export interface SuggestionAlgorithm {
  name: string;
  description: string;
  generate: (data: LotteryResult[]) => number[];
}

export interface NumberSuggestionResult {
  numbers: number[];
  algorithm: string;
  confidence: number;
  reasoning: string;
}
