import "Types.idl";

namespace RNSVG
{
  [default_interface]
  runtimeclass SvgViewManager
    : Microsoft.ReactNative.IViewManager
    , Microsoft.ReactNative.IViewManagerWithReactContext
    , Microsoft.ReactNative.IViewManagerWithNativeProperties
    , Microsoft.ReactNative.IViewManagerWithChildren
    , Microsoft.ReactNative.IViewManagerWithPointerEvents
  {
    SvgViewManager();
  };

  [default_interface]
  unsealed runtimeclass RenderableViewManager
    : Microsoft.ReactNative.IViewManager
    , Microsoft.ReactNative.IViewManagerWithReactContext
    , Microsoft.ReactNative.IViewManagerWithNativeProperties
  {
    RenderableViewManager();
  };

  [default_interface]
  runtimeclass RectViewManager : RenderableViewManager
  {
    RectViewManager();
  };

  [default_interface]
  runtimeclass CircleViewManager : RenderableViewManager
  {
    CircleViewManager();
  };

  [default_interface]
  runtimeclass EllipseViewManager : RenderableViewManager
  {
    EllipseViewManager();
  };

  [default_interface]
  runtimeclass LineViewManager : RenderableViewManager
  {
    LineViewManager();
  };

  [default_interface]
  runtimeclass PathViewManager : RenderableViewManager
  {
    PathViewManager();
  };

  [default_interface]
  runtimeclass UseViewManager : RenderableViewManager
  {
    UseViewManager();
  };

  [default_interface]
  runtimeclass ImageViewManager : RenderableViewManager
  {
    ImageViewManager();
  };

  [default_interface]
  unsealed runtimeclass GroupViewManager
    : RenderableViewManager
    , Microsoft.ReactNative.IViewManagerWithChildren
  {
    GroupViewManager();
  };

  [default_interface]
  runtimeclass DefsViewManager : GroupViewManager
  {
    DefsViewManager();
  };

  [default_interface]
  runtimeclass LinearGradientViewManager : GroupViewManager
  {
    LinearGradientViewManager();
  };

  [default_interface]
  runtimeclass RadialGradientViewManager : GroupViewManager
  {
    RadialGradientViewManager();
  };

  [default_interface]
  runtimeclass PatternViewManager : GroupViewManager
  {
    PatternViewManager();
  };

  [default_interface]
  runtimeclass SymbolViewManager : GroupViewManager
  {
    SymbolViewManager();
  };

  [default_interface]
  runtimeclass ClipPathViewManager : GroupViewManager
  {
    ClipPathViewManager();
  };

  [default_interface]
  runtimeclass MarkerViewManager : GroupViewManager
  {
    MarkerViewManager();
  };

  [default_interface]
  runtimeclass MaskViewManager : GroupViewManager
  {
    MaskViewManager();
  };

  [default_interface]
  unsealed runtimeclass TextViewManager : GroupViewManager
  {
    TextViewManager();
  };

  [default_interface]
  runtimeclass UnsupportedSvgViewManager
    : Microsoft.ReactNative.IViewManager
    , Microsoft.ReactNative.IViewManagerWithReactContext
    , Microsoft.ReactNative.IViewManagerWithChildren
  {
    UnsupportedSvgViewManager(String name);
  };


  [default_interface]
  runtimeclass TSpanViewManager : TextViewManager
  {
    TSpanViewManager();
  };
}
