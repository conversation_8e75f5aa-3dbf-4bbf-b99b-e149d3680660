{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface CardProps {\n  children: ReactNode;\n  className?: string;\n  hover?: boolean;\n  gradient?: boolean;\n  delay?: number;\n}\n\nexport default function Card({ \n  children, \n  className = '', \n  hover = true, \n  gradient = false,\n  delay = 0 \n}: CardProps) {\n  const baseClasses = `\n    rounded-xl shadow-lg backdrop-blur-sm border border-white/10\n    ${gradient \n      ? 'bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-800/90 dark:to-gray-900/70' \n      : 'bg-white/80 dark:bg-gray-800/80'\n    }\n    ${className}\n  `;\n\n  const hoverAnimation = hover ? {\n    whileHover: { \n      scale: 1.02,\n      y: -4,\n      transition: { duration: 0.2 }\n    },\n    whileTap: { scale: 0.98 }\n  } : {};\n\n  return (\n    <motion.div\n      className={baseClasses}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay }}\n      {...hoverAnimation}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAae,SAAS,KAAK,EAC3B,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,WAAW,KAAK,EAChB,QAAQ,CAAC,EACC;IACV,MAAM,cAAc,CAAC;;IAEnB,EAAE,WACE,0FACA,kCACH;IACD,EAAE,UAAU;EACd,CAAC;IAED,MAAM,iBAAiB,QAAQ;QAC7B,YAAY;YACV,OAAO;YACP,GAAG,CAAC;YACJ,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,UAAU;YAAE,OAAO;QAAK;IAC1B,IAAI,CAAC;IAEL,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK;QAAM;QAClC,GAAG,cAAc;kBAEjB;;;;;;AAGP;KApCwB", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/ui/NumberBall.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface NumberBallProps {\n  number: number;\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';\n  variant?: 'primary' | 'secondary' | 'power' | 'hot' | 'cold' | 'suggested';\n  delay?: number;\n  animate?: boolean;\n}\n\nexport default function NumberBall({ \n  number, \n  size = 'md', \n  variant = 'primary',\n  delay = 0,\n  animate = true\n}: NumberBallProps) {\n  const sizeClasses = {\n    xs: 'w-6 h-6 text-xs',\n    sm: 'w-8 h-8 text-sm',\n    md: 'w-10 h-10 text-sm',\n    lg: 'w-12 h-12 text-base',\n    xl: 'w-16 h-16 text-lg'\n  };\n\n  const variantClasses = {\n    primary: 'bg-gradient-to-br from-blue-500 to-blue-600 border-blue-400 shadow-blue-500/25',\n    secondary: 'bg-gradient-to-br from-gray-500 to-gray-600 border-gray-400 shadow-gray-500/25',\n    power: 'bg-gradient-to-br from-red-500 to-red-600 border-red-400 shadow-red-500/25',\n    hot: 'bg-gradient-to-br from-orange-500 to-red-500 border-orange-400 shadow-orange-500/25',\n    cold: 'bg-gradient-to-br from-cyan-500 to-blue-500 border-cyan-400 shadow-cyan-500/25',\n    suggested: 'bg-gradient-to-br from-green-500 to-emerald-600 border-green-400 shadow-green-500/25'\n  };\n\n  const baseClasses = `\n    rounded-full flex items-center justify-center text-white font-bold\n    border-2 shadow-lg backdrop-blur-sm\n    ${sizeClasses[size]}\n    ${variantClasses[variant]}\n  `;\n\n  const ballAnimation = animate ? {\n    initial: { scale: 0, rotate: -180 },\n    animate: { \n      scale: 1, \n      rotate: 0,\n      transition: { \n        type: \"spring\",\n        stiffness: 260,\n        damping: 20,\n        delay \n      }\n    },\n    whileHover: { \n      scale: 1.1,\n      transition: { duration: 0.2 }\n    }\n  } : {};\n\n  return (\n    <motion.div\n      className={baseClasses}\n      {...ballAnimation}\n    >\n      {number}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYe,SAAS,WAAW,EACjC,MAAM,EACN,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,QAAQ,CAAC,EACT,UAAU,IAAI,EACE;IAChB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,OAAO;QACP,KAAK;QACL,MAAM;QACN,WAAW;IACb;IAEA,MAAM,cAAc,CAAC;;;IAGnB,EAAE,WAAW,CAAC,KAAK,CAAC;IACpB,EAAE,cAAc,CAAC,QAAQ,CAAC;EAC5B,CAAC;IAED,MAAM,gBAAgB,UAAU;QAC9B,SAAS;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAI;QAClC,SAAS;YACP,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;gBACT;YACF;QACF;QACA,YAAY;YACV,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF,IAAI,CAAC;IAEL,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACV,GAAG,aAAa;kBAEhB;;;;;;AAGP;KAzDwB", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/LatestResults.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Calendar, Trophy, Zap } from \"lucide-react\";\nimport { LotteryResult } from \"@/types/lottery\";\nimport Card from \"@/components/ui/Card\";\nimport NumberBall from \"@/components/ui/NumberBall\";\n\ninterface LatestResultsProps {\n  data: LotteryResult[];\n}\n\nexport default function LatestResults({ data }: LatestResultsProps) {\n  const latestResults = data.slice(0, 5);\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n    });\n  };\n\n  const formatRelativeTime = (dateString: string) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now.getTime() - date.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 1) return \"Yesterday\";\n    if (diffDays < 7) return `${diffDays} days ago`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;\n    return formatDate(dateString);\n  };\n\n  if (!latestResults.length) {\n    return (\n      <Card className=\"p-6\">\n        <h2 className=\"text-xl font-bold text-gray-800 mb-4 flex items-center\">\n          <Trophy className=\"mr-2 text-blue-600\" size={24} />\n          Latest Results\n        </h2>\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-400 text-4xl mb-2\">🎱</div>\n          <p className=\"text-gray-500\">No lottery results available</p>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"p-6\" gradient>\n      <motion.h2\n        className=\"text-xl font-bold text-gray-800 mb-6 flex items-center\"\n        initial={{ opacity: 0, x: -20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ delay: 0.1 }}\n      >\n        <Trophy className=\"mr-2 text-blue-600\" size={24} />\n        Latest Results\n      </motion.h2>\n\n      <div className=\"space-y-4\">\n        {latestResults.map((result, index) => (\n          <motion.div\n            key={result.id}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.1 * (index + 1) }}\n            className={`\n              p-4 rounded-xl border-2 transition-all duration-300\n              ${\n                index === 0\n                  ? \"border-blue-300 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg\"\n                  : \"border-gray-200 bg-white/50 hover:bg-white/80\"\n              }\n            `}\n          >\n            <div className=\"flex justify-between items-start mb-4\">\n              <div>\n                <h3 className=\"font-semibold text-gray-800 flex items-center\">\n                  Draw #{result.id}\n                  {index === 0 && (\n                    <motion.span\n                      className=\"ml-2 px-2 py-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs rounded-full shadow-lg\"\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      transition={{ delay: 0.5, type: \"spring\" }}\n                    >\n                      Latest\n                    </motion.span>\n                  )}\n                </h3>\n                <div className=\"flex items-center text-sm text-gray-600 mt-1\">\n                  <Calendar size={14} className=\"mr-1\" />\n                  <span>{formatDate(result.date)}</span>\n                  <span className=\"mx-2\">•</span>\n                  <span className=\"text-blue-600 font-medium\">\n                    {formatRelativeTime(result.date)}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <span className=\"text-sm font-medium text-gray-700 flex items-center\">\n                <span className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></span>\n                Numbers:\n              </span>\n              <div className=\"flex space-x-2\">\n                {result.result.map((number, numIndex) => (\n                  <NumberBall\n                    key={numIndex}\n                    number={number}\n                    variant=\"primary\"\n                    delay={0.1 * (numIndex + 1)}\n                    size=\"sm\"\n                  />\n                ))}\n              </div>\n            </div>\n\n            {result.powerNumber && (\n              <div className=\"flex items-center space-x-3\">\n                <span className=\"text-sm font-medium text-gray-700 flex items-center\">\n                  <Zap size={14} className=\"mr-2 text-red-500\" />\n                  Power:\n                </span>\n                <NumberBall\n                  number={result.powerNumber}\n                  variant=\"power\"\n                  delay={0.7}\n                  size=\"sm\"\n                />\n              </div>\n            )}\n          </motion.div>\n        ))}\n      </div>\n\n      <motion.div\n        className=\"mt-6 pt-4 border-t border-gray-200\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.8 }}\n      >\n        <p className=\"text-xs text-gray-500 text-center\">\n          Showing {latestResults.length} most recent draws\n        </p>\n      </motion.div>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AACA;AANA;;;;;;AAYe,SAAS,cAAc,EAAE,IAAI,EAAsB;IAChE,MAAM,gBAAgB,KAAK,KAAK,CAAC,GAAG;IAEpC,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO;QACtD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,SAAS,CAAC;QAC/C,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAChE,OAAO,WAAW;IACpB;IAEA,IAAI,CAAC,cAAc,MAAM,EAAE;QACzB,qBACE,6LAAC,mIAAA,CAAA,UAAI;YAAC,WAAU;;8BACd,6LAAC;oBAAG,WAAU;;sCACZ,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;4BAAqB,MAAM;;;;;;wBAAM;;;;;;;8BAGrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA8B;;;;;;sCAC7C,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC,mIAAA,CAAA,UAAI;QAAC,WAAU;QAAM,QAAQ;;0BAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gBACR,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;wBAAqB,MAAM;;;;;;oBAAM;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,MAAM,CAAC,QAAQ,CAAC;wBAAE;wBACvC,WAAW,CAAC;;cAEV,EACE,UAAU,IACN,yEACA,gDACL;YACH,CAAC;;0CAED,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;gDAAgD;gDACrD,OAAO,EAAE;gDACf,UAAU,mBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,WAAU;oDACV,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDAAE,OAAO;oDAAE;oDACpB,YAAY;wDAAE,OAAO;wDAAK,MAAM;oDAAS;8DAC1C;;;;;;;;;;;;sDAKL,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC9B,6LAAC;8DAAM,WAAW,OAAO,IAAI;;;;;;8DAC7B,6LAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,6LAAC;oDAAK,WAAU;8DACb,mBAAmB,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;0CAMvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAK,WAAU;;;;;;4CAA+C;;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;kDACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,yBAC1B,6LAAC,yIAAA,CAAA,UAAU;gDAET,QAAQ;gDACR,SAAQ;gDACR,OAAO,MAAM,CAAC,WAAW,CAAC;gDAC1B,MAAK;+CAJA;;;;;;;;;;;;;;;;4BAUZ,OAAO,WAAW,kBACjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;0DACd,6LAAC,mMAAA,CAAA,MAAG;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAsB;;;;;;;kDAGjD,6LAAC,yIAAA,CAAA,UAAU;wCACT,QAAQ,OAAO,WAAW;wCAC1B,SAAQ;wCACR,OAAO;wCACP,MAAK;;;;;;;;;;;;;uBAnEN,OAAO,EAAE;;;;;;;;;;0BA2EpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,6LAAC;oBAAE,WAAU;;wBAAoC;wBACtC,cAAc,MAAM;wBAAC;;;;;;;;;;;;;;;;;;AAKxC;KA7IwB", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/services/LotteryDataService.ts"], "sourcesContent": ["import {\n  LotteryResult,\n  LotteryStatistics,\n  NumberFrequency,\n} from \"@/types/lottery\";\n\nexport class LotteryDataService {\n  private static instance: LotteryDataService;\n  private cache: Map<string, any> = new Map();\n  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes\n\n  private constructor() {}\n\n  public static getInstance(): LotteryDataService {\n    if (!LotteryDataService.instance) {\n      LotteryDataService.instance = new LotteryDataService();\n    }\n    return LotteryDataService.instance;\n  }\n\n  public async fetchLotteryData(): Promise<LotteryResult[]> {\n    const cacheKey = \"lottery-data\";\n    const cached = this.cache.get(cacheKey);\n\n    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {\n      return cached.data;\n    }\n\n    try {\n      const response = await fetch(\"/api/lottery-data\");\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      this.cache.set(cacheKey, { data, timestamp: Date.now() });\n      return data;\n    } catch (error) {\n      console.error(\"Failed to fetch lottery data:\", error);\n      throw error;\n    }\n  }\n\n  public calculateNumberFrequency(data: LotteryResult[]): NumberFrequency[] {\n    const frequency: { [key: number]: number } = {};\n    const totalNumbers = data.length * 6;\n\n    data.forEach((result) => {\n      result.result.forEach((number) => {\n        frequency[number] = (frequency[number] || 0) + 1;\n      });\n    });\n\n    const frequencyArray: NumberFrequency[] = [];\n    for (let i = 1; i <= 55; i++) {\n      const count = frequency[i] || 0;\n      frequencyArray.push({\n        number: i,\n        count,\n        percentage: totalNumbers > 0 ? (count / totalNumbers) * 100 : 0,\n      });\n    }\n\n    return frequencyArray.sort((a, b) => b.count - a.count);\n  }\n\n  public calculateStatistics(data: LotteryResult[]): LotteryStatistics {\n    const allFrequency = this.calculateNumberFrequency(data);\n\n    const now = new Date();\n    const getFilteredData = (days: number) => {\n      return data.filter((result) => {\n        const resultDate = new Date(result.date);\n        const diffTime = now.getTime() - resultDate.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays <= days;\n      });\n    };\n\n    return {\n      totalDraws: data.length,\n      mostFrequent: allFrequency.slice(0, 10),\n      leastFrequent: allFrequency.slice(-10).reverse(),\n      numberDistribution: allFrequency,\n      recentTrends: {\n        last30Days: this.calculateNumberFrequency(getFilteredData(30)),\n        last60Days: this.calculateNumberFrequency(getFilteredData(60)),\n        last90Days: this.calculateNumberFrequency(getFilteredData(90)),\n      },\n    };\n  }\n\n  public getHotNumbers(\n    data: LotteryResult[],\n    count: number = 6,\n    maxNumber: number = 55\n  ): number[] {\n    const frequency = this.calculateNumberFrequency(data);\n    const numbers = frequency.slice(0, count).map((f) => f.number);\n    return this.ensureUniqueNumbers(numbers, maxNumber);\n  }\n\n  public getColdNumbers(\n    data: LotteryResult[],\n    count: number = 6,\n    maxNumber: number = 55\n  ): number[] {\n    const frequency = this.calculateNumberFrequency(data);\n    const numbers = frequency\n      .slice(-count)\n      .map((f) => f.number)\n      .reverse();\n    return this.ensureUniqueNumbers(numbers, maxNumber);\n  }\n\n  public getBalancedNumbers(\n    data: LotteryResult[],\n    maxNumber: number = 55\n  ): number[] {\n    const hot = this.getHotNumbers(data, 3, maxNumber);\n    const cold = this.getColdNumbers(data, 3, maxNumber);\n    const combined = [...hot, ...cold];\n    return this.ensureUniqueNumbers(combined, maxNumber);\n  }\n\n  public getRecentTrendNumbers(data: LotteryResult[]): number[] {\n    const stats = this.calculateStatistics(data);\n    return stats.recentTrends.last30Days.slice(0, 6).map((f) => f.number);\n  }\n\n  public getRandomNumbers(maxNumber: number = 55): number[] {\n    const numbers: number[] = [];\n    const used = new Set<number>();\n\n    while (numbers.length < 6) {\n      const num = Math.floor(Math.random() * maxNumber) + 1;\n      if (!used.has(num)) {\n        numbers.push(num);\n        used.add(num);\n      }\n    }\n\n    return numbers.sort((a, b) => a - b);\n  }\n\n  // Utility function to ensure no duplicates in any algorithm\n  private ensureUniqueNumbers(\n    numbers: number[],\n    maxNumber: number = 55\n  ): number[] {\n    const unique = [...new Set(numbers)]; // Remove duplicates\n\n    // If we have fewer than 6 unique numbers, fill with random ones\n    while (unique.length < 6) {\n      const randomNum = Math.floor(Math.random() * maxNumber) + 1;\n      if (!unique.includes(randomNum)) {\n        unique.push(randomNum);\n      }\n    }\n\n    return unique.slice(0, 6).sort((a, b) => a - b);\n  }\n\n  public getMathematicalPatternNumbers(data: LotteryResult[]): number[] {\n    const frequency = this.calculateNumberFrequency(data);\n    const avgFrequency =\n      frequency.reduce((sum, f) => sum + f.count, 0) / frequency.length;\n\n    const balanced = frequency.filter(\n      (f) => Math.abs(f.count - avgFrequency) <= avgFrequency * 0.2\n    );\n\n    if (balanced.length >= 6) {\n      return balanced.slice(0, 6).map((f) => f.number);\n    }\n\n    return frequency.slice(0, 6).map((f) => f.number);\n  }\n\n  public calculateConfidence(numbers: number[], data: LotteryResult[]): number {\n    const stats = this.calculateStatistics(data);\n    const avgFrequency =\n      stats.numberDistribution.reduce((sum, f) => sum + f.count, 0) /\n      stats.numberDistribution.length;\n    const suggestedFrequencies = numbers.map(\n      (num) =>\n        stats.numberDistribution.find((f) => f.number === num)?.count || 0\n    );\n    const avgSuggestedFreq =\n      suggestedFrequencies.reduce((sum, f) => sum + f, 0) /\n      suggestedFrequencies.length;\n    return Math.min(100, Math.max(0, (avgSuggestedFreq / avgFrequency) * 50));\n  }\n\n  // Enhanced Algorithm: Smart Frequency with Recency Bias\n  public getSmartFrequencyNumbers(data: LotteryResult[]): number[] {\n    const recentData = data.slice(0, 50);\n    const allTimeData = data.slice(0, 200);\n\n    const recentFreq = this.calculateNumberFrequency(recentData);\n    const allTimeFreq = this.calculateNumberFrequency(allTimeData);\n\n    // Combine recent and all-time frequencies with weights\n    const smartScores = recentFreq.map((recent) => {\n      const allTime = allTimeFreq.find((f) => f.number === recent.number);\n      const recentWeight = 0.7;\n      const allTimeWeight = 0.3;\n\n      return {\n        number: recent.number,\n        score:\n          recent.percentage * recentWeight +\n          (allTime?.percentage || 0) * allTimeWeight,\n      };\n    });\n\n    return smartScores\n      .sort((a, b) => b.score - a.score)\n      .slice(0, 6)\n      .map((s) => s.number)\n      .sort((a, b) => a - b);\n  }\n\n  // Enhanced Algorithm: Gap Analysis with Prediction\n  public getGapAnalysisNumbers(data: LotteryResult[]): number[] {\n    const gapAnalysis: {\n      [key: number]: { gaps: number[]; avgGap: number; currentGap: number };\n    } = {};\n\n    // Calculate gaps for each number\n    for (let num = 1; num <= 55; num++) {\n      const gaps: number[] = [];\n      let lastSeen = -1;\n\n      data.forEach((result, index) => {\n        if (result.result.includes(num)) {\n          if (lastSeen !== -1) {\n            gaps.push(index - lastSeen);\n          }\n          lastSeen = index;\n        }\n      });\n\n      const avgGap =\n        gaps.length > 0\n          ? gaps.reduce((sum, gap) => sum + gap, 0) / gaps.length\n          : 0;\n      const currentGap = lastSeen === -1 ? data.length : lastSeen;\n\n      gapAnalysis[num] = { gaps, avgGap, currentGap };\n    }\n\n    // Select numbers that are \"due\" based on gap analysis\n    const candidates = Object.entries(gapAnalysis)\n      .map(([num, analysis]) => ({\n        number: parseInt(num),\n        priority:\n          analysis.currentGap >= analysis.avgGap\n            ? analysis.currentGap / analysis.avgGap\n            : 0,\n      }))\n      .filter((c) => c.priority > 1.2)\n      .sort((a, b) => b.priority - a.priority)\n      .slice(0, 6)\n      .map((c) => c.number);\n\n    // If not enough \"due\" numbers, fill with hot numbers\n    if (candidates.length < 6) {\n      const hotNumbers = this.getHotNumbers(data, 6 - candidates.length);\n      candidates.push(...hotNumbers.filter((n) => !candidates.includes(n)));\n    }\n\n    return candidates.slice(0, 6).sort((a, b) => a - b);\n  }\n\n  // Enhanced Algorithm: Pattern-Based Selection\n  public getPatternBasedNumbers(data: LotteryResult[]): number[] {\n    const recentData = data.slice(0, 30);\n\n    // Analyze patterns in recent draws\n    const patterns = {\n      evenOdd: this.analyzeEvenOddPattern(recentData),\n      sumRange: this.analyzeSumPattern(recentData),\n      consecutive: this.analyzeConsecutivePattern(recentData),\n      endDigits: this.analyzeEndDigitPattern(recentData),\n    };\n\n    // Generate numbers based on identified patterns\n    const candidates: number[] = [];\n    const used = new Set<number>();\n\n    // Apply even/odd balance\n    const targetEven = patterns.evenOdd.optimalEven;\n    const targetOdd = 6 - targetEven;\n\n    let evenCount = 0;\n    let oddCount = 0;\n\n    // Get frequency-based candidates\n    const frequency = this.calculateNumberFrequency(data.slice(0, 100));\n    const sortedByFreq = frequency.sort((a, b) => b.count - a.count);\n\n    for (const freq of sortedByFreq) {\n      if (candidates.length >= 6) break;\n\n      const isEven = freq.number % 2 === 0;\n\n      if (\n        (isEven && evenCount < targetEven) ||\n        (!isEven && oddCount < targetOdd)\n      ) {\n        if (!used.has(freq.number)) {\n          candidates.push(freq.number);\n          used.add(freq.number);\n          if (isEven) evenCount++;\n          else oddCount++;\n        }\n      }\n    }\n\n    // Fill remaining slots if needed\n    while (candidates.length < 6) {\n      for (const freq of sortedByFreq) {\n        if (candidates.length >= 6) break;\n        if (!used.has(freq.number)) {\n          candidates.push(freq.number);\n          used.add(freq.number);\n        }\n      }\n    }\n\n    return candidates.slice(0, 6).sort((a, b) => a - b);\n  }\n\n  // Enhanced Algorithm: Weighted Ensemble\n  public getEnsembleNumbers(data: LotteryResult[]): number[] {\n    const algorithms = [\n      { name: \"hot\", numbers: this.getHotNumbers(data), weight: 0.25 },\n      {\n        name: \"smart\",\n        numbers: this.getSmartFrequencyNumbers(data),\n        weight: 0.25,\n      },\n      { name: \"gap\", numbers: this.getGapAnalysisNumbers(data), weight: 0.25 },\n      {\n        name: \"pattern\",\n        numbers: this.getPatternBasedNumbers(data),\n        weight: 0.25,\n      },\n    ];\n\n    const scores: { [key: number]: number } = {};\n\n    // Initialize scores\n    for (let i = 1; i <= 55; i++) {\n      scores[i] = 0;\n    }\n\n    // Calculate weighted scores\n    algorithms.forEach((algo) => {\n      algo.numbers.forEach((num, index) => {\n        scores[num] += algo.weight * (6 - index); // Higher weight for earlier positions\n      });\n    });\n\n    return Object.entries(scores)\n      .sort(([, a], [, b]) => b - a)\n      .slice(0, 6)\n      .map(([num]) => parseInt(num))\n      .sort((a, b) => a - b);\n  }\n\n  // Helper methods for pattern analysis\n  private analyzeEvenOddPattern(data: LotteryResult[]): {\n    optimalEven: number;\n  } {\n    const evenOddCounts = data.map((result) => {\n      const even = result.result.filter((n) => n % 2 === 0).length;\n      return { even, odd: 6 - even };\n    });\n\n    const avgEven =\n      evenOddCounts.reduce((sum, eo) => sum + eo.even, 0) /\n      evenOddCounts.length;\n    return { optimalEven: Math.round(avgEven) };\n  }\n\n  private analyzeSumPattern(data: LotteryResult[]): {\n    optimalSum: number;\n    range: { min: number; max: number };\n  } {\n    const sums = data.map((result) =>\n      result.result.reduce((sum, num) => sum + num, 0)\n    );\n    const avgSum = sums.reduce((sum, s) => sum + s, 0) / sums.length;\n    const minSum = Math.min(...sums);\n    const maxSum = Math.max(...sums);\n\n    return {\n      optimalSum: Math.round(avgSum),\n      range: { min: minSum, max: maxSum },\n    };\n  }\n\n  private analyzeConsecutivePattern(data: LotteryResult[]): {\n    hasConsecutive: boolean;\n    avgConsecutive: number;\n  } {\n    const consecutiveCounts = data.map((result) => {\n      const sorted = [...result.result].sort((a, b) => a - b);\n      let consecutive = 0;\n      for (let i = 0; i < sorted.length - 1; i++) {\n        if (sorted[i + 1] === sorted[i] + 1) {\n          consecutive++;\n        }\n      }\n      return consecutive;\n    });\n\n    const avgConsecutive =\n      consecutiveCounts.reduce((sum, c) => sum + c, 0) /\n      consecutiveCounts.length;\n    return {\n      hasConsecutive: avgConsecutive > 0.5,\n      avgConsecutive,\n    };\n  }\n\n  private analyzeEndDigitPattern(data: LotteryResult[]): {\n    [key: number]: number;\n  } {\n    const endDigits: { [key: number]: number } = {};\n\n    data.forEach((result) => {\n      result.result.forEach((num) => {\n        const endDigit = num % 10;\n        endDigits[endDigit] = (endDigits[endDigit] || 0) + 1;\n      });\n    });\n\n    return endDigits;\n  }\n\n  public clearCache(): void {\n    this.cache.clear();\n  }\n}\n"], "names": [], "mappings": ";;;AAMO,MAAM;IACX,OAAe,SAA6B;IACpC,QAA0B,IAAI,MAAM;IAC3B,YAAY,IAAI,KAAK,KAAK;IAE3C,aAAsB,CAAC;IAEvB,OAAc,cAAkC;QAC9C,IAAI,CAAC,mBAAmB,QAAQ,EAAE;YAChC,mBAAmB,QAAQ,GAAG,IAAI;QACpC;QACA,OAAO,mBAAmB,QAAQ;IACpC;IAEA,MAAa,mBAA6C;QACxD,MAAM,WAAW;QACjB,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE9B,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE;YAC5D,OAAO,OAAO,IAAI;QACpB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;gBAAE;gBAAM,WAAW,KAAK,GAAG;YAAG;YACvD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEO,yBAAyB,IAAqB,EAAqB;QACxE,MAAM,YAAuC,CAAC;QAC9C,MAAM,eAAe,KAAK,MAAM,GAAG;QAEnC,KAAK,OAAO,CAAC,CAAC;YACZ,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;gBACrB,SAAS,CAAC,OAAO,GAAG,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI;YACjD;QACF;QAEA,MAAM,iBAAoC,EAAE;QAC5C,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;YAC5B,MAAM,QAAQ,SAAS,CAAC,EAAE,IAAI;YAC9B,eAAe,IAAI,CAAC;gBAClB,QAAQ;gBACR;gBACA,YAAY,eAAe,IAAI,AAAC,QAAQ,eAAgB,MAAM;YAChE;QACF;QAEA,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;IACxD;IAEO,oBAAoB,IAAqB,EAAqB;QACnE,MAAM,eAAe,IAAI,CAAC,wBAAwB,CAAC;QAEnD,MAAM,MAAM,IAAI;QAChB,MAAM,kBAAkB,CAAC;YACvB,OAAO,KAAK,MAAM,CAAC,CAAC;gBAClB,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;gBACvC,MAAM,WAAW,IAAI,OAAO,KAAK,WAAW,OAAO;gBACnD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;gBAC1D,OAAO,YAAY;YACrB;QACF;QAEA,OAAO;YACL,YAAY,KAAK,MAAM;YACvB,cAAc,aAAa,KAAK,CAAC,GAAG;YACpC,eAAe,aAAa,KAAK,CAAC,CAAC,IAAI,OAAO;YAC9C,oBAAoB;YACpB,cAAc;gBACZ,YAAY,IAAI,CAAC,wBAAwB,CAAC,gBAAgB;gBAC1D,YAAY,IAAI,CAAC,wBAAwB,CAAC,gBAAgB;gBAC1D,YAAY,IAAI,CAAC,wBAAwB,CAAC,gBAAgB;YAC5D;QACF;IACF;IAEO,cACL,IAAqB,EACrB,QAAgB,CAAC,EACjB,YAAoB,EAAE,EACZ;QACV,MAAM,YAAY,IAAI,CAAC,wBAAwB,CAAC;QAChD,MAAM,UAAU,UAAU,KAAK,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;QAC7D,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS;IAC3C;IAEO,eACL,IAAqB,EACrB,QAAgB,CAAC,EACjB,YAAoB,EAAE,EACZ;QACV,MAAM,YAAY,IAAI,CAAC,wBAAwB,CAAC;QAChD,MAAM,UAAU,UACb,KAAK,CAAC,CAAC,OACP,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM,EACnB,OAAO;QACV,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS;IAC3C;IAEO,mBACL,IAAqB,EACrB,YAAoB,EAAE,EACZ;QACV,MAAM,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;QACxC,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;QAC1C,MAAM,WAAW;eAAI;eAAQ;SAAK;QAClC,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU;IAC5C;IAEO,sBAAsB,IAAqB,EAAY;QAC5D,MAAM,QAAQ,IAAI,CAAC,mBAAmB,CAAC;QACvC,OAAO,MAAM,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;IACtE;IAEO,iBAAiB,YAAoB,EAAE,EAAY;QACxD,MAAM,UAAoB,EAAE;QAC5B,MAAM,OAAO,IAAI;QAEjB,MAAO,QAAQ,MAAM,GAAG,EAAG;YACzB,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa;YACpD,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM;gBAClB,QAAQ,IAAI,CAAC;gBACb,KAAK,GAAG,CAAC;YACX;QACF;QAEA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACpC;IAEA,4DAA4D;IACpD,oBACN,OAAiB,EACjB,YAAoB,EAAE,EACZ;QACV,MAAM,SAAS;eAAI,IAAI,IAAI;SAAS,EAAE,oBAAoB;QAE1D,gEAAgE;QAChE,MAAO,OAAO,MAAM,GAAG,EAAG;YACxB,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa;YAC1D,IAAI,CAAC,OAAO,QAAQ,CAAC,YAAY;gBAC/B,OAAO,IAAI,CAAC;YACd;QACF;QAEA,OAAO,OAAO,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAC/C;IAEO,8BAA8B,IAAqB,EAAY;QACpE,MAAM,YAAY,IAAI,CAAC,wBAAwB,CAAC;QAChD,MAAM,eACJ,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,KAAK,UAAU,MAAM;QAEnE,MAAM,WAAW,UAAU,MAAM,CAC/B,CAAC,IAAM,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,eAAe;QAG5D,IAAI,SAAS,MAAM,IAAI,GAAG;YACxB,OAAO,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;QACjD;QAEA,OAAO,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;IAClD;IAEO,oBAAoB,OAAiB,EAAE,IAAqB,EAAU;QAC3E,MAAM,QAAQ,IAAI,CAAC,mBAAmB,CAAC;QACvC,MAAM,eACJ,MAAM,kBAAkB,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,KAC3D,MAAM,kBAAkB,CAAC,MAAM;QACjC,MAAM,uBAAuB,QAAQ,GAAG,CACtC,CAAC,MACC,MAAM,kBAAkB,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,MAAM,SAAS;QAErE,MAAM,mBACJ,qBAAqB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG,KACjD,qBAAqB,MAAM;QAC7B,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,AAAC,mBAAmB,eAAgB;IACvE;IAEA,wDAAwD;IACjD,yBAAyB,IAAqB,EAAY;QAC/D,MAAM,aAAa,KAAK,KAAK,CAAC,GAAG;QACjC,MAAM,cAAc,KAAK,KAAK,CAAC,GAAG;QAElC,MAAM,aAAa,IAAI,CAAC,wBAAwB,CAAC;QACjD,MAAM,cAAc,IAAI,CAAC,wBAAwB,CAAC;QAElD,uDAAuD;QACvD,MAAM,cAAc,WAAW,GAAG,CAAC,CAAC;YAClC,MAAM,UAAU,YAAY,IAAI,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,OAAO,MAAM;YAClE,MAAM,eAAe;YACrB,MAAM,gBAAgB;YAEtB,OAAO;gBACL,QAAQ,OAAO,MAAM;gBACrB,OACE,OAAO,UAAU,GAAG,eACpB,CAAC,SAAS,cAAc,CAAC,IAAI;YACjC;QACF;QAEA,OAAO,YACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM,EACnB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACxB;IAEA,mDAAmD;IAC5C,sBAAsB,IAAqB,EAAY;QAC5D,MAAM,cAEF,CAAC;QAEL,iCAAiC;QACjC,IAAK,IAAI,MAAM,GAAG,OAAO,IAAI,MAAO;YAClC,MAAM,OAAiB,EAAE;YACzB,IAAI,WAAW,CAAC;YAEhB,KAAK,OAAO,CAAC,CAAC,QAAQ;gBACpB,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM;oBAC/B,IAAI,aAAa,CAAC,GAAG;wBACnB,KAAK,IAAI,CAAC,QAAQ;oBACpB;oBACA,WAAW;gBACb;YACF;YAEA,MAAM,SACJ,KAAK,MAAM,GAAG,IACV,KAAK,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,KAAK,MAAM,GACrD;YACN,MAAM,aAAa,aAAa,CAAC,IAAI,KAAK,MAAM,GAAG;YAEnD,WAAW,CAAC,IAAI,GAAG;gBAAE;gBAAM;gBAAQ;YAAW;QAChD;QAEA,sDAAsD;QACtD,MAAM,aAAa,OAAO,OAAO,CAAC,aAC/B,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,GAAK,CAAC;gBACzB,QAAQ,SAAS;gBACjB,UACE,SAAS,UAAU,IAAI,SAAS,MAAM,GAClC,SAAS,UAAU,GAAG,SAAS,MAAM,GACrC;YACR,CAAC,GACA,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,GAAG,KAC3B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,EACtC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;QAEtB,qDAAqD;QACrD,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,WAAW,MAAM;YACjE,WAAW,IAAI,IAAI,WAAW,MAAM,CAAC,CAAC,IAAM,CAAC,WAAW,QAAQ,CAAC;QACnE;QAEA,OAAO,WAAW,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACnD;IAEA,8CAA8C;IACvC,uBAAuB,IAAqB,EAAY;QAC7D,MAAM,aAAa,KAAK,KAAK,CAAC,GAAG;QAEjC,mCAAmC;QACnC,MAAM,WAAW;YACf,SAAS,IAAI,CAAC,qBAAqB,CAAC;YACpC,UAAU,IAAI,CAAC,iBAAiB,CAAC;YACjC,aAAa,IAAI,CAAC,yBAAyB,CAAC;YAC5C,WAAW,IAAI,CAAC,sBAAsB,CAAC;QACzC;QAEA,gDAAgD;QAChD,MAAM,aAAuB,EAAE;QAC/B,MAAM,OAAO,IAAI;QAEjB,yBAAyB;QACzB,MAAM,aAAa,SAAS,OAAO,CAAC,WAAW;QAC/C,MAAM,YAAY,IAAI;QAEtB,IAAI,YAAY;QAChB,IAAI,WAAW;QAEf,iCAAiC;QACjC,MAAM,YAAY,IAAI,CAAC,wBAAwB,CAAC,KAAK,KAAK,CAAC,GAAG;QAC9D,MAAM,eAAe,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;QAE/D,KAAK,MAAM,QAAQ,aAAc;YAC/B,IAAI,WAAW,MAAM,IAAI,GAAG;YAE5B,MAAM,SAAS,KAAK,MAAM,GAAG,MAAM;YAEnC,IACE,AAAC,UAAU,YAAY,cACtB,CAAC,UAAU,WAAW,WACvB;gBACA,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,MAAM,GAAG;oBAC1B,WAAW,IAAI,CAAC,KAAK,MAAM;oBAC3B,KAAK,GAAG,CAAC,KAAK,MAAM;oBACpB,IAAI,QAAQ;yBACP;gBACP;YACF;QACF;QAEA,iCAAiC;QACjC,MAAO,WAAW,MAAM,GAAG,EAAG;YAC5B,KAAK,MAAM,QAAQ,aAAc;gBAC/B,IAAI,WAAW,MAAM,IAAI,GAAG;gBAC5B,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,MAAM,GAAG;oBAC1B,WAAW,IAAI,CAAC,KAAK,MAAM;oBAC3B,KAAK,GAAG,CAAC,KAAK,MAAM;gBACtB;YACF;QACF;QAEA,OAAO,WAAW,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACnD;IAEA,wCAAwC;IACjC,mBAAmB,IAAqB,EAAY;QACzD,MAAM,aAAa;YACjB;gBAAE,MAAM;gBAAO,SAAS,IAAI,CAAC,aAAa,CAAC;gBAAO,QAAQ;YAAK;YAC/D;gBACE,MAAM;gBACN,SAAS,IAAI,CAAC,wBAAwB,CAAC;gBACvC,QAAQ;YACV;YACA;gBAAE,MAAM;gBAAO,SAAS,IAAI,CAAC,qBAAqB,CAAC;gBAAO,QAAQ;YAAK;YACvE;gBACE,MAAM;gBACN,SAAS,IAAI,CAAC,sBAAsB,CAAC;gBACrC,QAAQ;YACV;SACD;QAED,MAAM,SAAoC,CAAC;QAE3C,oBAAoB;QACpB,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;YAC5B,MAAM,CAAC,EAAE,GAAG;QACd;QAEA,4BAA4B;QAC5B,WAAW,OAAO,CAAC,CAAC;YAClB,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK;gBACzB,MAAM,CAAC,IAAI,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,KAAK,GAAG,sCAAsC;YAClF;QACF;QAEA,OAAO,OAAO,OAAO,CAAC,QACnB,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAK,IAAI,GAC3B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,IAAI,GAAK,SAAS,MACxB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACxB;IAEA,sCAAsC;IAC9B,sBAAsB,IAAqB,EAEjD;QACA,MAAM,gBAAgB,KAAK,GAAG,CAAC,CAAC;YAC9B,MAAM,OAAO,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,IAAI,MAAM,GAAG,MAAM;YAC5D,OAAO;gBAAE;gBAAM,KAAK,IAAI;YAAK;QAC/B;QAEA,MAAM,UACJ,cAAc,MAAM,CAAC,CAAC,KAAK,KAAO,MAAM,GAAG,IAAI,EAAE,KACjD,cAAc,MAAM;QACtB,OAAO;YAAE,aAAa,KAAK,KAAK,CAAC;QAAS;IAC5C;IAEQ,kBAAkB,IAAqB,EAG7C;QACA,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC,SACrB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK;QAEhD,MAAM,SAAS,KAAK,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG,KAAK,KAAK,MAAM;QAChE,MAAM,SAAS,KAAK,GAAG,IAAI;QAC3B,MAAM,SAAS,KAAK,GAAG,IAAI;QAE3B,OAAO;YACL,YAAY,KAAK,KAAK,CAAC;YACvB,OAAO;gBAAE,KAAK;gBAAQ,KAAK;YAAO;QACpC;IACF;IAEQ,0BAA0B,IAAqB,EAGrD;QACA,MAAM,oBAAoB,KAAK,GAAG,CAAC,CAAC;YAClC,MAAM,SAAS;mBAAI,OAAO,MAAM;aAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YACrD,IAAI,cAAc;YAClB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,GAAG,GAAG,IAAK;gBAC1C,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC,EAAE,GAAG,GAAG;oBACnC;gBACF;YACF;YACA,OAAO;QACT;QAEA,MAAM,iBACJ,kBAAkB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG,KAC9C,kBAAkB,MAAM;QAC1B,OAAO;YACL,gBAAgB,iBAAiB;YACjC;QACF;IACF;IAEQ,uBAAuB,IAAqB,EAElD;QACA,MAAM,YAAuC,CAAC;QAE9C,KAAK,OAAO,CAAC,CAAC;YACZ,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;gBACrB,MAAM,WAAW,MAAM;gBACvB,SAAS,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI;YACrD;QACF;QAEA,OAAO;IACT;IAEO,aAAmB;QACxB,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;AACF", "debugId": null}}, {"offset": {"line": 852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface ButtonProps {\n  children: ReactNode;\n  onClick?: () => void;\n  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  disabled?: boolean;\n  loading?: boolean;\n  icon?: ReactNode;\n  className?: string;\n}\n\nexport default function Button({\n  children,\n  onClick,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  icon,\n  className = ''\n}: ButtonProps) {\n  const baseClasses = `\n    inline-flex items-center justify-center font-medium rounded-lg\n    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\n    disabled:opacity-50 disabled:cursor-not-allowed\n  `;\n\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n\n  const variantClasses = {\n    primary: `\n      bg-gradient-to-r from-blue-600 to-blue-700 text-white\n      hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500\n      shadow-lg shadow-blue-500/25\n    `,\n    secondary: `\n      bg-gradient-to-r from-gray-600 to-gray-700 text-white\n      hover:from-gray-700 hover:to-gray-800 focus:ring-gray-500\n      shadow-lg shadow-gray-500/25\n    `,\n    success: `\n      bg-gradient-to-r from-green-600 to-green-700 text-white\n      hover:from-green-700 hover:to-green-800 focus:ring-green-500\n      shadow-lg shadow-green-500/25\n    `,\n    danger: `\n      bg-gradient-to-r from-red-600 to-red-700 text-white\n      hover:from-red-700 hover:to-red-800 focus:ring-red-500\n      shadow-lg shadow-red-500/25\n    `,\n    ghost: `\n      bg-transparent text-gray-700 border border-gray-300\n      hover:bg-gray-50 focus:ring-gray-500\n    `\n  };\n\n  const buttonClasses = `\n    ${baseClasses}\n    ${sizeClasses[size]}\n    ${variantClasses[variant]}\n    ${className}\n  `;\n\n  return (\n    <motion.button\n      className={buttonClasses}\n      onClick={onClick}\n      disabled={disabled || loading}\n      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}\n      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}\n      transition={{ duration: 0.1 }}\n    >\n      {loading && (\n        <motion.div\n          className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2\"\n          animate={{ rotate: 360 }}\n          transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n        />\n      )}\n      {icon && !loading && (\n        <span className=\"mr-2\">{icon}</span>\n      )}\n      {children}\n    </motion.button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBe,SAAS,OAAO,EAC7B,QAAQ,EACR,OAAO,EACP,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,IAAI,EACJ,YAAY,EAAE,EACF;IACZ,MAAM,cAAc,CAAC;;;;EAIrB,CAAC;IAED,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,SAAS,CAAC;;;;IAIV,CAAC;QACD,WAAW,CAAC;;;;IAIZ,CAAC;QACD,SAAS,CAAC;;;;IAIV,CAAC;QACD,QAAQ,CAAC;;;;IAIT,CAAC;QACD,OAAO,CAAC;;;IAGR,CAAC;IACH;IAEA,MAAM,gBAAgB,CAAC;IACrB,EAAE,YAAY;IACd,EAAE,WAAW,CAAC,KAAK,CAAC;IACpB,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC1B,EAAE,UAAU;EACd,CAAC;IAED,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW;QACX,SAAS;QACT,UAAU,YAAY;QACtB,YAAY;YAAE,OAAO,YAAY,UAAU,IAAI;QAAK;QACpD,UAAU;YAAE,OAAO,YAAY,UAAU,IAAI;QAAK;QAClD,YAAY;YAAE,UAAU;QAAI;;YAE3B,yBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS;;;;;;YAG/D,QAAQ,CAAC,yBACR,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAEzB;;;;;;;AAGP;KA9EwB", "debugId": null}}, {"offset": {"line": 960, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/StatisticsChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useMemo, memo } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { BarChart3, TrendingUp, Activity, Eye } from \"lucide-react\";\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  LineElement,\n  PointElement,\n} from \"chart.js\";\nimport { Bar, Line } from \"react-chartjs-2\";\nimport { LotteryResult } from \"@/types/lottery\";\nimport { LotteryDataService } from \"@/services/LotteryDataService\";\nimport Card from \"@/components/ui/Card\";\nimport Button from \"@/components/ui/Button\";\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface StatisticsChartProps {\n  data: LotteryResult[];\n}\n\nconst StatisticsChart = memo(function StatisticsChart({\n  data,\n}: StatisticsChartProps) {\n  const [chartType, setChartType] = useState<\"frequency\" | \"trends\">(\n    \"frequency\"\n  );\n  const dataService = LotteryDataService.getInstance();\n\n  // Memoize expensive calculations\n  const statistics = useMemo(() => {\n    if (data.length === 0) return null;\n    return dataService.calculateStatistics(data);\n  }, [data, dataService]);\n\n  if (!statistics || !data.length) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <h2 className=\"text-xl font-bold text-gray-800 mb-4\">\n          Statistics & Analysis\n        </h2>\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-400 text-4xl mb-2\">📊</div>\n          <p className=\"text-gray-500\">No data available for analysis</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Memoize chart data to prevent unnecessary re-renders\n  const frequencyChartData = useMemo(() => {\n    if (!statistics) return null;\n\n    return {\n      labels: statistics.numberDistribution.map((item) =>\n        item.number.toString()\n      ),\n      datasets: [\n        {\n          label: \"Frequency\",\n          data: statistics.numberDistribution.map((item) => item.count),\n          backgroundColor: statistics.numberDistribution.map((item, index) => {\n            if (index < 10) return \"rgba(239, 68, 68, 0.8)\"; // Hot numbers - red\n            if (index >= statistics.numberDistribution.length - 10)\n              return \"rgba(59, 130, 246, 0.8)\"; // Cold numbers - blue\n            return \"rgba(156, 163, 175, 0.8)\"; // Normal numbers - gray\n          }),\n          borderColor: statistics.numberDistribution.map((item, index) => {\n            if (index < 10) return \"rgba(239, 68, 68, 1)\";\n            if (index >= statistics.numberDistribution.length - 10)\n              return \"rgba(59, 130, 246, 1)\";\n            return \"rgba(156, 163, 175, 1)\";\n          }),\n          borderWidth: 1,\n        },\n      ],\n    };\n  }, [statistics]);\n\n  const trendsChartData = {\n    labels: statistics.numberDistribution\n      .slice(0, 20)\n      .map((item) => item.number.toString()),\n    datasets: [\n      {\n        label: \"All Time\",\n        data: statistics.numberDistribution\n          .slice(0, 20)\n          .map((item) => item.count),\n        borderColor: \"rgba(75, 192, 192, 1)\",\n        backgroundColor: \"rgba(75, 192, 192, 0.2)\",\n        tension: 0.1,\n      },\n      {\n        label: \"Last 30 Days\",\n        data: statistics.recentTrends.last30Days\n          .slice(0, 20)\n          .map((item) => item.count),\n        borderColor: \"rgba(255, 99, 132, 1)\",\n        backgroundColor: \"rgba(255, 99, 132, 0.2)\",\n        tension: 0.1,\n      },\n      {\n        label: \"Last 90 Days\",\n        data: statistics.recentTrends.last90Days\n          .slice(0, 20)\n          .map((item) => item.count),\n        borderColor: \"rgba(54, 162, 235, 1)\",\n        backgroundColor: \"rgba(54, 162, 235, 0.2)\",\n        tension: 0.1,\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: \"top\" as const,\n      },\n      title: {\n        display: true,\n        text:\n          chartType === \"frequency\"\n            ? \"Number Frequency Distribution\"\n            : \"Frequency Trends Comparison\",\n      },\n      tooltip: {\n        callbacks: {\n          label: function (context: any) {\n            const percentage = statistics.numberDistribution\n              .find((item) => item.number.toString() === context.label)\n              ?.percentage.toFixed(2);\n            return `${context.dataset.label}: ${context.parsed.y} (${percentage}%)`;\n          },\n        },\n      },\n    },\n    scales: {\n      x: {\n        title: {\n          display: true,\n          text: \"Numbers\",\n        },\n      },\n      y: {\n        title: {\n          display: true,\n          text: \"Frequency\",\n        },\n        beginAtZero: true,\n      },\n    },\n  };\n\n  return (\n    <Card className=\"p-6\" gradient>\n      <div className=\"flex justify-between items-center mb-6\">\n        <motion.h2\n          className=\"text-xl font-bold text-gray-800 flex items-center\"\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.1 }}\n        >\n          <BarChart3 className=\"mr-2 text-blue-600\" size={24} />\n          Statistics & Analysis\n        </motion.h2>\n\n        <motion.div\n          className=\"flex space-x-2\"\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <Button\n            onClick={() => setChartType(\"frequency\")}\n            variant={chartType === \"frequency\" ? \"primary\" : \"ghost\"}\n            size=\"sm\"\n            icon={<BarChart3 size={16} />}\n          >\n            Frequency\n          </Button>\n          <Button\n            onClick={() => setChartType(\"trends\")}\n            variant={chartType === \"trends\" ? \"primary\" : \"ghost\"}\n            size=\"sm\"\n            icon={<TrendingUp size={16} />}\n          >\n            Trends\n          </Button>\n        </motion.div>\n      </div>\n\n      {/* Summary Stats */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n        <div className=\"bg-blue-50 rounded-lg p-3 text-center\">\n          <div className=\"text-2xl font-bold text-blue-600\">\n            {statistics.totalDraws}\n          </div>\n          <div className=\"text-sm text-blue-800\">Total Draws</div>\n        </div>\n        <div className=\"bg-red-50 rounded-lg p-3 text-center\">\n          <div className=\"text-2xl font-bold text-red-600\">\n            {statistics.mostFrequent[0]?.number}\n          </div>\n          <div className=\"text-sm text-red-800\">Hottest Number</div>\n        </div>\n        <div className=\"bg-blue-50 rounded-lg p-3 text-center\">\n          <div className=\"text-2xl font-bold text-blue-600\">\n            {statistics.leastFrequent[0]?.number}\n          </div>\n          <div className=\"text-sm text-blue-800\">Coldest Number</div>\n        </div>\n        <div className=\"bg-green-50 rounded-lg p-3 text-center\">\n          <div className=\"text-2xl font-bold text-green-600\">\n            {Math.round(\n              statistics.numberDistribution.reduce(\n                (sum, item) => sum + item.count,\n                0\n              ) / 55\n            )}\n          </div>\n          <div className=\"text-sm text-green-800\">Avg Frequency</div>\n        </div>\n      </div>\n\n      {/* Chart */}\n      <div className=\"h-96\">\n        {chartType === \"frequency\" ? (\n          <Bar data={frequencyChartData} options={chartOptions} />\n        ) : (\n          <Line data={trendsChartData} options={chartOptions} />\n        )}\n      </div>\n\n      {/* Legend */}\n      <div className=\"mt-4 flex justify-center space-x-6 text-sm\">\n        <div className=\"flex items-center\">\n          <div className=\"w-4 h-4 bg-red-500 rounded mr-2\"></div>\n          <span>Hot Numbers (Top 10)</span>\n        </div>\n        <div className=\"flex items-center\">\n          <div className=\"w-4 h-4 bg-blue-500 rounded mr-2\"></div>\n          <span>Cold Numbers (Bottom 10)</span>\n        </div>\n        <div className=\"flex items-center\">\n          <div className=\"w-4 h-4 bg-gray-400 rounded mr-2\"></div>\n          <span>Normal Numbers</span>\n        </div>\n      </div>\n    </Card>\n  );\n});\n\nexport default StatisticsChart;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAWA;AAEA;AACA;AACA;;;AApBA;;;;;;;;;AAsBA,+JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,+JAAA,CAAA,gBAAa,EACb,+JAAA,CAAA,cAAW,EACX,+JAAA,CAAA,aAAU,EACV,+JAAA,CAAA,cAAW,EACX,+JAAA,CAAA,eAAY,EACZ,+JAAA,CAAA,QAAK,EACL,+JAAA,CAAA,UAAO,EACP,+JAAA,CAAA,SAAM;AAOR,MAAM,gCAAkB,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,UAAE,SAAS,gBAAgB,EACpD,IAAI,EACiB;;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvC;IAEF,MAAM,cAAc,wIAAA,CAAA,qBAAkB,CAAC,WAAW;IAElD,iCAAiC;IACjC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+DAAE;YACzB,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;YAC9B,OAAO,YAAY,mBAAmB,CAAC;QACzC;8DAAG;QAAC;QAAM;KAAY;IAEtB,IAAI,CAAC,cAAc,CAAC,KAAK,MAAM,EAAE;QAC/B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAuC;;;;;;8BAGrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA8B;;;;;;sCAC7C,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,uDAAuD;IACvD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uEAAE;YACjC,IAAI,CAAC,YAAY,OAAO;YAExB,OAAO;gBACL,QAAQ,WAAW,kBAAkB,CAAC,GAAG;mFAAC,CAAC,OACzC,KAAK,MAAM,CAAC,QAAQ;;gBAEtB,UAAU;oBACR;wBACE,OAAO;wBACP,MAAM,WAAW,kBAAkB,CAAC,GAAG;2FAAC,CAAC,OAAS,KAAK,KAAK;;wBAC5D,iBAAiB,WAAW,kBAAkB,CAAC,GAAG;2FAAC,CAAC,MAAM;gCACxD,IAAI,QAAQ,IAAI,OAAO,0BAA0B,oBAAoB;gCACrE,IAAI,SAAS,WAAW,kBAAkB,CAAC,MAAM,GAAG,IAClD,OAAO,2BAA2B,sBAAsB;gCAC1D,OAAO,4BAA4B,wBAAwB;4BAC7D;;wBACA,aAAa,WAAW,kBAAkB,CAAC,GAAG;2FAAC,CAAC,MAAM;gCACpD,IAAI,QAAQ,IAAI,OAAO;gCACvB,IAAI,SAAS,WAAW,kBAAkB,CAAC,MAAM,GAAG,IAClD,OAAO;gCACT,OAAO;4BACT;;wBACA,aAAa;oBACf;iBACD;YACH;QACF;sEAAG;QAAC;KAAW;IAEf,MAAM,kBAAkB;QACtB,QAAQ,WAAW,kBAAkB,CAClC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM,CAAC,QAAQ;QACrC,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,WAAW,kBAAkB,CAChC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK;gBAC3B,aAAa;gBACb,iBAAiB;gBACjB,SAAS;YACX;YACA;gBACE,OAAO;gBACP,MAAM,WAAW,YAAY,CAAC,UAAU,CACrC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK;gBAC3B,aAAa;gBACb,iBAAiB;gBACjB,SAAS;YACX;YACA;gBACE,OAAO;gBACP,MAAM,WAAW,YAAY,CAAC,UAAU,CACrC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK;gBAC3B,aAAa;gBACb,iBAAiB;gBACjB,SAAS;YACX;SACD;IACH;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MACE,cAAc,cACV,kCACA;YACR;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAU,OAAY;wBAC3B,MAAM,aAAa,WAAW,kBAAkB,CAC7C,IAAI,CAAC,CAAC,OAAS,KAAK,MAAM,CAAC,QAAQ,OAAO,QAAQ,KAAK,GACtD,WAAW,QAAQ;wBACvB,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC;oBACzE;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,OAAO;oBACL,SAAS;oBACT,MAAM;gBACR;YACF;YACA,GAAG;gBACD,OAAO;oBACL,SAAS;oBACT,MAAM;gBACR;gBACA,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,UAAI;QAAC,WAAU;QAAM,QAAQ;;0BAC5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6LAAC,qNAAA,CAAA,YAAS;gCAAC,WAAU;gCAAqB,MAAM;;;;;;4BAAM;;;;;;;kCAIxD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6LAAC,qIAAA,CAAA,UAAM;gCACL,SAAS,IAAM,aAAa;gCAC5B,SAAS,cAAc,cAAc,YAAY;gCACjD,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,YAAS;oCAAC,MAAM;;;;;;0CACxB;;;;;;0CAGD,6LAAC,qIAAA,CAAA,UAAM;gCACL,SAAS,IAAM,aAAa;gCAC5B,SAAS,cAAc,WAAW,YAAY;gCAC9C,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,aAAU;oCAAC,MAAM;;;;;;0CACzB;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,WAAW,UAAU;;;;;;0CAExB,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,WAAW,YAAY,CAAC,EAAE,EAAE;;;;;;0CAE/B,6LAAC;gCAAI,WAAU;0CAAuB;;;;;;;;;;;;kCAExC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,WAAW,aAAa,CAAC,EAAE,EAAE;;;;;;0CAEhC,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,CACT,WAAW,kBAAkB,CAAC,MAAM,CAClC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAC/B,KACE;;;;;;0CAGR,6LAAC;gCAAI,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;0BAK5C,6LAAC;gBAAI,WAAU;0BACZ,cAAc,4BACb,6LAAC,yJAAA,CAAA,MAAG;oBAAC,MAAM;oBAAoB,SAAS;;;;;yCAExC,6LAAC,yJAAA,CAAA,OAAI;oBAAC,MAAM;oBAAiB,SAAS;;;;;;;;;;;0BAK1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;;uCAEe", "debugId": null}}, {"offset": {"line": 1466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/services/PredictionService.ts"], "sourcesContent": ["import { \n  LotteryResult, \n  PredictionRecord, \n  AlgorithmPerformance, \n  NumberPattern,\n  AdvancedPattern \n} from '@/types/lottery';\n\nexport class PredictionService {\n  private static instance: PredictionService;\n  private predictions: PredictionRecord[] = [];\n  private readonly STORAGE_KEY = 'vietlott-predictions';\n\n  private constructor() {\n    this.loadPredictions();\n  }\n\n  public static getInstance(): PredictionService {\n    if (!PredictionService.instance) {\n      PredictionService.instance = new PredictionService();\n    }\n    return PredictionService.instance;\n  }\n\n  // Advanced Algorithm: Neural Network-inspired Pattern Recognition\n  public getNeuralPatternNumbers(data: LotteryResult[]): number[] {\n    const recentData = data.slice(0, 50); // Last 50 draws\n    const patterns = this.analyzePatterns(recentData);\n    \n    // Weight factors for different patterns\n    const weights = {\n      frequency: 0.3,\n      recency: 0.25,\n      gaps: 0.2,\n      evenOdd: 0.15,\n      sum: 0.1\n    };\n\n    const scores: { [key: number]: number } = {};\n    \n    // Initialize scores\n    for (let i = 1; i <= 55; i++) {\n      scores[i] = 0;\n    }\n\n    // Frequency scoring\n    const frequency = this.calculateFrequency(recentData);\n    frequency.forEach(f => {\n      scores[f.number] += f.percentage * weights.frequency;\n    });\n\n    // Recency scoring (recent numbers get higher scores)\n    recentData.slice(0, 10).forEach((result, index) => {\n      result.result.forEach(num => {\n        scores[num] += (10 - index) * weights.recency;\n      });\n    });\n\n    // Gap analysis scoring\n    const gaps = this.analyzeGaps(data);\n    gaps.forEach(gap => {\n      if (gap.expectedNext) {\n        scores[gap.number] += gap.probability * weights.gaps;\n      }\n    });\n\n    // Even/Odd balance scoring\n    const evenOddBalance = this.getOptimalEvenOddBalance(patterns);\n    for (let i = 1; i <= 55; i++) {\n      const isEven = i % 2 === 0;\n      if ((isEven && evenOddBalance.needMore === 'even') || \n          (!isEven && evenOddBalance.needMore === 'odd')) {\n        scores[i] += weights.evenOdd;\n      }\n    }\n\n    // Sum range scoring\n    const optimalSum = this.getOptimalSum(recentData);\n    const candidates = this.generateCandidatesForSum(scores, optimalSum);\n    \n    return candidates.slice(0, 6);\n  }\n\n  // Advanced Algorithm: Fibonacci Sequence Pattern\n  public getFibonacciPatternNumbers(data: LotteryResult[]): number[] {\n    const fibNumbers = this.generateFibonacci(55);\n    const frequency = this.calculateFrequency(data.slice(0, 100));\n    \n    // Score Fibonacci numbers based on their frequency\n    const fibScores = fibNumbers.map(num => ({\n      number: num,\n      score: frequency.find(f => f.number === num)?.percentage || 0\n    }));\n\n    // Mix high-scoring Fibonacci numbers with complementary numbers\n    const topFib = fibScores.sort((a, b) => b.score - a.score).slice(0, 3);\n    const complementary = this.getComplementaryNumbers(\n      topFib.map(f => f.number), \n      data, \n      3\n    );\n\n    return [...topFib.map(f => f.number), ...complementary].sort((a, b) => a - b);\n  }\n\n  // Advanced Algorithm: Machine Learning-inspired Weighted Selection\n  public getMLWeightedNumbers(data: LotteryResult[]): number[] {\n    const features = this.extractFeatures(data);\n    const weights = this.calculateMLWeights(features);\n    \n    const candidates: { number: number; weight: number }[] = [];\n    \n    for (let i = 1; i <= 55; i++) {\n      let weight = 0;\n      \n      // Feature-based weighting\n      weight += weights.frequency * (features.frequency[i] || 0);\n      weight += weights.recency * (features.recency[i] || 0);\n      weight += weights.pattern * (features.pattern[i] || 0);\n      weight += weights.position * (features.position[i] || 0);\n      \n      candidates.push({ number: i, weight });\n    }\n\n    return candidates\n      .sort((a, b) => b.weight - a.weight)\n      .slice(0, 6)\n      .map(c => c.number)\n      .sort((a, b) => a - b);\n  }\n\n  // Advanced Algorithm: Chaos Theory-based Selection\n  public getChaosTheoryNumbers(data: LotteryResult[]): number[] {\n    const recentData = data.slice(0, 30);\n    const chaosFactors = this.calculateChaosFactors(recentData);\n    \n    // Use chaos factors to generate pseudo-random but pattern-aware numbers\n    const seeds = chaosFactors.map(factor => Math.sin(factor * Math.PI));\n    const numbers: number[] = [];\n    const used = new Set<number>();\n    \n    for (let i = 0; i < 6; i++) {\n      let num = Math.floor((Math.abs(seeds[i % seeds.length]) * 55)) + 1;\n      \n      // Ensure uniqueness\n      while (used.has(num)) {\n        num = (num % 55) + 1;\n      }\n      \n      numbers.push(num);\n      used.add(num);\n    }\n    \n    return numbers.sort((a, b) => a - b);\n  }\n\n  // Store prediction for future comparison\n  public storePrediction(\n    numbers: number[], \n    algorithm: string, \n    confidence: number\n  ): string {\n    const prediction: PredictionRecord = {\n      id: this.generateId(),\n      date: new Date().toISOString().split('T')[0],\n      predictedNumbers: numbers,\n      algorithm,\n      confidence,\n      timestamp: Date.now()\n    };\n\n    this.predictions.push(prediction);\n    this.savePredictions();\n    return prediction.id;\n  }\n\n  // Compare prediction with actual result\n  public comparePrediction(predictionId: string, actualResult: LotteryResult): void {\n    const prediction = this.predictions.find(p => p.id === predictionId);\n    if (!prediction) return;\n\n    const matches = prediction.predictedNumbers.filter(num => \n      actualResult.result.includes(num)\n    ).length;\n\n    prediction.actualNumbers = actualResult.result;\n    prediction.powerNumber = actualResult.powerNumber;\n    prediction.matches = matches;\n    prediction.accuracy = (matches / 6) * 100;\n\n    this.savePredictions();\n  }\n\n  // Get algorithm performance statistics\n  public getAlgorithmPerformance(): AlgorithmPerformance[] {\n    const algorithms = [...new Set(this.predictions.map(p => p.algorithm))];\n    \n    return algorithms.map(algorithm => {\n      const algorithmPredictions = this.predictions.filter(p => \n        p.algorithm === algorithm && p.matches !== undefined\n      );\n\n      if (algorithmPredictions.length === 0) {\n        return {\n          algorithmName: algorithm,\n          totalPredictions: 0,\n          averageMatches: 0,\n          bestMatch: 0,\n          accuracy: 0,\n          confidenceScore: 0,\n          lastUpdated: new Date().toISOString()\n        };\n      }\n\n      const totalMatches = algorithmPredictions.reduce((sum, p) => sum + (p.matches || 0), 0);\n      const averageMatches = totalMatches / algorithmPredictions.length;\n      const bestMatch = Math.max(...algorithmPredictions.map(p => p.matches || 0));\n      const accuracy = algorithmPredictions.reduce((sum, p) => sum + (p.accuracy || 0), 0) / algorithmPredictions.length;\n      const confidenceScore = algorithmPredictions.reduce((sum, p) => sum + p.confidence, 0) / algorithmPredictions.length;\n\n      return {\n        algorithmName: algorithm,\n        totalPredictions: algorithmPredictions.length,\n        averageMatches: Math.round(averageMatches * 100) / 100,\n        bestMatch,\n        accuracy: Math.round(accuracy * 100) / 100,\n        confidenceScore: Math.round(confidenceScore * 100) / 100,\n        lastUpdated: new Date().toISOString()\n      };\n    });\n  }\n\n  // Get all predictions\n  public getPredictions(): PredictionRecord[] {\n    return [...this.predictions].sort((a, b) => b.timestamp - a.timestamp);\n  }\n\n  // Private helper methods\n  private analyzePatterns(data: LotteryResult[]): NumberPattern {\n    const allNumbers = data.flatMap(result => result.result);\n    const consecutive = this.findConsecutivePatterns(allNumbers);\n    const evenOdd = this.analyzeEvenOdd(allNumbers);\n    const sums = data.map(result => result.result.reduce((sum, num) => sum + num, 0));\n    \n    return {\n      consecutive,\n      evenOdd,\n      sumRange: {\n        min: Math.min(...sums),\n        max: Math.max(...sums),\n        average: sums.reduce((sum, s) => sum + s, 0) / sums.length\n      },\n      gaps: this.analyzeNumberGaps(data),\n      repeats: this.findRepeatingPatterns(data)\n    };\n  }\n\n  private calculateFrequency(data: LotteryResult[]): { number: number; percentage: number }[] {\n    const frequency: { [key: number]: number } = {};\n    const totalNumbers = data.length * 6;\n\n    data.forEach(result => {\n      result.result.forEach(number => {\n        frequency[number] = (frequency[number] || 0) + 1;\n      });\n    });\n\n    return Object.entries(frequency).map(([num, count]) => ({\n      number: parseInt(num),\n      percentage: (count / totalNumbers) * 100\n    }));\n  }\n\n  private analyzeGaps(data: LotteryResult[]): { number: number; gap: number; probability: number; expectedNext: boolean }[] {\n    const gaps: { [key: number]: number[] } = {};\n    \n    // Calculate gaps for each number\n    for (let num = 1; num <= 55; num++) {\n      gaps[num] = [];\n      let lastSeen = -1;\n      \n      data.forEach((result, index) => {\n        if (result.result.includes(num)) {\n          if (lastSeen !== -1) {\n            gaps[num].push(index - lastSeen);\n          }\n          lastSeen = index;\n        }\n      });\n    }\n\n    return Object.entries(gaps).map(([num, gapArray]) => {\n      const avgGap = gapArray.length > 0 ? gapArray.reduce((sum, gap) => sum + gap, 0) / gapArray.length : 0;\n      const currentGap = this.getCurrentGap(parseInt(num), data);\n      const probability = currentGap >= avgGap ? Math.min(currentGap / avgGap, 2) : 0;\n      \n      return {\n        number: parseInt(num),\n        gap: currentGap,\n        probability,\n        expectedNext: probability > 1.2\n      };\n    });\n  }\n\n  private getCurrentGap(number: number, data: LotteryResult[]): number {\n    for (let i = 0; i < data.length; i++) {\n      if (data[i].result.includes(number)) {\n        return i;\n      }\n    }\n    return data.length;\n  }\n\n  private generateFibonacci(max: number): number[] {\n    const fib = [1, 1];\n    while (fib[fib.length - 1] < max) {\n      fib.push(fib[fib.length - 1] + fib[fib.length - 2]);\n    }\n    return fib.filter(n => n <= max);\n  }\n\n  private getComplementaryNumbers(existing: number[], data: LotteryResult[], count: number): number[] {\n    const frequency = this.calculateFrequency(data);\n    const available = frequency.filter(f => !existing.includes(f.number));\n    return available\n      .sort((a, b) => b.percentage - a.percentage)\n      .slice(0, count)\n      .map(f => f.number);\n  }\n\n  private extractFeatures(data: LotteryResult[]): any {\n    // Extract various features for ML-like processing\n    return {\n      frequency: this.calculateFrequency(data.slice(0, 100)),\n      recency: this.calculateRecency(data.slice(0, 20)),\n      pattern: this.calculatePatternScores(data.slice(0, 50)),\n      position: this.calculatePositionScores(data.slice(0, 30))\n    };\n  }\n\n  private calculateMLWeights(features: any): any {\n    // Simple weight calculation based on feature importance\n    return {\n      frequency: 0.4,\n      recency: 0.3,\n      pattern: 0.2,\n      position: 0.1\n    };\n  }\n\n  private calculateChaosFactors(data: LotteryResult[]): number[] {\n    return data.map((result, index) => {\n      const sum = result.result.reduce((s, n) => s + n, 0);\n      const variance = this.calculateVariance(result.result);\n      return (sum * variance) / (index + 1);\n    });\n  }\n\n  private calculateVariance(numbers: number[]): number {\n    const mean = numbers.reduce((sum, n) => sum + n, 0) / numbers.length;\n    return numbers.reduce((sum, n) => sum + Math.pow(n - mean, 2), 0) / numbers.length;\n  }\n\n  private generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  private loadPredictions(): void {\n    if (typeof window !== 'undefined') {\n      const stored = localStorage.getItem(this.STORAGE_KEY);\n      if (stored) {\n        this.predictions = JSON.parse(stored);\n      }\n    }\n  }\n\n  private savePredictions(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.predictions));\n    }\n  }\n\n  // Additional helper methods (simplified implementations)\n  private findConsecutivePatterns(numbers: number[]): number[] { return []; }\n  private analyzeEvenOdd(numbers: number[]): { even: number; odd: number } { \n    const even = numbers.filter(n => n % 2 === 0).length;\n    return { even, odd: numbers.length - even };\n  }\n  private analyzeNumberGaps(data: LotteryResult[]): number[] { return []; }\n  private findRepeatingPatterns(data: LotteryResult[]): number[] { return []; }\n  private getOptimalEvenOddBalance(patterns: NumberPattern): { needMore: 'even' | 'odd' } {\n    return { needMore: patterns.evenOdd.even > patterns.evenOdd.odd ? 'odd' : 'even' };\n  }\n  private getOptimalSum(data: LotteryResult[]): number {\n    const sums = data.map(r => r.result.reduce((s, n) => s + n, 0));\n    return sums.reduce((s, sum) => s + sum, 0) / sums.length;\n  }\n  private generateCandidatesForSum(scores: { [key: number]: number }, targetSum: number): number[] {\n    return Object.entries(scores)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 12)\n      .map(([num]) => parseInt(num));\n  }\n  private calculateRecency(data: LotteryResult[]): any { return {}; }\n  private calculatePatternScores(data: LotteryResult[]): any { return {}; }\n  private calculatePositionScores(data: LotteryResult[]): any { return {}; }\n}\n"], "names": [], "mappings": ";;;AAQO,MAAM;IACX,OAAe,SAA4B;IACnC,cAAkC,EAAE,CAAC;IAC5B,cAAc,uBAAuB;IAEtD,aAAsB;QACpB,IAAI,CAAC,eAAe;IACtB;IAEA,OAAc,cAAiC;QAC7C,IAAI,CAAC,kBAAkB,QAAQ,EAAE;YAC/B,kBAAkB,QAAQ,GAAG,IAAI;QACnC;QACA,OAAO,kBAAkB,QAAQ;IACnC;IAEA,kEAAkE;IAC3D,wBAAwB,IAAqB,EAAY;QAC9D,MAAM,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,gBAAgB;QACtD,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QAEtC,wCAAwC;QACxC,MAAM,UAAU;YACd,WAAW;YACX,SAAS;YACT,MAAM;YACN,SAAS;YACT,KAAK;QACP;QAEA,MAAM,SAAoC,CAAC;QAE3C,oBAAoB;QACpB,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;YAC5B,MAAM,CAAC,EAAE,GAAG;QACd;QAEA,oBAAoB;QACpB,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC;QAC1C,UAAU,OAAO,CAAC,CAAA;YAChB,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU,GAAG,QAAQ,SAAS;QACtD;QAEA,qDAAqD;QACrD,WAAW,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,QAAQ;YACvC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAA;gBACpB,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,QAAQ,OAAO;YAC/C;QACF;QAEA,uBAAuB;QACvB,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC;QAC9B,KAAK,OAAO,CAAC,CAAA;YACX,IAAI,IAAI,YAAY,EAAE;gBACpB,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,WAAW,GAAG,QAAQ,IAAI;YACtD;QACF;QAEA,2BAA2B;QAC3B,MAAM,iBAAiB,IAAI,CAAC,wBAAwB,CAAC;QACrD,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;YAC5B,MAAM,SAAS,IAAI,MAAM;YACzB,IAAI,AAAC,UAAU,eAAe,QAAQ,KAAK,UACtC,CAAC,UAAU,eAAe,QAAQ,KAAK,OAAQ;gBAClD,MAAM,CAAC,EAAE,IAAI,QAAQ,OAAO;YAC9B;QACF;QAEA,oBAAoB;QACpB,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC;QACtC,MAAM,aAAa,IAAI,CAAC,wBAAwB,CAAC,QAAQ;QAEzD,OAAO,WAAW,KAAK,CAAC,GAAG;IAC7B;IAEA,iDAAiD;IAC1C,2BAA2B,IAAqB,EAAY;QACjE,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC;QAC1C,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC,KAAK,KAAK,CAAC,GAAG;QAExD,mDAAmD;QACnD,MAAM,YAAY,WAAW,GAAG,CAAC,CAAA,MAAO,CAAC;gBACvC,QAAQ;gBACR,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,MAAM,cAAc;YAC9D,CAAC;QAED,gEAAgE;QAChE,MAAM,SAAS,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG;QACpE,MAAM,gBAAgB,IAAI,CAAC,uBAAuB,CAChD,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,GACxB,MACA;QAGF,OAAO;eAAI,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;eAAM;SAAc,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAC7E;IAEA,mEAAmE;IAC5D,qBAAqB,IAAqB,EAAY;QAC3D,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QACtC,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC;QAExC,MAAM,aAAmD,EAAE;QAE3D,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;YAC5B,IAAI,SAAS;YAEb,0BAA0B;YAC1B,UAAU,QAAQ,SAAS,GAAG,CAAC,SAAS,SAAS,CAAC,EAAE,IAAI,CAAC;YACzD,UAAU,QAAQ,OAAO,GAAG,CAAC,SAAS,OAAO,CAAC,EAAE,IAAI,CAAC;YACrD,UAAU,QAAQ,OAAO,GAAG,CAAC,SAAS,OAAO,CAAC,EAAE,IAAI,CAAC;YACrD,UAAU,QAAQ,QAAQ,GAAG,CAAC,SAAS,QAAQ,CAAC,EAAE,IAAI,CAAC;YAEvD,WAAW,IAAI,CAAC;gBAAE,QAAQ;gBAAG;YAAO;QACtC;QAEA,OAAO,WACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM,EAClC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,EACjB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACxB;IAEA,mDAAmD;IAC5C,sBAAsB,IAAqB,EAAY;QAC5D,MAAM,aAAa,KAAK,KAAK,CAAC,GAAG;QACjC,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;QAEhD,wEAAwE;QACxE,MAAM,QAAQ,aAAa,GAAG,CAAC,CAAA,SAAU,KAAK,GAAG,CAAC,SAAS,KAAK,EAAE;QAClE,MAAM,UAAoB,EAAE;QAC5B,MAAM,OAAO,IAAI;QAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,IAAI,MAAM,KAAK,KAAK,CAAE,KAAK,GAAG,CAAC,KAAK,CAAC,IAAI,MAAM,MAAM,CAAC,IAAI,MAAO;YAEjE,oBAAoB;YACpB,MAAO,KAAK,GAAG,CAAC,KAAM;gBACpB,MAAM,AAAC,MAAM,KAAM;YACrB;YAEA,QAAQ,IAAI,CAAC;YACb,KAAK,GAAG,CAAC;QACX;QAEA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACpC;IAEA,yCAAyC;IAClC,gBACL,OAAiB,EACjB,SAAiB,EACjB,UAAkB,EACV;QACR,MAAM,aAA+B;YACnC,IAAI,IAAI,CAAC,UAAU;YACnB,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5C,kBAAkB;YAClB;YACA;YACA,WAAW,KAAK,GAAG;QACrB;QAEA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,eAAe;QACpB,OAAO,WAAW,EAAE;IACtB;IAEA,wCAAwC;IACjC,kBAAkB,YAAoB,EAAE,YAA2B,EAAQ;QAChF,MAAM,aAAa,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,CAAC,YAAY;QAEjB,MAAM,UAAU,WAAW,gBAAgB,CAAC,MAAM,CAAC,CAAA,MACjD,aAAa,MAAM,CAAC,QAAQ,CAAC,MAC7B,MAAM;QAER,WAAW,aAAa,GAAG,aAAa,MAAM;QAC9C,WAAW,WAAW,GAAG,aAAa,WAAW;QACjD,WAAW,OAAO,GAAG;QACrB,WAAW,QAAQ,GAAG,AAAC,UAAU,IAAK;QAEtC,IAAI,CAAC,eAAe;IACtB;IAEA,uCAAuC;IAChC,0BAAkD;QACvD,MAAM,aAAa;eAAI,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS;SAAG;QAEvE,OAAO,WAAW,GAAG,CAAC,CAAA;YACpB,MAAM,uBAAuB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,IACnD,EAAE,SAAS,KAAK,aAAa,EAAE,OAAO,KAAK;YAG7C,IAAI,qBAAqB,MAAM,KAAK,GAAG;gBACrC,OAAO;oBACL,eAAe;oBACf,kBAAkB;oBAClB,gBAAgB;oBAChB,WAAW;oBACX,UAAU;oBACV,iBAAiB;oBACjB,aAAa,IAAI,OAAO,WAAW;gBACrC;YACF;YAEA,MAAM,eAAe,qBAAqB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG;YACrF,MAAM,iBAAiB,eAAe,qBAAqB,MAAM;YACjE,MAAM,YAAY,KAAK,GAAG,IAAI,qBAAqB,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI;YACzE,MAAM,WAAW,qBAAqB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,QAAQ,IAAI,CAAC,GAAG,KAAK,qBAAqB,MAAM;YAClH,MAAM,kBAAkB,qBAAqB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE,KAAK,qBAAqB,MAAM;YAEpH,OAAO;gBACL,eAAe;gBACf,kBAAkB,qBAAqB,MAAM;gBAC7C,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,OAAO;gBACnD;gBACA,UAAU,KAAK,KAAK,CAAC,WAAW,OAAO;gBACvC,iBAAiB,KAAK,KAAK,CAAC,kBAAkB,OAAO;gBACrD,aAAa,IAAI,OAAO,WAAW;YACrC;QACF;IACF;IAEA,sBAAsB;IACf,iBAAqC;QAC1C,OAAO;eAAI,IAAI,CAAC,WAAW;SAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IACvE;IAEA,yBAAyB;IACjB,gBAAgB,IAAqB,EAAiB;QAC5D,MAAM,aAAa,KAAK,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM;QACvD,MAAM,cAAc,IAAI,CAAC,uBAAuB,CAAC;QACjD,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC;QACpC,MAAM,OAAO,KAAK,GAAG,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK;QAE9E,OAAO;YACL;YACA;YACA,UAAU;gBACR,KAAK,KAAK,GAAG,IAAI;gBACjB,KAAK,KAAK,GAAG,IAAI;gBACjB,SAAS,KAAK,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG,KAAK,KAAK,MAAM;YAC5D;YACA,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAC7B,SAAS,IAAI,CAAC,qBAAqB,CAAC;QACtC;IACF;IAEQ,mBAAmB,IAAqB,EAA4C;QAC1F,MAAM,YAAuC,CAAC;QAC9C,MAAM,eAAe,KAAK,MAAM,GAAG;QAEnC,KAAK,OAAO,CAAC,CAAA;YACX,OAAO,MAAM,CAAC,OAAO,CAAC,CAAA;gBACpB,SAAS,CAAC,OAAO,GAAG,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI;YACjD;QACF;QAEA,OAAO,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,CAAC;gBACtD,QAAQ,SAAS;gBACjB,YAAY,AAAC,QAAQ,eAAgB;YACvC,CAAC;IACH;IAEQ,YAAY,IAAqB,EAAiF;QACxH,MAAM,OAAoC,CAAC;QAE3C,iCAAiC;QACjC,IAAK,IAAI,MAAM,GAAG,OAAO,IAAI,MAAO;YAClC,IAAI,CAAC,IAAI,GAAG,EAAE;YACd,IAAI,WAAW,CAAC;YAEhB,KAAK,OAAO,CAAC,CAAC,QAAQ;gBACpB,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM;oBAC/B,IAAI,aAAa,CAAC,GAAG;wBACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;oBACzB;oBACA,WAAW;gBACb;YACF;QACF;QAEA,OAAO,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS;YAC9C,MAAM,SAAS,SAAS,MAAM,GAAG,IAAI,SAAS,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,SAAS,MAAM,GAAG;YACrG,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,SAAS,MAAM;YACrD,MAAM,cAAc,cAAc,SAAS,KAAK,GAAG,CAAC,aAAa,QAAQ,KAAK;YAE9E,OAAO;gBACL,QAAQ,SAAS;gBACjB,KAAK;gBACL;gBACA,cAAc,cAAc;YAC9B;QACF;IACF;IAEQ,cAAc,MAAc,EAAE,IAAqB,EAAU;QACnE,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS;gBACnC,OAAO;YACT;QACF;QACA,OAAO,KAAK,MAAM;IACpB;IAEQ,kBAAkB,GAAW,EAAY;QAC/C,MAAM,MAAM;YAAC;YAAG;SAAE;QAClB,MAAO,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,GAAG,IAAK;YAChC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QACpD;QACA,OAAO,IAAI,MAAM,CAAC,CAAA,IAAK,KAAK;IAC9B;IAEQ,wBAAwB,QAAkB,EAAE,IAAqB,EAAE,KAAa,EAAY;QAClG,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC;QAC1C,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,IAAK,CAAC,SAAS,QAAQ,CAAC,EAAE,MAAM;QACnE,OAAO,UACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU,EAC1C,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;IACtB;IAEQ,gBAAgB,IAAqB,EAAO;QAClD,kDAAkD;QAClD,OAAO;YACL,WAAW,IAAI,CAAC,kBAAkB,CAAC,KAAK,KAAK,CAAC,GAAG;YACjD,SAAS,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK,CAAC,GAAG;YAC7C,SAAS,IAAI,CAAC,sBAAsB,CAAC,KAAK,KAAK,CAAC,GAAG;YACnD,UAAU,IAAI,CAAC,uBAAuB,CAAC,KAAK,KAAK,CAAC,GAAG;QACvD;IACF;IAEQ,mBAAmB,QAAa,EAAO;QAC7C,wDAAwD;QACxD,OAAO;YACL,WAAW;YACX,SAAS;YACT,SAAS;YACT,UAAU;QACZ;IACF;IAEQ,sBAAsB,IAAqB,EAAY;QAC7D,OAAO,KAAK,GAAG,CAAC,CAAC,QAAQ;YACvB,MAAM,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;YAClD,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC,OAAO,MAAM;YACrD,OAAO,AAAC,MAAM,WAAY,CAAC,QAAQ,CAAC;QACtC;IACF;IAEQ,kBAAkB,OAAiB,EAAU;QACnD,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG,KAAK,QAAQ,MAAM;QACpE,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM,IAAI,KAAK,QAAQ,MAAM;IACpF;IAEQ,aAAqB;QAC3B,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrE;IAEQ,kBAAwB;QAC9B,wCAAmC;YACjC,MAAM,SAAS,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW;YACpD,IAAI,QAAQ;gBACV,IAAI,CAAC,WAAW,GAAG,KAAK,KAAK,CAAC;YAChC;QACF;IACF;IAEQ,kBAAwB;QAC9B,wCAAmC;YACjC,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,WAAW;QACxE;IACF;IAEA,yDAAyD;IACjD,wBAAwB,OAAiB,EAAY;QAAE,OAAO,EAAE;IAAE;IAClE,eAAe,OAAiB,EAAiC;QACvE,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,MAAM,GAAG,MAAM;QACpD,OAAO;YAAE;YAAM,KAAK,QAAQ,MAAM,GAAG;QAAK;IAC5C;IACQ,kBAAkB,IAAqB,EAAY;QAAE,OAAO,EAAE;IAAE;IAChE,sBAAsB,IAAqB,EAAY;QAAE,OAAO,EAAE;IAAE;IACpE,yBAAyB,QAAuB,EAAgC;QACtF,OAAO;YAAE,UAAU,SAAS,OAAO,CAAC,IAAI,GAAG,SAAS,OAAO,CAAC,GAAG,GAAG,QAAQ;QAAO;IACnF;IACQ,cAAc,IAAqB,EAAU;QACnD,MAAM,OAAO,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;QAC5D,OAAO,KAAK,MAAM,CAAC,CAAC,GAAG,MAAQ,IAAI,KAAK,KAAK,KAAK,MAAM;IAC1D;IACQ,yBAAyB,MAAiC,EAAE,SAAiB,EAAY;QAC/F,OAAO,OAAO,OAAO,CAAC,QACnB,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,IAAI,GACzB,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,CAAC,IAAI,GAAK,SAAS;IAC7B;IACQ,iBAAiB,IAAqB,EAAO;QAAE,OAAO,CAAC;IAAG;IAC1D,uBAAuB,IAAqB,EAAO;QAAE,OAAO,CAAC;IAAG;IAChE,wBAAwB,IAAqB,EAAO;QAAE,OAAO,CAAC;IAAG;AAC3E", "debugId": null}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/NumberSuggestion.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  RefreshC<PERSON>,\n  Copy,\n  Check,\n  Lightbulb,\n  Target,\n  TrendingUp,\n  Save,\n} from \"lucide-react\";\nimport {\n  LotteryResult,\n  PredictionRecord,\n  AlgorithmPerformance,\n} from \"@/types/lottery\";\nimport { LotteryDataService } from \"@/services/LotteryDataService\";\nimport { PredictionService } from \"@/services/PredictionService\";\nimport Card from \"@/components/ui/Card\";\nimport NumberBall from \"@/components/ui/NumberBall\";\nimport Button from \"@/components/ui/Button\";\n\ninterface NumberSuggestionProps {\n  data: LotteryResult[];\n}\n\ninterface SuggestionAlgorithm {\n  name: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nconst algorithms: SuggestionAlgorithm[] = [\n  {\n    name: \"Smart Frequency\",\n    description: \"Recent + historical frequency analysis\",\n    icon: \"🧠\",\n    color: \"from-purple-500 to-indigo-500\",\n  },\n  {\n    name: \"Gap Analysis\",\n    description: \"Numbers due based on gap patterns\",\n    icon: \"📊\",\n    color: \"from-blue-500 to-cyan-500\",\n  },\n  {\n    name: \"Pattern Recognition\",\n    description: \"Even/odd, sum, and sequence patterns\",\n    icon: \"🔍\",\n    color: \"from-green-500 to-emerald-500\",\n  },\n  {\n    name: \"Ensemble Method\",\n    description: \"Weighted combination of all algorithms\",\n    icon: \"🎯\",\n    color: \"from-orange-500 to-red-500\",\n  },\n  {\n    name: \"Neural Pattern\",\n    description: \"AI-inspired pattern recognition\",\n    icon: \"🤖\",\n    color: \"from-pink-500 to-purple-500\",\n  },\n  {\n    name: \"Fibonacci Sequence\",\n    description: \"Mathematical Fibonacci patterns\",\n    icon: \"🌀\",\n    color: \"from-teal-500 to-blue-500\",\n  },\n  {\n    name: \"ML Weighted\",\n    description: \"Machine learning weighted selection\",\n    icon: \"⚡\",\n    color: \"from-yellow-500 to-orange-500\",\n  },\n  {\n    name: \"Chaos Theory\",\n    description: \"Chaos theory-based prediction\",\n    icon: \"🌪️\",\n    color: \"from-gray-500 to-slate-600\",\n  },\n];\n\nexport default function NumberSuggestion({ data }: NumberSuggestionProps) {\n  const [selectedAlgorithm, setSelectedAlgorithm] = useState(\n    algorithms[0].name\n  );\n  const [suggestion, setSuggestion] = useState<{\n    numbers: number[];\n    confidence: number;\n    reasoning: string;\n  } | null>(null);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [copied, setCopied] = useState(false);\n  const [showPerformance, setShowPerformance] = useState(false);\n  const [predictions, setPredictions] = useState<PredictionRecord[]>([]);\n  const [performance, setPerformance] = useState<AlgorithmPerformance[]>([]);\n  const [actualResult, setActualResult] = useState(\"\");\n  const [savedPredictionId, setSavedPredictionId] = useState<string | null>(\n    null\n  );\n\n  const dataService = LotteryDataService.getInstance();\n  const predictionService = PredictionService.getInstance();\n\n  useEffect(() => {\n    if (data.length > 0) {\n      generateSuggestion(selectedAlgorithm);\n    }\n  }, [data, selectedAlgorithm]);\n\n  const generateSuggestion = async (algorithmName: string) => {\n    if (data.length === 0) return;\n\n    setIsGenerating(true);\n\n    // Simulate processing time for better UX\n    await new Promise((resolve) => setTimeout(resolve, 800));\n\n    let numbers: number[] = [];\n\n    switch (algorithmName) {\n      case \"Smart Frequency\":\n        numbers = dataService.getSmartFrequencyNumbers(data);\n        break;\n      case \"Gap Analysis\":\n        numbers = dataService.getGapAnalysisNumbers(data);\n        break;\n      case \"Pattern Recognition\":\n        numbers = dataService.getPatternBasedNumbers(data);\n        break;\n      case \"Ensemble Method\":\n        numbers = dataService.getEnsembleNumbers(data);\n        break;\n      case \"Neural Pattern\":\n        numbers = predictionService.getNeuralPatternNumbers(data);\n        break;\n      case \"Fibonacci Sequence\":\n        numbers = predictionService.getFibonacciPatternNumbers(data);\n        break;\n      case \"ML Weighted\":\n        numbers = predictionService.getMLWeightedNumbers(data);\n        break;\n      case \"Chaos Theory\":\n        numbers = predictionService.getChaosTheoryNumbers(data);\n        break;\n      default:\n        numbers = dataService.getEnsembleNumbers(data);\n    }\n\n    const confidence = Math.round(\n      dataService.calculateConfidence(numbers, data)\n    );\n    const algorithm = algorithms.find((a) => a.name === algorithmName);\n\n    setSuggestion({\n      numbers,\n      confidence,\n      reasoning: algorithm?.description || \"\",\n    });\n\n    setIsGenerating(false);\n  };\n\n  const handleAlgorithmChange = (algorithmName: string) => {\n    setSelectedAlgorithm(algorithmName);\n  };\n\n  const handleRefresh = () => {\n    generateSuggestion(selectedAlgorithm);\n  };\n\n  const handleCopy = async () => {\n    if (suggestion) {\n      await navigator.clipboard.writeText(suggestion.numbers.join(\", \"));\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    }\n  };\n\n  const handleSavePrediction = () => {\n    if (suggestion) {\n      const predictionId = predictionService.storePrediction(\n        suggestion.numbers,\n        selectedAlgorithm,\n        suggestion.confidence\n      );\n      setSavedPredictionId(predictionId);\n      loadPredictions();\n    }\n  };\n\n  const handleCompareResult = () => {\n    if (actualResult && savedPredictionId) {\n      try {\n        // Parse actual result: \"09 37 42 45 46 50 14\" format\n        const parts = actualResult.trim().split(/\\s+/);\n        const numbers = parts.slice(0, 6).map((n) => parseInt(n));\n        const powerNumber = parts.length > 6 ? parseInt(parts[6]) : undefined;\n\n        const mockResult: LotteryResult = {\n          id: \"manual\",\n          date: new Date().toISOString().split(\"T\")[0],\n          result: numbers,\n          powerNumber,\n        };\n\n        predictionService.comparePrediction(savedPredictionId, mockResult);\n        loadPredictions();\n        loadPerformance();\n        setActualResult(\"\");\n        setSavedPredictionId(null);\n      } catch (error) {\n        console.error(\"Error parsing actual result:\", error);\n      }\n    }\n  };\n\n  const loadPredictions = () => {\n    setPredictions(predictionService.getPredictions());\n  };\n\n  const loadPerformance = () => {\n    setPerformance(predictionService.getAlgorithmPerformance());\n  };\n\n  // Load predictions and performance on component mount\n  useEffect(() => {\n    loadPredictions();\n    loadPerformance();\n  }, []);\n\n  const ConfidenceBar = ({ confidence }: { confidence: number }) => (\n    <div className=\"w-full bg-gray-200 rounded-full h-3 overflow-hidden\">\n      <motion.div\n        className={`h-3 rounded-full ${\n          confidence >= 70\n            ? \"bg-gradient-to-r from-green-400 to-green-600\"\n            : confidence >= 40\n            ? \"bg-gradient-to-r from-yellow-400 to-yellow-600\"\n            : \"bg-gradient-to-r from-red-400 to-red-600\"\n        }`}\n        initial={{ width: 0 }}\n        animate={{ width: `${confidence}%` }}\n        transition={{ duration: 1, delay: 0.5 }}\n      />\n    </div>\n  );\n\n  if (!data.length) {\n    return (\n      <Card className=\"p-6\">\n        <h2 className=\"text-xl font-bold text-gray-800 mb-4 flex items-center\">\n          <Sparkles className=\"mr-2 text-green-600\" size={24} />\n          Number Suggestions\n        </h2>\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-400 text-4xl mb-2\">🎲</div>\n          <p className=\"text-gray-500\">No data available for suggestions</p>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"p-6\" gradient>\n      <motion.h2\n        className=\"text-xl font-bold text-gray-800 mb-6 flex items-center\"\n        initial={{ opacity: 0, x: -20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ delay: 0.1 }}\n      >\n        <Sparkles className=\"mr-2 text-green-600\" size={24} />\n        Number Suggestions\n      </motion.h2>\n\n      {/* Algorithm Selection */}\n      <motion.div\n        className=\"mb-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n      >\n        <label className=\"block text-sm font-medium text-gray-700 mb-3 flex items-center\">\n          <Lightbulb className=\"mr-2\" size={16} />\n          Choose Algorithm:\n        </label>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n          {algorithms.map((algorithm, index) => (\n            <motion.button\n              key={algorithm.name}\n              onClick={() => handleAlgorithmChange(algorithm.name)}\n              className={`\n                p-3 rounded-lg border-2 transition-all duration-200 text-left\n                ${\n                  selectedAlgorithm === algorithm.name\n                    ? `border-blue-500 bg-gradient-to-r ${algorithm.color} text-white shadow-lg`\n                    : \"border-gray-200 bg-white hover:border-gray-300 hover:shadow-md\"\n                }\n              `}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.1 * index }}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <div className=\"flex items-center mb-1\">\n                <span className=\"text-lg mr-2\">{algorithm.icon}</span>\n                <span className=\"font-medium\">{algorithm.name}</span>\n              </div>\n              <p\n                className={`text-xs ${\n                  selectedAlgorithm === algorithm.name\n                    ? \"text-white/80\"\n                    : \"text-gray-500\"\n                }`}\n              >\n                {algorithm.description}\n              </p>\n            </motion.button>\n          ))}\n        </div>\n      </motion.div>\n\n      <AnimatePresence mode=\"wait\">\n        {isGenerating ? (\n          <motion.div\n            key=\"generating\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"text-center py-8\"\n          >\n            <motion.div\n              className=\"w-16 h-16 border-4 border-green-200 border-t-green-600 rounded-full mx-auto mb-4\"\n              animate={{ rotate: 360 }}\n              transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n            />\n            <p className=\"text-gray-600 font-medium\">\n              Generating suggestions...\n            </p>\n          </motion.div>\n        ) : suggestion ? (\n          <motion.div\n            key=\"suggestion\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            transition={{ delay: 0.3 }}\n          >\n            {/* Suggested Numbers */}\n            <div className=\"mb-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"font-semibold text-gray-800 flex items-center\">\n                  <span className=\"w-2 h-2 bg-green-500 rounded-full mr-2\"></span>\n                  Your Lucky Numbers:\n                </h3>\n                <div className=\"flex space-x-2\">\n                  <Button\n                    onClick={handleSavePrediction}\n                    variant=\"success\"\n                    size=\"sm\"\n                    icon={<Save size={16} />}\n                    disabled={!suggestion || savedPredictionId !== null}\n                  >\n                    {savedPredictionId ? \"Saved\" : \"Save\"}\n                  </Button>\n                  <Button\n                    onClick={handleRefresh}\n                    variant=\"secondary\"\n                    size=\"sm\"\n                    icon={<RefreshCw size={16} />}\n                    loading={isGenerating}\n                  >\n                    Refresh\n                  </Button>\n                </div>\n              </div>\n\n              <div className=\"flex justify-center space-x-3 mb-6\">\n                {suggestion.numbers.map((number, index) => (\n                  <NumberBall\n                    key={index}\n                    number={number}\n                    variant=\"suggested\"\n                    size=\"lg\"\n                    delay={0.1 * index}\n                  />\n                ))}\n              </div>\n            </div>\n\n            {/* Confidence and Details */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-white/60 rounded-xl p-4 border border-white/20\">\n                <div className=\"flex justify-between items-center mb-3\">\n                  <span className=\"text-sm font-medium text-gray-700 flex items-center\">\n                    <span className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></span>\n                    Confidence Level:\n                  </span>\n                  <span className=\"text-lg font-bold text-gray-800\">\n                    {suggestion.confidence}%\n                  </span>\n                </div>\n                <ConfidenceBar confidence={suggestion.confidence} />\n                <p className=\"text-xs text-gray-500 mt-2\">\n                  Based on {data.length} historical draws analysis\n                </p>\n              </div>\n\n              <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200\">\n                <h4 className=\"font-medium text-gray-800 mb-2 flex items-center\">\n                  <span className=\"text-blue-600 mr-2\">ℹ️</span>\n                  Algorithm Insights:\n                </h4>\n                <p className=\"text-sm text-gray-700 mb-3\">\n                  {suggestion.reasoning}\n                </p>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-600\">\n                  <div className=\"flex items-center\">\n                    <span className=\"w-1 h-1 bg-blue-500 rounded-full mr-2\"></span>\n                    {data.length} draws analyzed\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"w-1 h-1 bg-blue-500 rounded-full mr-2\"></span>\n                    Numbers sorted ascending\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"w-1 h-1 bg-blue-500 rounded-full mr-2\"></span>\n                    Frequency-based confidence\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Quick Copy */}\n            <motion.div\n              className=\"mt-6 pt-4 border-t border-gray-200\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.8 }}\n            >\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 flex items-center\">\n                  <Copy size={14} className=\"mr-2\" />\n                  Quick copy:\n                </span>\n                <Button\n                  onClick={handleCopy}\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  icon={\n                    copied ? (\n                      <Check size={16} className=\"text-green-600\" />\n                    ) : (\n                      <Copy size={16} />\n                    )\n                  }\n                  className={copied ? \"text-green-600 border-green-300\" : \"\"}\n                >\n                  {copied ? \"Copied!\" : suggestion.numbers.join(\", \")}\n                </Button>\n              </div>\n            </motion.div>\n\n            {/* Prediction Tracking Section */}\n            {savedPredictionId && (\n              <motion.div\n                className=\"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-xl\"\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: \"auto\" }}\n                transition={{ delay: 0.5 }}\n              >\n                <h4 className=\"font-medium text-yellow-800 mb-3 flex items-center\">\n                  <Target className=\"mr-2\" size={16} />\n                  Compare with Actual Result\n                </h4>\n                <p className=\"text-sm text-yellow-700 mb-3\">\n                  Enter the actual lottery result to track algorithm\n                  performance:\n                </p>\n                <div className=\"flex space-x-2\">\n                  <input\n                    type=\"text\"\n                    value={actualResult}\n                    onChange={(e) => setActualResult(e.target.value)}\n                    placeholder=\"e.g., 09 37 42 45 46 50 14\"\n                    className=\"flex-1 px-3 py-2 border border-yellow-300 rounded-lg text-sm focus:ring-2 focus:ring-yellow-500 focus:border-transparent\"\n                  />\n                  <Button\n                    onClick={handleCompareResult}\n                    variant=\"primary\"\n                    size=\"sm\"\n                    disabled={!actualResult.trim()}\n                  >\n                    Compare\n                  </Button>\n                </div>\n              </motion.div>\n            )}\n\n            {/* Performance Toggle */}\n            <motion.div\n              className=\"mt-6 text-center\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.7 }}\n            >\n              <Button\n                onClick={() => setShowPerformance(!showPerformance)}\n                variant=\"ghost\"\n                size=\"sm\"\n                icon={<TrendingUp size={16} />}\n              >\n                {showPerformance ? \"Hide\" : \"Show\"} Algorithm Performance\n              </Button>\n            </motion.div>\n\n            {/* Algorithm Performance Section */}\n            <AnimatePresence>\n              {showPerformance && (\n                <motion.div\n                  className=\"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl\"\n                  initial={{ opacity: 0, height: 0 }}\n                  animate={{ opacity: 1, height: \"auto\" }}\n                  exit={{ opacity: 0, height: 0 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  <h4 className=\"font-medium text-blue-800 mb-4 flex items-center\">\n                    <TrendingUp className=\"mr-2\" size={16} />\n                    Algorithm Performance Comparison\n                  </h4>\n\n                  {performance.length > 0 ? (\n                    <div className=\"space-y-3\">\n                      {performance\n                        .sort((a, b) => b.averageMatches - a.averageMatches)\n                        .map((perf, index) => (\n                          <div\n                            key={perf.algorithmName}\n                            className={`p-3 rounded-lg border ${\n                              perf.algorithmName === selectedAlgorithm\n                                ? \"border-blue-400 bg-blue-100\"\n                                : \"border-blue-200 bg-white\"\n                            }`}\n                          >\n                            <div className=\"flex justify-between items-center mb-2\">\n                              <span className=\"font-medium text-blue-900\">\n                                #{index + 1} {perf.algorithmName}\n                              </span>\n                              <span className=\"text-sm text-blue-600\">\n                                {perf.totalPredictions} predictions\n                              </span>\n                            </div>\n                            <div className=\"grid grid-cols-3 gap-2 text-xs\">\n                              <div>\n                                <span className=\"text-blue-600\">\n                                  Avg Matches:\n                                </span>\n                                <span className=\"font-bold ml-1\">\n                                  {perf.averageMatches}\n                                </span>\n                              </div>\n                              <div>\n                                <span className=\"text-blue-600\">Best:</span>\n                                <span className=\"font-bold ml-1\">\n                                  {perf.bestMatch}/6\n                                </span>\n                              </div>\n                              <div>\n                                <span className=\"text-blue-600\">Accuracy:</span>\n                                <span className=\"font-bold ml-1\">\n                                  {perf.accuracy}%\n                                </span>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                    </div>\n                  ) : (\n                    <p className=\"text-blue-600 text-sm text-center py-4\">\n                      No performance data yet. Save predictions and compare with\n                      actual results to see algorithm performance.\n                    </p>\n                  )}\n                </motion.div>\n              )}\n            </AnimatePresence>\n\n            {/* Recent Predictions */}\n            {predictions.length > 0 && (\n              <motion.div\n                className=\"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-xl\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.9 }}\n              >\n                <h4 className=\"font-medium text-gray-800 mb-3\">\n                  Recent Predictions\n                </h4>\n                <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n                  {predictions.slice(0, 5).map((pred) => (\n                    <div\n                      key={pred.id}\n                      className=\"flex justify-between items-center p-2 bg-white rounded border text-xs\"\n                    >\n                      <div>\n                        <span className=\"font-medium\">{pred.algorithm}</span>\n                        <span className=\"text-gray-500 ml-2\">{pred.date}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-gray-600\">\n                          {pred.predictedNumbers.join(\", \")}\n                        </span>\n                        {pred.matches !== undefined && (\n                          <span\n                            className={`px-2 py-1 rounded text-xs font-bold ${\n                              pred.matches >= 3\n                                ? \"bg-green-100 text-green-800\"\n                                : pred.matches >= 1\n                                ? \"bg-yellow-100 text-yellow-800\"\n                                : \"bg-red-100 text-red-800\"\n                            }`}\n                          >\n                            {pred.matches}/6\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </motion.div>\n            )}\n          </motion.div>\n        ) : null}\n      </AnimatePresence>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AACA;AACA;;;AAvBA;;;;;;;;;AAoCA,MAAM,aAAoC;IACxC;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;CACD;AAEc,SAAS,iBAAiB,EAAE,IAAI,EAAyB;;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvD,UAAU,CAAC,EAAE,CAAC,IAAI;IAEpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIjC;IACV,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvD;IAGF,MAAM,cAAc,wIAAA,CAAA,qBAAkB,CAAC,WAAW;IAClD,MAAM,oBAAoB,uIAAA,CAAA,oBAAiB,CAAC,WAAW;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,KAAK,MAAM,GAAG,GAAG;gBACnB,mBAAmB;YACrB;QACF;qCAAG;QAAC;QAAM;KAAkB;IAE5B,MAAM,qBAAqB,OAAO;QAChC,IAAI,KAAK,MAAM,KAAK,GAAG;QAEvB,gBAAgB;QAEhB,yCAAyC;QACzC,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QAEnD,IAAI,UAAoB,EAAE;QAE1B,OAAQ;YACN,KAAK;gBACH,UAAU,YAAY,wBAAwB,CAAC;gBAC/C;YACF,KAAK;gBACH,UAAU,YAAY,qBAAqB,CAAC;gBAC5C;YACF,KAAK;gBACH,UAAU,YAAY,sBAAsB,CAAC;gBAC7C;YACF,KAAK;gBACH,UAAU,YAAY,kBAAkB,CAAC;gBACzC;YACF,KAAK;gBACH,UAAU,kBAAkB,uBAAuB,CAAC;gBACpD;YACF,KAAK;gBACH,UAAU,kBAAkB,0BAA0B,CAAC;gBACvD;YACF,KAAK;gBACH,UAAU,kBAAkB,oBAAoB,CAAC;gBACjD;YACF,KAAK;gBACH,UAAU,kBAAkB,qBAAqB,CAAC;gBAClD;YACF;gBACE,UAAU,YAAY,kBAAkB,CAAC;QAC7C;QAEA,MAAM,aAAa,KAAK,KAAK,CAC3B,YAAY,mBAAmB,CAAC,SAAS;QAE3C,MAAM,YAAY,WAAW,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;QAEpD,cAAc;YACZ;YACA;YACA,WAAW,WAAW,eAAe;QACvC;QAEA,gBAAgB;IAClB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,qBAAqB;IACvB;IAEA,MAAM,gBAAgB;QACpB,mBAAmB;IACrB;IAEA,MAAM,aAAa;QACjB,IAAI,YAAY;YACd,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,WAAW,OAAO,CAAC,IAAI,CAAC;YAC5D,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,YAAY;YACd,MAAM,eAAe,kBAAkB,eAAe,CACpD,WAAW,OAAO,EAClB,mBACA,WAAW,UAAU;YAEvB,qBAAqB;YACrB;QACF;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,gBAAgB,mBAAmB;YACrC,IAAI;gBACF,qDAAqD;gBACrD,MAAM,QAAQ,aAAa,IAAI,GAAG,KAAK,CAAC;gBACxC,MAAM,UAAU,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAM,SAAS;gBACtD,MAAM,cAAc,MAAM,MAAM,GAAG,IAAI,SAAS,KAAK,CAAC,EAAE,IAAI;gBAE5D,MAAM,aAA4B;oBAChC,IAAI;oBACJ,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC5C,QAAQ;oBACR;gBACF;gBAEA,kBAAkB,iBAAiB,CAAC,mBAAmB;gBACvD;gBACA;gBACA,gBAAgB;gBAChB,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,eAAe,kBAAkB,cAAc;IACjD;IAEA,MAAM,kBAAkB;QACtB,eAAe,kBAAkB,uBAAuB;IAC1D;IAEA,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;YACA;QACF;qCAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC,EAAE,UAAU,EAA0B,iBAC3D,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,iBAAiB,EAC3B,cAAc,KACV,iDACA,cAAc,KACd,mDACA,4CACJ;gBACF,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS;oBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;gBAAC;gBACnC,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;;;;;;;;;;;IAK5C,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,qBACE,6LAAC,mIAAA,CAAA,UAAI;YAAC,WAAU;;8BACd,6LAAC;oBAAG,WAAU;;sCACZ,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;4BAAsB,MAAM;;;;;;wBAAM;;;;;;;8BAGxD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA8B;;;;;;sCAC7C,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC,mIAAA,CAAA,UAAI;QAAC,WAAU;QAAM,QAAQ;;0BAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gBACR,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;wBAAsB,MAAM;;;;;;oBAAM;;;;;;;0BAKxD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,6LAAC;wBAAM,WAAU;;0CACf,6LAAC,+MAAA,CAAA,YAAS;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAG1C,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS,IAAM,sBAAsB,UAAU,IAAI;gCACnD,WAAW,CAAC;;gBAEV,EACE,sBAAsB,UAAU,IAAI,GAChC,CAAC,iCAAiC,EAAE,UAAU,KAAK,CAAC,qBAAqB,CAAC,GAC1E,iEACL;cACH,CAAC;gCACD,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,OAAO,MAAM;gCAAM;gCACjC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB,UAAU,IAAI;;;;;;0DAC9C,6LAAC;gDAAK,WAAU;0DAAe,UAAU,IAAI;;;;;;;;;;;;kDAE/C,6LAAC;wCACC,WAAW,CAAC,QAAQ,EAClB,sBAAsB,UAAU,IAAI,GAChC,kBACA,iBACJ;kDAED,UAAU,WAAW;;;;;;;+BA3BnB,UAAU,IAAI;;;;;;;;;;;;;;;;0BAkC3B,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,QAAQ;4BAAI;4BACvB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;gCAAU,MAAM;4BAAS;;;;;;sCAE9D,6LAAC;4BAAE,WAAU;sCAA4B;;;;;;;mBAXrC;;;;2BAeJ,2BACF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,OAAO;oBAAI;;sCAGzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;;;;;;gDAAgD;;;;;;;sDAGlE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,UAAM;oDACL,SAAS;oDACT,SAAQ;oDACR,MAAK;oDACL,oBAAM,6LAAC,qMAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;oDAClB,UAAU,CAAC,cAAc,sBAAsB;8DAE9C,oBAAoB,UAAU;;;;;;8DAEjC,6LAAC,qIAAA,CAAA,UAAM;oDACL,SAAS;oDACT,SAAQ;oDACR,MAAK;oDACL,oBAAM,6LAAC,mNAAA,CAAA,YAAS;wDAAC,MAAM;;;;;;oDACvB,SAAS;8DACV;;;;;;;;;;;;;;;;;;8CAML,6LAAC;oCAAI,WAAU;8CACZ,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC/B,6LAAC,yIAAA,CAAA,UAAU;4CAET,QAAQ;4CACR,SAAQ;4CACR,MAAK;4CACL,OAAO,MAAM;2CAJR;;;;;;;;;;;;;;;;sCAWb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;sEACd,6LAAC;4DAAK,WAAU;;;;;;wDAA+C;;;;;;;8DAGjE,6LAAC;oDAAK,WAAU;;wDACb,WAAW,UAAU;wDAAC;;;;;;;;;;;;;sDAG3B,6LAAC;4CAAc,YAAY,WAAW,UAAU;;;;;;sDAChD,6LAAC;4CAAE,WAAU;;gDAA6B;gDAC9B,KAAK,MAAM;gDAAC;;;;;;;;;;;;;8CAI1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAqB;;;;;;gDAAS;;;;;;;sDAGhD,6LAAC;4CAAE,WAAU;sDACV,WAAW,SAAS;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;;;;;wDACf,KAAK,MAAM;wDAAC;;;;;;;8DAEf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;;;;;wDAA+C;;;;;;;8DAGjE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;;;;;wDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;0DACd,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;kDAGrC,6LAAC,qIAAA,CAAA,UAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,MACE,uBACE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;mEAE3B,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;wCAGhB,WAAW,SAAS,oCAAoC;kDAEvD,SAAS,YAAY,WAAW,OAAO,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;wBAMnD,mCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCAAM;;;;;;;8CAGvC,6LAAC;oCAAE,WAAU;8CAA+B;;;;;;8CAI5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC,qIAAA,CAAA,UAAM;4CACL,SAAS;4CACT,SAAQ;4CACR,MAAK;4CACL,UAAU,CAAC,aAAa,IAAI;sDAC7B;;;;;;;;;;;;;;;;;;sCAQP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,6LAAC,qIAAA,CAAA,UAAM;gCACL,SAAS,IAAM,mBAAmB,CAAC;gCACnC,SAAQ;gCACR,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,aAAU;oCAAC,MAAM;;;;;;;oCAEvB,kBAAkB,SAAS;oCAAO;;;;;;;;;;;;sCAKvC,6LAAC,4LAAA,CAAA,kBAAe;sCACb,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;oCAI1C,YAAY,MAAM,GAAG,kBACpB,6LAAC;wCAAI,WAAU;kDACZ,YACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc,EAClD,GAAG,CAAC,CAAC,MAAM,sBACV,6LAAC;gDAEC,WAAW,CAAC,sBAAsB,EAChC,KAAK,aAAa,KAAK,oBACnB,gCACA,4BACJ;;kEAEF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAA4B;oEACxC,QAAQ;oEAAE;oEAAE,KAAK,aAAa;;;;;;;0EAElC,6LAAC;gEAAK,WAAU;;oEACb,KAAK,gBAAgB;oEAAC;;;;;;;;;;;;;kEAG3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAGhC,6LAAC;wEAAK,WAAU;kFACb,KAAK,cAAc;;;;;;;;;;;;0EAGxB,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;;4EACb,KAAK,SAAS;4EAAC;;;;;;;;;;;;;0EAGpB,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;;4EACb,KAAK,QAAQ;4EAAC;;;;;;;;;;;;;;;;;;;;+CAjChB,KAAK,aAAa;;;;;;;;;6DAyC/B,6LAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;wBAU7D,YAAY,MAAM,GAAG,mBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;8CACZ,YAAY,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC5B,6LAAC;4CAEC,WAAU;;8DAEV,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAe,KAAK,SAAS;;;;;;sEAC7C,6LAAC;4DAAK,WAAU;sEAAsB,KAAK,IAAI;;;;;;;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,KAAK,gBAAgB,CAAC,IAAI,CAAC;;;;;;wDAE7B,KAAK,OAAO,KAAK,2BAChB,6LAAC;4DACC,WAAW,CAAC,oCAAoC,EAC9C,KAAK,OAAO,IAAI,IACZ,gCACA,KAAK,OAAO,IAAI,IAChB,kCACA,2BACJ;;gEAED,KAAK,OAAO;gEAAC;;;;;;;;;;;;;;2CArBf,KAAK,EAAE;;;;;;;;;;;;;;;;;mBAnQlB;;;;2BAkSJ;;;;;;;;;;;;AAIZ;GA3iBwB;KAAA", "debugId": null}}, {"offset": {"line": 3069, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/HistoricalData.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { LotteryResult } from '@/types/lottery';\n\ninterface HistoricalDataProps {\n  data: LotteryResult[];\n}\n\nexport default function HistoricalData({ data }: HistoricalDataProps) {\n  const [currentPage, setCurrentPage] = useState(1);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortBy, setSortBy] = useState<'date' | 'id'>('date');\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');\n  \n  const itemsPerPage = 10;\n\n  // Filter and sort data\n  const filteredData = data.filter(result => \n    result.id.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    result.date.includes(searchTerm) ||\n    result.result.some(num => num.toString().includes(searchTerm))\n  );\n\n  const sortedData = [...filteredData].sort((a, b) => {\n    let comparison = 0;\n    \n    if (sortBy === 'date') {\n      comparison = new Date(a.date).getTime() - new Date(b.date).getTime();\n    } else {\n      comparison = a.id.localeCompare(b.id);\n    }\n    \n    return sortOrder === 'asc' ? comparison : -comparison;\n  });\n\n  // Pagination\n  const totalPages = Math.ceil(sortedData.length / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const paginatedData = sortedData.slice(startIndex, startIndex + itemsPerPage);\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const NumberBall = ({ number, size = 'sm' }: { number: number; size?: 'sm' | 'xs' }) => (\n    <div\n      className={`\n        rounded-full bg-blue-500 text-white font-bold flex items-center justify-center\n        ${size === 'sm' ? 'w-8 h-8 text-sm' : 'w-6 h-6 text-xs'}\n      `}\n    >\n      {number}\n    </div>\n  );\n\n  const handleSort = (field: 'date' | 'id') => {\n    if (sortBy === field) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(field);\n      setSortOrder('desc');\n    }\n    setCurrentPage(1);\n  };\n\n  if (!data.length) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <h2 className=\"text-xl font-bold text-gray-800 mb-4\">Historical Data</h2>\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-400 text-4xl mb-2\">📋</div>\n          <p className=\"text-gray-500\">No historical data available</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h2 className=\"text-xl font-bold text-gray-800 mb-4 flex items-center\">\n        <span className=\"mr-2\">📋</span>\n        Historical Data\n      </h2>\n\n      {/* Controls */}\n      <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n        <div className=\"flex-1\">\n          <input\n            type=\"text\"\n            placeholder=\"Search by draw ID, date, or numbers...\"\n            value={searchTerm}\n            onChange={(e) => {\n              setSearchTerm(e.target.value);\n              setCurrentPage(1);\n            }}\n            className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n        <div className=\"flex gap-2\">\n          <button\n            onClick={() => handleSort('date')}\n            className={`px-3 py-2 rounded text-sm transition-colors flex items-center ${\n              sortBy === 'date'\n                ? 'bg-blue-500 text-white'\n                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n          >\n            Date {sortBy === 'date' && (sortOrder === 'asc' ? '↑' : '↓')}\n          </button>\n          <button\n            onClick={() => handleSort('id')}\n            className={`px-3 py-2 rounded text-sm transition-colors flex items-center ${\n              sortBy === 'id'\n                ? 'bg-blue-500 text-white'\n                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n          >\n            Draw ID {sortBy === 'id' && (sortOrder === 'asc' ? '↑' : '↓')}\n          </button>\n        </div>\n      </div>\n\n      {/* Results count */}\n      <div className=\"mb-4 text-sm text-gray-600\">\n        Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, sortedData.length)} of {sortedData.length} results\n        {searchTerm && ` (filtered from ${data.length} total)`}\n      </div>\n\n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"w-full\">\n          <thead>\n            <tr className=\"border-b border-gray-200\">\n              <th className=\"text-left py-3 px-2 font-semibold text-gray-700\">Draw ID</th>\n              <th className=\"text-left py-3 px-2 font-semibold text-gray-700\">Date</th>\n              <th className=\"text-left py-3 px-2 font-semibold text-gray-700\">Numbers</th>\n              <th className=\"text-left py-3 px-2 font-semibold text-gray-700\">Power</th>\n            </tr>\n          </thead>\n          <tbody>\n            {paginatedData.map((result, index) => (\n              <tr\n                key={result.id}\n                className={`border-b border-gray-100 hover:bg-gray-50 transition-colors ${\n                  index % 2 === 0 ? 'bg-white' : 'bg-gray-50'\n                }`}\n              >\n                <td className=\"py-3 px-2 font-mono text-sm\">{result.id}</td>\n                <td className=\"py-3 px-2 text-sm\">{formatDate(result.date)}</td>\n                <td className=\"py-3 px-2\">\n                  <div className=\"flex space-x-1\">\n                    {result.result.map((number, numIndex) => (\n                      <NumberBall key={numIndex} number={number} size=\"xs\" />\n                    ))}\n                  </div>\n                </td>\n                <td className=\"py-3 px-2\">\n                  {result.powerNumber && (\n                    <div className=\"w-6 h-6 rounded-full bg-red-500 text-white font-bold flex items-center justify-center text-xs\">\n                      {result.powerNumber}\n                    </div>\n                  )}\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"flex justify-center items-center space-x-2 mt-6\">\n          <button\n            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\n            disabled={currentPage === 1}\n            className=\"px-3 py-1 rounded text-sm bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Previous\n          </button>\n          \n          <div className=\"flex space-x-1\">\n            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              \n              return (\n                <button\n                  key={pageNum}\n                  onClick={() => setCurrentPage(pageNum)}\n                  className={`px-3 py-1 rounded text-sm transition-colors ${\n                    currentPage === pageNum\n                      ? 'bg-blue-500 text-white'\n                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                  }`}\n                >\n                  {pageNum}\n                </button>\n              );\n            })}\n          </div>\n          \n          <button\n            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}\n            disabled={currentPage === totalPages}\n            className=\"px-3 py-1 rounded text-sm bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Next\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,eAAe,EAAE,IAAI,EAAuB;;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,MAAM,eAAe;IAErB,uBAAuB;IACvB,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,SAC/B,OAAO,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,OAAO,IAAI,CAAC,QAAQ,CAAC,eACrB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,GAAG,QAAQ,CAAC;IAGpD,MAAM,aAAa;WAAI;KAAa,CAAC,IAAI,CAAC,CAAC,GAAG;QAC5C,IAAI,aAAa;QAEjB,IAAI,WAAW,QAAQ;YACrB,aAAa,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;QACpE,OAAO;YACL,aAAa,EAAE,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE;QACtC;QAEA,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEA,aAAa;IACb,MAAM,aAAa,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;IACjD,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,gBAAgB,WAAW,KAAK,CAAC,YAAY,aAAa;IAEhE,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,IAAI,EAA0C,iBACjF,6LAAC;YACC,WAAW,CAAC;;QAEV,EAAE,SAAS,OAAO,oBAAoB,kBAAkB;MAC1D,CAAC;sBAEA;;;;;;IAIL,MAAM,aAAa,CAAC;QAClB,IAAI,WAAW,OAAO;YACpB,aAAa,cAAc,QAAQ,SAAS;QAC9C,OAAO;YACL,UAAU;YACV,aAAa;QACf;QACA,eAAe;IACjB;IAEA,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAuC;;;;;;8BACrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA8B;;;;;;sCAC7C,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC;wBAAK,WAAU;kCAAO;;;;;;oBAAS;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC;gCACT,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC5B,eAAe;4BACjB;4BACA,WAAU;;;;;;;;;;;kCAGd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,WAAW;gCAC1B,WAAW,CAAC,8DAA8D,EACxE,WAAW,SACP,2BACA,+CACJ;;oCACH;oCACO,WAAW,UAAU,CAAC,cAAc,QAAQ,MAAM,GAAG;;;;;;;0CAE7D,6LAAC;gCACC,SAAS,IAAM,WAAW;gCAC1B,WAAW,CAAC,8DAA8D,EACxE,WAAW,OACP,2BACA,+CACJ;;oCACH;oCACU,WAAW,QAAQ,CAAC,cAAc,QAAQ,MAAM,GAAG;;;;;;;;;;;;;;;;;;;0BAMlE,6LAAC;gBAAI,WAAU;;oBAA6B;oBACjC,aAAa;oBAAE;oBAAE,KAAK,GAAG,CAAC,aAAa,cAAc,WAAW,MAAM;oBAAE;oBAAK,WAAW,MAAM;oBAAC;oBACvG,cAAc,CAAC,gBAAgB,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;;;;;;;0BAIxD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;sCACC,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;;;;;;;;;;;;sCAGpE,6LAAC;sCACE,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,6LAAC;oCAEC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,IAAI,aAAa,cAC/B;;sDAEF,6LAAC;4CAAG,WAAU;sDAA+B,OAAO,EAAE;;;;;;sDACtD,6LAAC;4CAAG,WAAU;sDAAqB,WAAW,OAAO,IAAI;;;;;;sDACzD,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;0DACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,yBAC1B,6LAAC;wDAA0B,QAAQ;wDAAQ,MAAK;uDAA/B;;;;;;;;;;;;;;;sDAIvB,6LAAC;4CAAG,WAAU;sDACX,OAAO,WAAW,kBACjB,6LAAC;gDAAI,WAAU;0DACZ,OAAO,WAAW;;;;;;;;;;;;mCAjBpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;YA4BvB,aAAa,mBACZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wBACxD,UAAU,gBAAgB;wBAC1B,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;wBAAY,GAAG,CAAC,GAAG;4BACnD,IAAI;4BACJ,IAAI,cAAc,GAAG;gCACnB,UAAU,IAAI;4BAChB,OAAO,IAAI,eAAe,GAAG;gCAC3B,UAAU,IAAI;4BAChB,OAAO,IAAI,eAAe,aAAa,GAAG;gCACxC,UAAU,aAAa,IAAI;4BAC7B,OAAO;gCACL,UAAU,cAAc,IAAI;4BAC9B;4BAEA,qBACE,6LAAC;gCAEC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,4CAA4C,EACtD,gBAAgB,UACZ,2BACA,+CACJ;0CAED;+BARI;;;;;wBAWX;;;;;;kCAGF,6LAAC;wBACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,YAAY,cAAc;wBACjE,UAAU,gBAAgB;wBAC1B,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX;GAzNwB;KAAA", "debugId": null}}, {"offset": {"line": 3485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  color?: string;\n  text?: string;\n}\n\nexport default function LoadingSpinner({ \n  size = 'md', \n  color = 'blue-600',\n  text = 'Loading...'\n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-8 h-8',\n    md: 'w-12 h-12',\n    lg: 'w-16 h-16'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg'\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center space-y-4\">\n      <motion.div\n        className={`${sizeClasses[size]} border-4 border-gray-200 border-t-${color} rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n      />\n      <motion.p \n        className={`${textSizeClasses[size]} text-gray-600 font-medium`}\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.2 }}\n      >\n        {text}\n      </motion.p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EACrC,OAAO,IAAI,EACX,QAAQ,UAAU,EAClB,OAAO,YAAY,EACC;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mCAAmC,EAAE,MAAM,aAAa,CAAC;gBACzF,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS;;;;;;0BAE9D,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,WAAW,GAAG,eAAe,CAAC,KAAK,CAAC,0BAA0B,CAAC;gBAC/D,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;0BAExB;;;;;;;;;;;;AAIT;KAlCwB", "debugId": null}}, {"offset": {"line": 3558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/services/LotteryConfigService.ts"], "sourcesContent": ["import { LotteryType, LotteryConfig } from '@/types/lottery';\n\nexport class LotteryConfigService {\n  private static instance: LotteryConfigService;\n  private currentLotteryType: LotteryType = 'power655';\n\n  private constructor() {}\n\n  public static getInstance(): LotteryConfigService {\n    if (!LotteryConfigService.instance) {\n      LotteryConfigService.instance = new LotteryConfigService();\n    }\n    return LotteryConfigService.instance;\n  }\n\n  public getLotteryConfigs(): LotteryConfig[] {\n    return [\n      {\n        type: 'power655',\n        name: 'Power 6/55',\n        description: 'Pick 6 numbers from 1-55 + 1 power number',\n        maxNumber: 55,\n        numbersCount: 6,\n        hasPowerNumber: true,\n        icon: '⚡',\n        color: 'from-blue-500 to-indigo-600'\n      },\n      {\n        type: 'mega645',\n        name: 'Mega 6/45',\n        description: 'Pick 6 numbers from 1-45',\n        maxNumber: 45,\n        numbersCount: 6,\n        hasPowerNumber: false,\n        icon: '💎',\n        color: 'from-purple-500 to-pink-600'\n      }\n    ];\n  }\n\n  public getCurrentConfig(): LotteryConfig {\n    const configs = this.getLotteryConfigs();\n    return configs.find(config => config.type === this.currentLotteryType) || configs[0];\n  }\n\n  public setCurrentLotteryType(type: LotteryType): void {\n    this.currentLotteryType = type;\n  }\n\n  public getCurrentLotteryType(): LotteryType {\n    return this.currentLotteryType;\n  }\n\n  // Vietlott Power 6/55 Rules\n  public getPower655Rules(): string[] {\n    return [\n      'Select 6 different numbers from 1 to 55',\n      'Select 1 power number from 1 to 55',\n      'No duplicate numbers allowed in main selection',\n      'Power number can be same as main numbers',\n      'Draws held twice weekly (Tuesday & Friday)',\n      'Minimum jackpot: 12 billion VND'\n    ];\n  }\n\n  // Vietlott Mega 6/45 Rules\n  public getMega645Rules(): string[] {\n    return [\n      'Select 6 different numbers from 1 to 45',\n      'No power number required',\n      'No duplicate numbers allowed',\n      'Draws held twice weekly (Wednesday & Saturday)',\n      'Minimum jackpot: 15 billion VND',\n      'Better odds than Power 6/55'\n    ];\n  }\n\n  public getRulesForCurrentLottery(): string[] {\n    return this.currentLotteryType === 'power655' \n      ? this.getPower655Rules() \n      : this.getMega645Rules();\n  }\n\n  // Calculate odds for each lottery type\n  public getOdds(lotteryType: LotteryType): { jackpot: string; match5: string; match4: string; match3: string } {\n    if (lotteryType === 'power655') {\n      return {\n        jackpot: '1 in 50,063,860',\n        match5: '1 in 906,615',\n        match4: '1 in 19,068',\n        match3: '1 in 645'\n      };\n    } else {\n      return {\n        jackpot: '1 in 8,145,060',\n        match5: '1 in 34,808',\n        match4: '1 in 733',\n        match3: '1 in 45'\n      };\n    }\n  }\n\n  // Prize structure information\n  public getPrizeStructure(lotteryType: LotteryType): { level: string; condition: string; prize: string }[] {\n    if (lotteryType === 'power655') {\n      return [\n        { level: 'Jackpot', condition: '6 numbers + Power', prize: 'Jackpot (min 12B VND)' },\n        { level: 'Prize 1', condition: '6 numbers', prize: '40M - 60M VND' },\n        { level: 'Prize 2', condition: '5 numbers + Power', prize: '10M - 20M VND' },\n        { level: 'Prize 3', condition: '5 numbers', prize: '500K - 1M VND' },\n        { level: 'Prize 4', condition: '4 numbers + Power', prize: '200K - 400K VND' },\n        { level: 'Prize 5', condition: '4 numbers', prize: '50K - 100K VND' },\n        { level: 'Prize 6', condition: '3 numbers + Power', prize: '20K - 50K VND' }\n      ];\n    } else {\n      return [\n        { level: 'Jackpot', condition: '6 numbers', prize: 'Jackpot (min 15B VND)' },\n        { level: 'Prize 1', condition: '5 numbers', prize: '10M - 30M VND' },\n        { level: 'Prize 2', condition: '4 numbers', prize: '300K - 800K VND' },\n        { level: 'Prize 3', condition: '3 numbers', prize: '30K - 80K VND' }\n      ];\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAe,SAA+B;IACtC,qBAAkC,WAAW;IAErD,aAAsB,CAAC;IAEvB,OAAc,cAAoC;QAChD,IAAI,CAAC,qBAAqB,QAAQ,EAAE;YAClC,qBAAqB,QAAQ,GAAG,IAAI;QACtC;QACA,OAAO,qBAAqB,QAAQ;IACtC;IAEO,oBAAqC;QAC1C,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,WAAW;gBACX,cAAc;gBACd,gBAAgB;gBAChB,MAAM;gBACN,OAAO;YACT;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,WAAW;gBACX,cAAc;gBACd,gBAAgB;gBAChB,MAAM;gBACN,OAAO;YACT;SACD;IACH;IAEO,mBAAkC;QACvC,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK,IAAI,CAAC,kBAAkB,KAAK,OAAO,CAAC,EAAE;IACtF;IAEO,sBAAsB,IAAiB,EAAQ;QACpD,IAAI,CAAC,kBAAkB,GAAG;IAC5B;IAEO,wBAAqC;QAC1C,OAAO,IAAI,CAAC,kBAAkB;IAChC;IAEA,4BAA4B;IACrB,mBAA6B;QAClC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,2BAA2B;IACpB,kBAA4B;QACjC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEO,4BAAsC;QAC3C,OAAO,IAAI,CAAC,kBAAkB,KAAK,aAC/B,IAAI,CAAC,gBAAgB,KACrB,IAAI,CAAC,eAAe;IAC1B;IAEA,uCAAuC;IAChC,QAAQ,WAAwB,EAAuE;QAC5G,IAAI,gBAAgB,YAAY;YAC9B,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,QAAQ;YACV;QACF,OAAO;YACL,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,QAAQ;YACV;QACF;IACF;IAEA,8BAA8B;IACvB,kBAAkB,WAAwB,EAAyD;QACxG,IAAI,gBAAgB,YAAY;YAC9B,OAAO;gBACL;oBAAE,OAAO;oBAAW,WAAW;oBAAqB,OAAO;gBAAwB;gBACnF;oBAAE,OAAO;oBAAW,WAAW;oBAAa,OAAO;gBAAgB;gBACnE;oBAAE,OAAO;oBAAW,WAAW;oBAAqB,OAAO;gBAAgB;gBAC3E;oBAAE,OAAO;oBAAW,WAAW;oBAAa,OAAO;gBAAgB;gBACnE;oBAAE,OAAO;oBAAW,WAAW;oBAAqB,OAAO;gBAAkB;gBAC7E;oBAAE,OAAO;oBAAW,WAAW;oBAAa,OAAO;gBAAiB;gBACpE;oBAAE,OAAO;oBAAW,WAAW;oBAAqB,OAAO;gBAAgB;aAC5E;QACH,OAAO;YACL,OAAO;gBACL;oBAAE,OAAO;oBAAW,WAAW;oBAAa,OAAO;gBAAwB;gBAC3E;oBAAE,OAAO;oBAAW,WAAW;oBAAa,OAAO;gBAAgB;gBACnE;oBAAE,OAAO;oBAAW,WAAW;oBAAa,OAAO;gBAAkB;gBACrE;oBAAE,OAAO;oBAAW,WAAW;oBAAa,OAAO;gBAAgB;aACpE;QACH;IACF;AACF", "debugId": null}}, {"offset": {"line": 3723, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Sparkles, TrendingUp, Database, RefreshCw } from \"lucide-react\";\nimport LotteryTypeSelector from \"@/components/LotteryTypeSelector\";\nimport LatestResults from \"@/components/LatestResults\";\nimport StatisticsChart from \"@/components/StatisticsChart\";\nimport NumberSuggestion from \"@/components/NumberSuggestion\";\nimport HistoricalData from \"@/components/HistoricalData\";\nimport LoadingSpinner from \"@/components/ui/LoadingSpinner\";\nimport Button from \"@/components/ui/Button\";\nimport { LotteryResult, LotteryType } from \"@/types/lottery\";\nimport { LotteryDataService } from \"@/services/LotteryDataService\";\nimport { LotteryConfigService } from \"@/services/LotteryConfigService\";\n\nexport default function Home() {\n  const [lotteryData, setLotteryData] = useState<LotteryResult[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [refreshing, setRefreshing] = useState(false);\n  const [currentLotteryType, setCurrentLotteryType] =\n    useState<LotteryType>(\"power655\");\n\n  const dataService = LotteryDataService.getInstance();\n  const configService = LotteryConfigService.getInstance();\n\n  const fetchData = async (forceRefresh = false, lotteryType?: LotteryType) => {\n    try {\n      setLoading(!forceRefresh);\n      setRefreshing(forceRefresh);\n      setError(null);\n\n      if (forceRefresh) {\n        dataService.clearCache();\n      }\n\n      const typeToFetch = lotteryType || currentLotteryType;\n      const response = await fetch(`/api/lottery-data?type=${typeToFetch}`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      setLotteryData(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"An error occurred\");\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  const handleLotteryTypeChange = (newType: LotteryType) => {\n    setCurrentLotteryType(newType);\n    configService.setCurrentLotteryType(newType);\n    fetchData(true, newType);\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" text=\"Loading lottery data...\" />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 via-white to-pink-50 flex items-center justify-center\">\n        <motion.div\n          className=\"text-center max-w-md mx-auto p-8\"\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5 }}\n        >\n          <motion.div\n            className=\"text-red-500 text-6xl mb-4\"\n            animate={{ rotate: [0, -10, 10, -10, 0] }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            ⚠️\n          </motion.div>\n          <h1 className=\"text-2xl font-bold text-gray-800 mb-2\">\n            Error Loading Data\n          </h1>\n          <p className=\"text-gray-600 mb-6\">{error}</p>\n          <Button\n            onClick={() => fetchData(true)}\n            variant=\"primary\"\n            icon={<RefreshCw size={16} />}\n            loading={refreshing}\n          >\n            Retry\n          </Button>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\n      {/* Header */}\n      <motion.header\n        className=\"bg-white/80 backdrop-blur-sm shadow-lg border-b border-white/20\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <motion.h1\n                className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent flex items-center\"\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: 0.2 }}\n              >\n                <Sparkles className=\"mr-3 text-blue-600\" size={32} />\n                Vietlott Analyzer\n              </motion.h1>\n              <motion.p\n                className=\"text-gray-600 mt-2 text-lg\"\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                AI-powered analysis for Power 6/55 & Mega 6/45\n              </motion.p>\n            </div>\n            <motion.div\n              className=\"flex items-center space-x-4\"\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.4 }}\n            >\n              <div className=\"text-right\">\n                <p className=\"text-sm text-gray-500\">Total Draws</p>\n                <p className=\"text-2xl font-bold text-blue-600\">\n                  {lotteryData.length}\n                </p>\n              </div>\n              <Button\n                onClick={() => fetchData(true)}\n                variant=\"secondary\"\n                size=\"sm\"\n                icon={<RefreshCw size={16} />}\n                loading={refreshing}\n              >\n                Refresh Data\n              </Button>\n            </motion.div>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <motion.div\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n        >\n          <LatestResults data={lotteryData} />\n          <NumberSuggestion data={lotteryData} />\n        </motion.div>\n\n        <motion.div\n          className=\"mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.7 }}\n        >\n          <StatisticsChart data={lotteryData} />\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.9 }}\n        >\n          <HistoricalData data={lotteryData} />\n        </motion.div>\n      </main>\n\n      {/* Footer */}\n      <motion.footer\n        className=\"bg-white/60 backdrop-blur-sm border-t border-white/20 mt-16\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 1.1 }}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"text-center text-gray-600\">\n            <p className=\"text-sm\">\n              ⚠️ This application is for educational and entertainment purposes\n              only.\n            </p>\n            <p className=\"text-xs mt-1\">\n              Lottery numbers are random. Past results do not guarantee future\n              outcomes. Please gamble responsibly.\n            </p>\n          </div>\n        </div>\n      </motion.footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAdA;;;;;;;;;;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAC/C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAExB,MAAM,cAAc,wIAAA,CAAA,qBAAkB,CAAC,WAAW;IAClD,MAAM,gBAAgB,0IAAA,CAAA,uBAAoB,CAAC,WAAW;IAEtD,MAAM,YAAY,OAAO,eAAe,KAAK,EAAE;QAC7C,IAAI;YACF,WAAW,CAAC;YACZ,cAAc;YACd,SAAS;YAET,IAAI,cAAc;gBAChB,YAAY,UAAU;YACxB;YAEA,MAAM,cAAc,eAAe;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,aAAa;YACpE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;YACX,cAAc;QAChB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,sBAAsB;QACtB,cAAc,qBAAqB,CAAC;QACpC,UAAU,MAAM;IAClB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6IAAA,CAAA,UAAc;gBAAC,MAAK;gBAAK,MAAK;;;;;;;;;;;IAGrC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,QAAQ;gCAAC;gCAAG,CAAC;gCAAI;gCAAI,CAAC;gCAAI;6BAAE;wBAAC;wBACxC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCACzC;;;;;;kCAGD,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,6LAAC,qIAAA,CAAA,UAAM;wBACL,SAAS,IAAM,UAAU;wBACzB,SAAQ;wBACR,oBAAM,6LAAC,mNAAA,CAAA,YAAS;4BAAC,MAAM;;;;;;wBACvB,SAAS;kCACV;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAqB,MAAM;;;;;;4CAAM;;;;;;;kDAGvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAC1B;;;;;;;;;;;;0CAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DACV,YAAY,MAAM;;;;;;;;;;;;kDAGvB,6LAAC,qIAAA,CAAA,UAAM;wCACL,SAAS,IAAM,UAAU;wCACzB,SAAQ;wCACR,MAAK;wCACL,oBAAM,6LAAC,mNAAA,CAAA,YAAS;4CAAC,MAAM;;;;;;wCACvB,SAAS;kDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6LAAC,sIAAA,CAAA,UAAa;gCAAC,MAAM;;;;;;0CACrB,6LAAC,yIAAA,CAAA,UAAgB;gCAAC,MAAM;;;;;;;;;;;;kCAG1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,6LAAC,wIAAA,CAAA,UAAe;4BAAC,MAAM;;;;;;;;;;;kCAGzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,6LAAC,uIAAA,CAAA,UAAc;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAK1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CAIvB,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;GAnMwB;KAAA", "debugId": null}}]}