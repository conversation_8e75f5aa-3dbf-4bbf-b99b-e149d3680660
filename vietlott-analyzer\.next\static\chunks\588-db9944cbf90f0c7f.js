"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[588],{299:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]])},463:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},760:(t,e,i)=>{i.d(e,{N:()=>v});var n=i(5155),r=i(2115),s=i(869),o=i(2885),a=i(7494),l=i(845),u=i(7351),h=i(1508);class c extends r.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,u.s)(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(t){let{children:e,isPresent:i,anchorX:s}=t,o=(0,r.useId)(),a=(0,r.useRef)(null),l=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,r.useContext)(h.Q);return(0,r.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:r,right:h}=l.current;if(i||!a.current||!t||!e)return;a.current.dataset.motionPopId=o;let c=document.createElement("style");return u&&(c.nonce=u),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(r):"right: ".concat(h),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[i]),(0,n.jsx)(c,{isPresent:i,childRef:a,sizeRef:l,children:r.cloneElement(e,{ref:a})})}let f=t=>{let{children:e,initial:i,isPresent:s,onExitComplete:a,custom:u,presenceAffectsLayout:h,mode:c,anchorX:f}=t,m=(0,o.M)(p),g=(0,r.useId)(),y=!0,v=(0,r.useMemo)(()=>(y=!1,{id:g,initial:i,isPresent:s,custom:u,onExitComplete:t=>{for(let e of(m.set(t,!0),m.values()))if(!e)return;a&&a()},register:t=>(m.set(t,!1),()=>m.delete(t))}),[s,m,a]);return h&&y&&(v={...v}),(0,r.useMemo)(()=>{m.forEach((t,e)=>m.set(e,!1))},[s]),r.useEffect(()=>{s||m.size||!a||a()},[s]),"popLayout"===c&&(e=(0,n.jsx)(d,{isPresent:s,anchorX:f,children:e})),(0,n.jsx)(l.t.Provider,{value:v,children:e})};function p(){return new Map}var m=i(2082);let g=t=>t.key||"";function y(t){let e=[];return r.Children.forEach(t,t=>{(0,r.isValidElement)(t)&&e.push(t)}),e}let v=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:u,presenceAffectsLayout:h=!0,mode:c="sync",propagate:d=!1,anchorX:p="left"}=t,[v,x]=(0,m.xQ)(d),b=(0,r.useMemo)(()=>y(e),[e]),w=d&&!v?[]:b.map(g),T=(0,r.useRef)(!0),M=(0,r.useRef)(b),P=(0,o.M)(()=>new Map),[k,S]=(0,r.useState)(b),[A,E]=(0,r.useState)(b);(0,a.E)(()=>{T.current=!1,M.current=b;for(let t=0;t<A.length;t++){let e=g(A[t]);w.includes(e)?P.delete(e):!0!==P.get(e)&&P.set(e,!1)}},[A,w.length,w.join("-")]);let C=[];if(b!==k){let t=[...b];for(let e=0;e<A.length;e++){let i=A[e],n=g(i);w.includes(n)||(t.splice(e,0,i),C.push(i))}return"wait"===c&&C.length&&(t=C),E(y(t)),S(b),null}let{forceRender:V}=(0,r.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:A.map(t=>{let e=g(t),r=(!d||!!v)&&(b===A||w.includes(e));return(0,n.jsx)(f,{isPresent:r,initial:(!T.current||!!l)&&void 0,custom:i,presenceAffectsLayout:h,mode:c,onExitComplete:r?void 0:()=>{if(!P.has(e))return;P.set(e,!0);let t=!0;P.forEach(e=>{e||(t=!1)}),t&&(null==V||V(),E(M.current),d&&(null==x||x()),u&&u())},anchorX:p,children:t},e)})})}},845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(2115).createContext)({})},1284:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1351:(t,e,i)=>{let n;function r(t){return t+.5|0}i.d(e,{$:()=>ec,A:()=>tO,B:()=>tj,C:()=>eu,D:()=>tP,E:()=>eT,F:()=>Y,G:()=>eQ,H:()=>th,I:()=>eY,J:()=>e1,K:()=>e0,L:()=>tW,M:()=>e$,N:()=>ty,O:()=>_,P:()=>ts,Q:()=>$,R:()=>ek,S:()=>tC,T:()=>to,U:()=>tT,V:()=>en,W:()=>tV,X:()=>es,Y:()=>eh,Z:()=>ep,_:()=>t_,a:()=>eP,a0:()=>eM,a1:()=>t$,a2:()=>tY,a3:()=>t8,a4:()=>K,a5:()=>tt,a6:()=>t6,a7:()=>ti,a8:()=>function t(e,i,n,r){return new Proxy({_cacheable:!1,_proxy:e,_context:i,_subProxy:n,_stack:new Set,_descriptors:eE(e,r),setContext:i=>t(e,i,n,r),override:s=>t(e.override(s),i,n,r)},{deleteProperty:(t,i)=>(delete t[i],delete e[i],!0),get:(e,i,n)=>eR(e,i,()=>(function(e,i,n){let{_proxy:r,_context:s,_subProxy:o,_descriptors:a}=e,l=r[i];return ti(l)&&a.isScriptable(i)&&(l=function(t,e,i,n){let{_proxy:r,_context:s,_subProxy:o,_stack:a}=i;if(a.has(t))throw Error("Recursion detected: "+Array.from(a).join("->")+"->"+t);a.add(t);let l=e(s,o||n);return a.delete(t),eV(t,l)&&(l=ej(r._scopes,r,t,l)),l}(i,l,e,n)),F(l)&&l.length&&(l=function(e,i,n,r){let{_proxy:s,_context:o,_subProxy:a,_descriptors:l}=n;if(void 0!==o.index&&r(e))return i[o.index%i.length];if(B(i[0])){let n=i,r=s._scopes.filter(t=>t!==n);for(let u of(i=[],n)){let n=ej(r,s,e,u);i.push(t(n,o,a&&a[e],l))}}return i}(i,l,e,a.isIndexable)),eV(i,l)&&(l=t(l,s,o&&o[i],a)),l})(e,i,n)),getOwnPropertyDescriptor:(t,i)=>t._descriptors.allKeys?Reflect.has(e,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,i),getPrototypeOf:()=>Reflect.getPrototypeOf(e),has:(t,i)=>Reflect.has(e,i),ownKeys:()=>Reflect.ownKeys(e),set:(t,i,n)=>(e[i]=n,delete t[i],!0)})},a9:()=>eA,aA:()=>e3,aB:()=>e4,aC:()=>tH,aD:()=>e8,aE:()=>el,aF:()=>tk,aG:()=>j,aH:()=>tb,aI:()=>tg,aJ:()=>tx,aK:()=>tm,aL:()=>tM,aM:()=>t4,aN:()=>tf,aO:()=>er,aP:()=>tL,aQ:()=>tD,aa:()=>eE,ab:()=>Z,ac:()=>O,ad:()=>tU,ae:()=>eJ,af:()=>eo,ag:()=>tn,ah:()=>ia,ai:()=>H,aj:()=>tr,ak:()=>tR,al:()=>ex,am:()=>eU,an:()=>ii,ao:()=>ie,ap:()=>e5,aq:()=>e9,ar:()=>e2,as:()=>ed,at:()=>ef,au:()=>ea,av:()=>em,aw:()=>eb,ax:()=>ew,ay:()=>it,az:()=>tA,b:()=>F,c:()=>tJ,d:()=>ei,e:()=>tG,f:()=>J,g:()=>I,h:()=>te,i:()=>B,j:()=>eS,k:()=>L,l:()=>tB,m:()=>W,n:()=>U,o:()=>t9,p:()=>tE,q:()=>tz,r:()=>tN,s:()=>tp,t:()=>tw,u:()=>tI,v:()=>N,w:()=>tX,x:()=>tv,y:()=>eB,z:()=>eZ});let s=(t,e,i)=>Math.max(Math.min(t,i),e);function o(t){return s(r(2.55*t),0,255)}function a(t){return s(r(255*t),0,255)}function l(t){return s(r(t/2.55)/100,0,1)}function u(t){return s(r(100*t),0,100)}let h={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},c=[..."0123456789ABCDEF"],d=t=>c[15&t],f=t=>c[(240&t)>>4]+c[15&t],p=t=>(240&t)>>4==(15&t),m=t=>p(t.r)&&p(t.g)&&p(t.b)&&p(t.a),g=(t,e)=>t<255?e(t):"",y=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function v(t,e,i){let n=e*Math.min(i,1-i),r=(e,r=(e+t/30)%12)=>i-n*Math.max(Math.min(r-3,9-r,1),-1);return[r(0),r(8),r(4)]}function x(t,e,i){let n=(n,r=(n+t/60)%6)=>i-i*e*Math.max(Math.min(r,4-r,1),0);return[n(5),n(3),n(1)]}function b(t,e,i){let n,r=v(t,1,.5);for(e+i>1&&(n=1/(e+i),e*=n,i*=n),n=0;n<3;n++)r[n]*=1-e-i,r[n]+=e;return r}function w(t){let e,i,n,r=t.r/255,s=t.g/255,o=t.b/255,a=Math.max(r,s,o),l=Math.min(r,s,o),u=(a+l)/2;a!==l&&(n=a-l,i=u>.5?n/(2-a-l):n/(a+l),e=60*(e=r===a?(s-o)/n+6*(s<o):s===a?(o-r)/n+2:(r-s)/n+4)+.5);return[0|e,i||0,u]}function T(t,e,i,n){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,n)).map(a)}function M(t){return(t%360+360)%360}let P={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},k={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},S=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,A=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,E=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function C(t,e,i){if(t){let n=w(t);n[e]=Math.max(0,Math.min(n[e]+n[e]*i,0===e?360:1)),t.r=(n=T(v,n,void 0,void 0))[0],t.g=n[1],t.b=n[2]}}function V(t,e){return t?Object.assign(e||{},t):t}function R(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=a(t[3]))):(e=V(t,{r:0,g:0,b:0,a:1})).a=a(e.a),e}class D{constructor(t){let e;if(t instanceof D)return t;let i=typeof t;"object"===i?e=R(t):"string"===i&&(e=function(t){var e,i=t.length;return"#"===t[0]&&(4===i||5===i?e={r:255&17*h[t[1]],g:255&17*h[t[2]],b:255&17*h[t[3]],a:5===i?17*h[t[4]]:255}:(7===i||9===i)&&(e={r:h[t[1]]<<4|h[t[2]],g:h[t[3]]<<4|h[t[4]],b:h[t[5]]<<4|h[t[6]],a:9===i?h[t[7]]<<4|h[t[8]]:255})),e}(t)||function(t){n||((n=function(){let t,e,i,n,r,s={},o=Object.keys(k),a=Object.keys(P);for(t=0;t<o.length;t++){for(e=0,n=r=o[t];e<a.length;e++)i=a[e],r=r.replace(i,P[i]);i=parseInt(k[n],16),s[r]=[i>>16&255,i>>8&255,255&i]}return s}()).transparent=[0,0,0,0]);let e=n[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,i,n,r=S.exec(t),a=255;if(r){if(r[7]!==e){let t=+r[7];a=r[8]?o(t):s(255*t,0,255)}return e=+r[1],i=+r[3],n=+r[5],e=255&(r[2]?o(e):s(e,0,255)),{r:e,g:i=255&(r[4]?o(i):s(i,0,255)),b:n=255&(r[6]?o(n):s(n,0,255)),a:a}}}(t):function(t){let e,i=y.exec(t),n=255;if(!i)return;i[5]!==e&&(n=i[6]?o(+i[5]):a(+i[5]));let r=M(+i[2]),s=i[3]/100,l=i[4]/100;return{r:(e="hwb"===i[1]?T(b,r,s,l):"hsv"===i[1]?T(x,r,s,l):T(v,r,s,l))[0],g:e[1],b:e[2],a:n}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=V(this._rgb);return t&&(t.a=l(t.a)),t}set rgb(t){this._rgb=R(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${l(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;return this._valid?(e=m(t=this._rgb)?d:f,t?"#"+e(t.r)+e(t.g)+e(t.b)+g(t.a,e):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=w(t),i=e[0],n=u(e[1]),r=u(e[2]);return t.a<255?`hsla(${i}, ${n}%, ${r}%, ${l(t.a)})`:`hsl(${i}, ${n}%, ${r}%)`}(this._rgb):void 0}mix(t,e){if(t){let i,n=this.rgb,r=t.rgb,s=e===i?.5:e,o=2*s-1,a=n.a-r.a,l=((o*a==-1?o:(o+a)/(1+o*a))+1)/2;i=1-l,n.r=255&l*n.r+i*r.r+.5,n.g=255&l*n.g+i*r.g+.5,n.b=255&l*n.b+i*r.b+.5,n.a=s*n.a+(1-s)*r.a,this.rgb=n}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){let n=E(l(t.r)),r=E(l(t.g)),s=E(l(t.b));return{r:a(A(n+i*(E(l(e.r))-n))),g:a(A(r+i*(E(l(e.g))-r))),b:a(A(s+i*(E(l(e.b))-s))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new D(this.rgb)}alpha(t){return this._rgb.a=a(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=r(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return C(this._rgb,2,t),this}darken(t){return C(this._rgb,2,-t),this}saturate(t){return C(this._rgb,1,t),this}desaturate(t){return C(this._rgb,1,-t),this}rotate(t){var e,i;return e=this._rgb,(i=w(e))[0]=M(i[0]+t),e.r=(i=T(v,i,void 0,void 0))[0],e.g=i[1],e.b=i[2],this}}function j(){}let O=(()=>{let t=0;return()=>t++})();function L(t){return null==t}function F(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function B(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function I(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function _(t,e){return I(t)?t:e}function N(t,e){return void 0===t?e:t}let W=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,U=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function $(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function Y(t,e,i,n){let r,s,o;if(F(t))if(s=t.length,n)for(r=s-1;r>=0;r--)e.call(i,t[r],r);else for(r=0;r<s;r++)e.call(i,t[r],r);else if(B(t))for(r=0,s=(o=Object.keys(t)).length;r<s;r++)e.call(i,t[o[r]],o[r])}function H(t,e){let i,n,r,s;if(!t||!e||t.length!==e.length)return!1;for(i=0,n=t.length;i<n;++i)if(r=t[i],s=e[i],r.datasetIndex!==s.datasetIndex||r.index!==s.index)return!1;return!0}function z(t){if(F(t))return t.map(z);if(B(t)){let e=Object.create(null),i=Object.keys(t),n=i.length,r=0;for(;r<n;++r)e[i[r]]=z(t[i[r]]);return e}return t}function X(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function q(t,e,i,n){if(!X(t))return;let r=e[t],s=i[t];B(r)&&B(s)?K(r,s,n):e[t]=z(s)}function K(t,e,i){let n,r=F(e)?e:[e],s=r.length;if(!B(t))return t;let o=(i=i||{}).merger||q;for(let e=0;e<s;++e){if(!B(n=r[e]))continue;let s=Object.keys(n);for(let e=0,r=s.length;e<r;++e)o(s[e],t,n,i)}return t}function Z(t,e){return K(t,e,{merger:G})}function G(t,e,i){if(!X(t))return;let n=e[t],r=i[t];B(n)&&B(r)?Z(n,r):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=z(r))}let Q={"":t=>t,x:t=>t.x,y:t=>t.y};function J(t,e){return(Q[e]||(Q[e]=function(t){let e=function(t){let e=t.split("."),i=[],n="";for(let t of e)(n+=t).endsWith("\\")?n=n.slice(0,-1)+".":(i.push(n),n="");return i}(t);return t=>{for(let i of e){if(""===i)break;t=t&&t[i]}return t}}(e)))(t)}function tt(t){return t.charAt(0).toUpperCase()+t.slice(1)}let te=t=>void 0!==t,ti=t=>"function"==typeof t,tn=(t,e)=>{if(t.size!==e.size)return!1;for(let i of t)if(!e.has(i))return!1;return!0};function tr(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}let ts=Math.PI,to=2*ts,ta=to+ts,tl=Number.POSITIVE_INFINITY,tu=ts/180,th=ts/2,tc=ts/4,td=2*ts/3,tf=Math.log10,tp=Math.sign;function tm(t,e,i){return Math.abs(t-e)<i}function tg(t){let e=Math.round(t),i=Math.pow(10,Math.floor(tf(t=tm(t,e,t/1e3)?e:t))),n=t/i;return(n<=1?1:n<=2?2:n<=5?5:10)*i}function ty(t){let e,i=[],n=Math.sqrt(t);for(e=1;e<n;e++)t%e==0&&(i.push(e),i.push(t/e));return n===(0|n)&&i.push(n),i.sort((t,e)=>t-e).pop(),i}function tv(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function tx(t,e){let i=Math.round(t);return i-e<=t&&i+e>=t}function tb(t,e,i){let n,r,s;for(n=0,r=t.length;n<r;n++)isNaN(s=t[n][i])||(e.min=Math.min(e.min,s),e.max=Math.max(e.max,s))}function tw(t){return ts/180*t}function tT(t){return 180/ts*t}function tM(t){if(!I(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function tP(t,e){let i=e.x-t.x,n=e.y-t.y,r=Math.sqrt(i*i+n*n),s=Math.atan2(n,i);return s<-.5*ts&&(s+=to),{angle:s,distance:r}}function tk(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function tS(t,e){return(t-e+ta)%to-ts}function tA(t){return(t%to+to)%to}function tE(t,e,i,n){let r=tA(t),s=tA(e),o=tA(i),a=tA(s-r),l=tA(o-r),u=tA(r-s),h=tA(r-o);return r===s||r===o||n&&s===o||a>l&&u<h}function tC(t,e,i){return Math.max(e,Math.min(i,t))}function tV(t){return tC(t,-32768,32767)}function tR(t,e,i,n=1e-6){return t>=Math.min(e,i)-n&&t<=Math.max(e,i)+n}function tD(t,e,i){let n;i=i||(i=>t[i]<e);let r=t.length-1,s=0;for(;r-s>1;)i(n=s+r>>1)?s=n:r=n;return{lo:s,hi:r}}let tj=(t,e,i,n)=>tD(t,i,n?n=>{let r=t[n][e];return r<i||r===i&&t[n+1][e]===i}:n=>t[n][e]<i),tO=(t,e,i)=>tD(t,i,n=>t[n][e]>=i);function tL(t,e,i){let n=0,r=t.length;for(;n<r&&t[n]<e;)n++;for(;r>n&&t[r-1]>i;)r--;return n>0||r<t.length?t.slice(n,r):t}let tF=["push","pop","shift","splice","unshift"];function tB(t,e){if(t._chartjs)return void t._chartjs.listeners.push(e);Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),tF.forEach(e=>{let i="_onData"+tt(e),n=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let r=n.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[i]&&t[i](...e)}),r}})})}function tI(t,e){let i=t._chartjs;if(!i)return;let n=i.listeners,r=n.indexOf(e);-1!==r&&n.splice(r,1),n.length>0||(tF.forEach(e=>{delete t[e]}),delete t._chartjs)}function t_(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let tN="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function tW(t,e){let i=[],n=!1;return function(...r){i=r,n||(n=!0,tN.call(window,()=>{n=!1,t.apply(e,i)}))}}function tU(t,e){let i;return function(...n){return e?(clearTimeout(i),i=setTimeout(t,e,n)):t.apply(this,n),e}}let t$=t=>"start"===t?"left":"end"===t?"right":"center",tY=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2,tH=(t,e,i,n)=>t===(n?"left":"right")?i:"center"===t?(e+i)/2:e;function tz(t,e,i){let n=e.length,r=0,s=n;if(t._sorted){let{iScale:o,vScale:a,_parsed:l}=t,u=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,h=o.axis,{min:c,max:d,minDefined:f,maxDefined:p}=o.getUserBounds();if(f){if(r=Math.min(tj(l,h,c).lo,i?n:tj(e,h,o.getPixelForValue(c)).lo),u){let t=l.slice(0,r+1).reverse().findIndex(t=>!L(t[a.axis]));r-=Math.max(0,t)}r=tC(r,0,n-1)}if(p){let t=Math.max(tj(l,o.axis,d,!0).hi+1,i?0:tj(e,h,o.getPixelForValue(d),!0).hi+1);if(u){let e=l.slice(t-1).findIndex(t=>!L(t[a.axis]));t+=Math.max(0,e)}s=tC(t,r,n)-r}else s=n-r}return{start:r,count:s}}function tX(t){let{xScale:e,yScale:i,_scaleRanges:n}=t,r={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!n)return t._scaleRanges=r,!0;let s=n.xmin!==e.min||n.xmax!==e.max||n.ymin!==i.min||n.ymax!==i.max;return Object.assign(n,r),s}let tq=t=>0===t||1===t,tK=(t,e,i)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*to/i)),tZ=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*to/i)+1,tG={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*th)+1,easeOutSine:t=>Math.sin(t*th),easeInOutSine:t=>-.5*(Math.cos(ts*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>tq(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>tq(t)?t:tK(t,.075,.3),easeOutElastic:t=>tq(t)?t:tZ(t,.075,.3),easeInOutElastic:t=>tq(t)?t:t<.5?.5*tK(2*t,.1125,.45):.5+.5*tZ(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-tG.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*tG.easeInBounce(2*t):.5*tG.easeOutBounce(2*t-1)+.5};function tQ(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function tJ(t){return tQ(t)?t:new D(t)}function t0(t){return tQ(t)?t:new D(t).saturate(.5).darken(.1).hexString()}let t1=["x","y","borderWidth","radius","tension"],t2=["color","borderColor","backgroundColor"],t5=new Map;function t9(t,e,i){return(function(t,e){let i=t+JSON.stringify(e=e||{}),n=t5.get(i);return n||(n=new Intl.NumberFormat(t,e),t5.set(i,n)),n})(e,i).format(t)}let t3={values:t=>F(t)?t:""+t,numeric(t,e,i){let n;if(0===t)return"0";let r=this.chart.options.locale,s=t;if(i.length>1){var o,a;let e,r=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(r<1e-4||r>1e15)&&(n="scientific"),o=t,Math.abs(e=(a=i).length>3?a[2].value-a[1].value:a[1].value-a[0].value)>=1&&o!==Math.floor(o)&&(e=o-Math.floor(o)),s=e}let l=tf(Math.abs(s)),u=isNaN(l)?1:Math.max(Math.min(-1*Math.floor(l),20),0),h={notation:n,minimumFractionDigits:u,maximumFractionDigits:u};return Object.assign(h,this.options.ticks.format),t9(t,r,h)},logarithmic(t,e,i){return 0===t?"0":[1,2,3,5,10,15].includes(i[e].significand||t/Math.pow(10,Math.floor(tf(t))))||e>.8*i.length?t3.numeric.call(this,t,e,i):""}};var t4={formatters:t3};let t8=Object.create(null),t6=Object.create(null);function t7(t,e){if(!e)return t;let i=e.split(".");for(let e=0,n=i.length;e<n;++e){let n=i[e];t=t[n]||(t[n]=Object.create(null))}return t}function et(t,e,i){return"string"==typeof e?K(t7(t,e),i):K(t7(t,""),e)}class ee{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>t0(e.backgroundColor),this.hoverBorderColor=(t,e)=>t0(e.borderColor),this.hoverColor=(t,e)=>t0(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return et(this,t,e)}get(t){return t7(this,t)}describe(t,e){return et(t6,t,e)}override(t,e){return et(t8,t,e)}route(t,e,i,n){let r=t7(this,t),s=t7(this,i),o="_"+e;Object.defineProperties(r,{[o]:{value:r[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[o],e=s[n];return B(t)?Object.assign({},e,t):N(t,e)},set(t){this[o]=t}}})}apply(t){t.forEach(t=>t(this))}}var ei=new ee({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:t2},numbers:{type:"number",properties:t1}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:t4.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function en(t,e,i,n,r){let s=e[r];return s||(s=e[r]=t.measureText(r).width,i.push(r)),s>n&&(n=s),n}function er(t,e,i,n){let r,s,o,a,l,u=(n=n||{}).data=n.data||{},h=n.garbageCollect=n.garbageCollect||[];n.font!==e&&(u=n.data={},h=n.garbageCollect=[],n.font=e),t.save(),t.font=e;let c=0,d=i.length;for(r=0;r<d;r++)if(null==(a=i[r])||F(a)){if(F(a))for(s=0,o=a.length;s<o;s++)null==(l=a[s])||F(l)||(c=en(t,u,h,c,l))}else c=en(t,u,h,c,a);t.restore();let f=h.length/2;if(f>i.length){for(r=0;r<f;r++)delete u[h[r]];h.splice(0,f)}return c}function es(t,e,i){let n=t.currentDevicePixelRatio,r=0!==i?Math.max(i/2,.5):0;return Math.round((e-r)*n)/n+r}function eo(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function ea(t,e,i,n){el(t,e,i,n,null)}function el(t,e,i,n,r){let s,o,a,l,u,h,c,d,f=e.pointStyle,p=e.rotation,m=e.radius,g=(p||0)*tu;if(f&&"object"==typeof f&&("[object HTMLImageElement]"===(s=f.toString())||"[object HTMLCanvasElement]"===s)){t.save(),t.translate(i,n),t.rotate(g),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),t.restore();return}if(!isNaN(m)&&!(m<=0)){switch(t.beginPath(),f){default:r?t.ellipse(i,n,r/2,m,0,0,to):t.arc(i,n,m,0,to),t.closePath();break;case"triangle":h=r?r/2:m,t.moveTo(i+Math.sin(g)*h,n-Math.cos(g)*m),g+=td,t.lineTo(i+Math.sin(g)*h,n-Math.cos(g)*m),g+=td,t.lineTo(i+Math.sin(g)*h,n-Math.cos(g)*m),t.closePath();break;case"rectRounded":u=.516*m,o=Math.cos(g+tc)*(l=m-u),c=Math.cos(g+tc)*(r?r/2-u:l),a=Math.sin(g+tc)*l,d=Math.sin(g+tc)*(r?r/2-u:l),t.arc(i-c,n-a,u,g-ts,g-th),t.arc(i+d,n-o,u,g-th,g),t.arc(i+c,n+a,u,g,g+th),t.arc(i-d,n+o,u,g+th,g+ts),t.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*m,h=r?r/2:l,t.rect(i-h,n-l,2*h,2*l);break}g+=tc;case"rectRot":c=Math.cos(g)*(r?r/2:m),o=Math.cos(g)*m,a=Math.sin(g)*m,d=Math.sin(g)*(r?r/2:m),t.moveTo(i-c,n-a),t.lineTo(i+d,n-o),t.lineTo(i+c,n+a),t.lineTo(i-d,n+o),t.closePath();break;case"crossRot":g+=tc;case"cross":c=Math.cos(g)*(r?r/2:m),o=Math.cos(g)*m,a=Math.sin(g)*m,d=Math.sin(g)*(r?r/2:m),t.moveTo(i-c,n-a),t.lineTo(i+c,n+a),t.moveTo(i+d,n-o),t.lineTo(i-d,n+o);break;case"star":c=Math.cos(g)*(r?r/2:m),o=Math.cos(g)*m,a=Math.sin(g)*m,d=Math.sin(g)*(r?r/2:m),t.moveTo(i-c,n-a),t.lineTo(i+c,n+a),t.moveTo(i+d,n-o),t.lineTo(i-d,n+o),g+=tc,c=Math.cos(g)*(r?r/2:m),o=Math.cos(g)*m,a=Math.sin(g)*m,d=Math.sin(g)*(r?r/2:m),t.moveTo(i-c,n-a),t.lineTo(i+c,n+a),t.moveTo(i+d,n-o),t.lineTo(i-d,n+o);break;case"line":o=r?r/2:Math.cos(g)*m,a=Math.sin(g)*m,t.moveTo(i-o,n-a),t.lineTo(i+o,n+a);break;case"dash":t.moveTo(i,n),t.lineTo(i+Math.cos(g)*(r?r/2:m),n+Math.sin(g)*m);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function eu(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function eh(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function ec(t){t.restore()}function ed(t,e,i,n,r){if(!e)return t.lineTo(i.x,i.y);if("middle"===r){let n=(e.x+i.x)/2;t.lineTo(n,e.y),t.lineTo(n,i.y)}else"after"===r!=!!n?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function ef(t,e,i,n){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(n?e.cp1x:e.cp2x,n?e.cp1y:e.cp2y,n?i.cp2x:i.cp1x,n?i.cp2y:i.cp1y,i.x,i.y)}function ep(t,e,i,n,r,s={}){let o,a,l=F(e)?e:[e],u=s.strokeWidth>0&&""!==s.strokeColor;for(t.save(),t.font=r.string,s.translation&&t.translate(s.translation[0],s.translation[1]),L(s.rotation)||t.rotate(s.rotation),s.color&&(t.fillStyle=s.color),s.textAlign&&(t.textAlign=s.textAlign),s.textBaseline&&(t.textBaseline=s.textBaseline),o=0;o<l.length;++o)a=l[o],s.backdrop&&function(t,e){let i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}(t,s.backdrop),u&&(s.strokeColor&&(t.strokeStyle=s.strokeColor),L(s.strokeWidth)||(t.lineWidth=s.strokeWidth),t.strokeText(a,i,n,s.maxWidth)),t.fillText(a,i,n,s.maxWidth),function(t,e,i,n,r){if(r.strikethrough||r.underline){let s=t.measureText(n),o=e-s.actualBoundingBoxLeft,a=e+s.actualBoundingBoxRight,l=i-s.actualBoundingBoxAscent,u=i+s.actualBoundingBoxDescent,h=r.strikethrough?(l+u)/2:u;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=r.decorationWidth||2,t.moveTo(o,h),t.lineTo(a,h),t.stroke()}}(t,i,n,a,s),n+=Number(r.lineHeight);t.restore()}function em(t,e){let{x:i,y:n,w:r,h:s,radius:o}=e;t.arc(i+o.topLeft,n+o.topLeft,o.topLeft,1.5*ts,ts,!0),t.lineTo(i,n+s-o.bottomLeft),t.arc(i+o.bottomLeft,n+s-o.bottomLeft,o.bottomLeft,ts,th,!0),t.lineTo(i+r-o.bottomRight,n+s),t.arc(i+r-o.bottomRight,n+s-o.bottomRight,o.bottomRight,th,0,!0),t.lineTo(i+r,n+o.topRight),t.arc(i+r-o.topRight,n+o.topRight,o.topRight,0,-th,!0),t.lineTo(i+o.topLeft,n)}let eg=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,ey=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,ev=t=>+t||0;function ex(t,e){let i={},n=B(e),r=n?Object.keys(e):e,s=B(t)?n?i=>N(t[i],t[e[i]]):e=>t[e]:()=>t;for(let t of r)i[t]=ev(s(t));return i}function eb(t){return ex(t,{top:"y",right:"x",bottom:"y",left:"x"})}function ew(t){return ex(t,["topLeft","topRight","bottomLeft","bottomRight"])}function eT(t){let e=eb(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function eM(t,e){t=t||{},e=e||ei.font;let i=N(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let n=N(t.style,e.style);n&&!(""+n).match(ey)&&(console.warn('Invalid font style specified: "'+n+'"'),n=void 0);let r={family:N(t.family,e.family),lineHeight:function(t,e){let i=(""+t).match(eg);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}(N(t.lineHeight,e.lineHeight),i),size:i,style:n,weight:N(t.weight,e.weight),string:""};return r.string=!r||L(r.size)||L(r.family)?null:(r.style?r.style+" ":"")+(r.weight?r.weight+" ":"")+r.size+"px "+r.family,r}function eP(t,e,i,n){let r,s,o,a=!0;for(r=0,s=t.length;r<s;++r)if(void 0!==(o=t[r])&&(void 0!==e&&"function"==typeof o&&(o=o(e),a=!1),void 0!==i&&F(o)&&(o=o[i%o.length],a=!1),void 0!==o))return n&&!a&&(n.cacheable=!1),o}function ek(t,e,i){let{min:n,max:r}=t,s=U(e,(r-n)/2),o=(t,e)=>i&&0===t?0:t+e;return{min:o(n,-Math.abs(s)),max:o(r,s)}}function eS(t,e){return Object.assign(Object.create(t),e)}function eA(t,e=[""],i,n,r=()=>t[0]){let s=i||t;return void 0===n&&(n=eL("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:s,_fallback:n,_getTarget:r,override:i=>eA([i,...t],e,s,n)},{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,n)=>eR(i,n,()=>(function(t,e,i,n){let r;for(let s of e)if(void 0!==(r=eL(eC(s,t),i)))return eV(t,r)?ej(i,n,t,r):r})(n,e,t,i)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>eF(t).includes(e),ownKeys:t=>eF(t),set(t,e,i){let n=t._storage||(t._storage=r());return t[e]=n[e]=i,delete t._keys,!0}})}function eE(t,e={scriptable:!0,indexable:!0}){let{_scriptable:i=e.scriptable,_indexable:n=e.indexable,_allKeys:r=e.allKeys}=t;return{allKeys:r,scriptable:i,indexable:n,isScriptable:ti(i)?i:()=>i,isIndexable:ti(n)?n:()=>n}}let eC=(t,e)=>t?t+tt(e):e,eV=(t,e)=>B(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function eR(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let n=i();return t[e]=n,n}let eD=(t,e)=>!0===t?e:"string"==typeof t?J(e,t):void 0;function ej(t,e,i,n){var r;let s=e._rootScopes,o=(r=e._fallback,ti(r)?r(i,n):r),a=[...t,...s],l=new Set;l.add(n);let u=eO(l,a,i,o||i,n);return null!==u&&(void 0===o||o===i||null!==(u=eO(l,a,o,u,n)))&&eA(Array.from(l),[""],s,o,()=>(function(t,e,i){let n=t._getTarget();e in n||(n[e]={});let r=n[e];return F(r)&&B(i)?i:r||{}})(e,i,n))}function eO(t,e,i,n,r){for(;i;)i=function(t,e,i,n,r){for(let o of e){let e=eD(i,o);if(e){var s;t.add(e);let o=(s=e._fallback,ti(s)?s(i,r):s);if(void 0!==o&&o!==i&&o!==n)return o}else if(!1===e&&void 0!==n&&i!==n)return null}return!1}(t,e,i,n,r);return i}function eL(t,e){for(let i of e){if(!i)continue;let e=i[t];if(void 0!==e)return e}}function eF(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let i of t)for(let t of Object.keys(i).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function eB(t,e,i,n){let r,s,o,{iScale:a}=t,{key:l="r"}=this._parsing,u=Array(n);for(r=0;r<n;++r)o=e[s=r+i],u[r]={r:a.parse(J(o,l),s)};return u}let eI=Number.EPSILON||1e-14,e_=(t,e)=>e<t.length&&!t[e].skip&&t[e],eN=t=>"x"===t?"y":"x";function eW(t,e,i){return Math.max(Math.min(t,i),e)}function eU(t,e,i,n,r){let s,o,a,l;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let i,n,r,s=eN(e),o=t.length,a=Array(o).fill(0),l=Array(o),u=e_(t,0);for(i=0;i<o;++i)if(n=r,r=u,u=e_(t,i+1),r){if(u){let t=u[e]-r[e];a[i]=0!==t?(u[s]-r[s])/t:0}l[i]=n?u?tp(a[i-1])!==tp(a[i])?0:(a[i-1]+a[i])/2:a[i-1]:a[i]}!function(t,e,i){let n,r,s,o,a,l=t.length,u=e_(t,0);for(let h=0;h<l-1;++h)if(a=u,u=e_(t,h+1),a&&u){if(tm(e[h],0,eI)){i[h]=i[h+1]=0;continue}(o=Math.pow(n=i[h]/e[h],2)+Math.pow(r=i[h+1]/e[h],2))<=9||(s=3/Math.sqrt(o),i[h]=n*s*e[h],i[h+1]=r*s*e[h])}}(t,a,l),function(t,e,i="x"){let n,r,s,o=eN(i),a=t.length,l=e_(t,0);for(let u=0;u<a;++u){if(r=s,s=l,l=e_(t,u+1),!s)continue;let a=s[i],h=s[o];r&&(n=(a-r[i])/3,s[`cp1${i}`]=a-n,s[`cp1${o}`]=h-n*e[u]),l&&(n=(l[i]-a)/3,s[`cp2${i}`]=a+n,s[`cp2${o}`]=h+n*e[u])}}(t,l,e)}(t,r);else{let i=n?t[t.length-1]:t[0];for(s=0,o=t.length;s<o;++s)l=function(t,e,i,n){let r=t.skip?e:t,s=i.skip?e:i,o=tk(e,r),a=tk(s,e),l=o/(o+a),u=a/(o+a);l=isNaN(l)?0:l,u=isNaN(u)?0:u;let h=n*l,c=n*u;return{previous:{x:e.x-h*(s.x-r.x),y:e.y-h*(s.y-r.y)},next:{x:e.x+c*(s.x-r.x),y:e.y+c*(s.y-r.y)}}}(i,a=t[s],t[Math.min(s+1,o-!n)%o],e.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,i=a}e.capBezierPoints&&function(t,e){let i,n,r,s,o,a=eu(t[0],e);for(i=0,n=t.length;i<n;++i)o=s,s=a,a=i<n-1&&eu(t[i+1],e),s&&(r=t[i],o&&(r.cp1x=eW(r.cp1x,e.left,e.right),r.cp1y=eW(r.cp1y,e.top,e.bottom)),a&&(r.cp2x=eW(r.cp2x,e.left,e.right),r.cp2y=eW(r.cp2y,e.top,e.bottom)))}(t,i)}function e$(){return"undefined"!=typeof window&&"undefined"!=typeof document}function eY(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function eH(t,e,i){let n;return"string"==typeof t?(n=parseInt(t,10),-1!==t.indexOf("%")&&(n=n/100*e.parentNode[i])):n=t,n}let ez=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),eX=["top","right","bottom","left"];function eq(t,e,i){let n={};i=i?"-"+i:"";for(let r=0;r<4;r++){let s=eX[r];n[s]=parseFloat(t[e+"-"+s+i])||0}return n.width=n.left+n.right,n.height=n.top+n.bottom,n}let eK=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function eZ(t,e){if("native"in t)return t;let{canvas:i,currentDevicePixelRatio:n}=e,r=ez(i),s="border-box"===r.boxSizing,o=eq(r,"padding"),a=eq(r,"border","width"),{x:l,y:u,box:h}=function(t,e){let i,n,r=t.touches,s=r&&r.length?r[0]:t,{offsetX:o,offsetY:a}=s,l=!1;if(eK(o,a,t.target))i=o,n=a;else{let t=e.getBoundingClientRect();i=s.clientX-t.left,n=s.clientY-t.top,l=!0}return{x:i,y:n,box:l}}(t,i),c=o.left+(h&&a.left),d=o.top+(h&&a.top),{width:f,height:p}=e;return s&&(f-=o.width+a.width,p-=o.height+a.height),{x:Math.round((l-c)/f*i.width/n),y:Math.round((u-d)/p*i.height/n)}}let eG=t=>Math.round(10*t)/10;function eQ(t,e,i,n){let r=ez(t),s=eq(r,"margin"),o=eH(r.maxWidth,t,"clientWidth")||tl,a=eH(r.maxHeight,t,"clientHeight")||tl,l=function(t,e,i){let n,r;if(void 0===e||void 0===i){let s=t&&eY(t);if(s){let t=s.getBoundingClientRect(),o=ez(s),a=eq(o,"border","width"),l=eq(o,"padding");e=t.width-l.width-a.width,i=t.height-l.height-a.height,n=eH(o.maxWidth,s,"clientWidth"),r=eH(o.maxHeight,s,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:n||tl,maxHeight:r||tl}}(t,e,i),{width:u,height:h}=l;if("content-box"===r.boxSizing){let t=eq(r,"border","width"),e=eq(r,"padding");u-=e.width+t.width,h-=e.height+t.height}return u=Math.max(0,u-s.width),h=Math.max(0,n?u/n:h-s.height),u=eG(Math.min(u,o,l.maxWidth)),h=eG(Math.min(h,a,l.maxHeight)),u&&!h&&(h=eG(u/2)),(void 0!==e||void 0!==i)&&n&&l.height&&h>l.height&&(u=eG(Math.floor((h=l.height)*n))),{width:u,height:h}}function eJ(t,e,i){let n=e||1,r=Math.floor(t.height*n),s=Math.floor(t.width*n);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let o=t.canvas;return o.style&&(i||!o.style.height&&!o.style.width)&&(o.style.height=`${t.height}px`,o.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==n||o.height!==r||o.width!==s)&&(t.currentDevicePixelRatio=n,o.height=r,o.width=s,t.ctx.setTransform(n,0,0,n,0,0),!0)}let e0=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};e$()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function e1(t,e){let i=ez(t).getPropertyValue(e),n=i&&i.match(/^(\d+)(\.\d+)?px$/);return n?+n[1]:void 0}function e2(t,e,i,n){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function e5(t,e,i,n){return{x:t.x+i*(e.x-t.x),y:"middle"===n?i<.5?t.y:e.y:"after"===n?i<1?t.y:e.y:i>0?e.y:t.y}}function e9(t,e,i,n){let r={x:t.cp2x,y:t.cp2y},s={x:e.cp1x,y:e.cp1y},o=e2(t,r,i),a=e2(r,s,i),l=e2(s,e,i),u=e2(o,a,i),h=e2(a,l,i);return e2(u,h,i)}function e3(t,e,i){var n;return t?(n=i,{x:t=>e+e+n-t,setWidth(t){n=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function e4(t,e){let i,n;("ltr"===e||"rtl"===e)&&(n=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=n)}function e8(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function e6(t){return"angle"===t?{between:tE,compare:tS,normalize:tA}:{between:tR,compare:(t,e)=>t-e,normalize:t=>t}}function e7({start:t,end:e,count:i,loop:n,style:r}){return{start:t%i,end:e%i,loop:n&&(e-t+1)%i==0,style:r}}function it(t,e,i){let n,r,s;if(!i)return[t];let{property:o,start:a,end:l}=i,u=e.length,{compare:h,between:c,normalize:d}=e6(o),{start:f,end:p,loop:m,style:g}=function(t,e,i){let n,{property:r,start:s,end:o}=i,{between:a,normalize:l}=e6(r),u=e.length,{start:h,end:c,loop:d}=t;if(d){for(h+=u,c+=u,n=0;n<u&&a(l(e[h%u][r]),s,o);++n)h--,c--;h%=u,c%=u}return c<h&&(c+=u),{start:h,end:c,loop:d,style:t.style}}(t,e,i),y=[],v=!1,x=null,b=()=>c(a,s,n)&&0!==h(a,s),w=()=>0===h(l,n)||c(l,s,n),T=()=>v||b(),M=()=>!v||w();for(let t=f,i=f;t<=p;++t)(r=e[t%u]).skip||(n=d(r[o]))!==s&&(v=c(n,a,l),null===x&&T()&&(x=0===h(n,a)?t:i),null!==x&&M()&&(y.push(e7({start:x,end:t,loop:m,count:u,style:g})),x=null),i=t,s=n);return null!==x&&y.push(e7({start:x,end:p,loop:m,count:u,style:g})),y}function ie(t,e){let i=[],n=t.segments;for(let r=0;r<n.length;r++){let s=it(n[r],t.points,e);s.length&&i.push(...s)}return i}function ii(t,e){let i=t.points,n=t.options.spanGaps,r=i.length;if(!r)return[];let s=!!t._loop,{start:o,end:a}=function(t,e,i,n){let r=0,s=e-1;if(i&&!n)for(;r<e&&!t[r].skip;)r++;for(;r<e&&t[r].skip;)r++;for(r%=e,i&&(s+=r);s>r&&t[s%e].skip;)s--;return{start:r,end:s%=e}}(i,r,s,n);if(!0===n)return ir(t,[{start:o,end:a,loop:s}],i,e);let l=a<o?a+r:a,u=!!t._fullLoop&&0===o&&a===r-1;return ir(t,function(t,e,i,n){let r,s=t.length,o=[],a=e,l=t[e];for(r=e+1;r<=i;++r){let i=t[r%s];i.skip||i.stop?l.skip||(n=!1,o.push({start:e%s,end:(r-1)%s,loop:n}),e=a=i.stop?r:null):(a=r,l.skip&&(e=r)),l=i}return null!==a&&o.push({start:e%s,end:a%s,loop:n}),o}(i,o,l,u),i,e)}function ir(t,e,i,n){return n&&n.setContext&&i?function(t,e,i,n){let r=t._chart.getContext(),s=is(t.options),{_datasetIndex:o,options:{spanGaps:a}}=t,l=i.length,u=[],h=s,c=e[0].start,d=c;function f(t,e,n,r){let s=a?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=s;for(;i[e%l].skip;)e+=s;t%l!=e%l&&(u.push({start:t%l,end:e%l,loop:n,style:r}),h=r,c=e%l)}}for(let t of e){let e,s=i[(c=a?c:t.start)%l];for(d=c+1;d<=t.end;d++){let a=i[d%l];(function(t,e){if(!e)return!1;let i=[],n=function(t,e){return tQ(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,n)!==JSON.stringify(e,n)})(e=is(n.setContext(eS(r,{type:"segment",p0:s,p1:a,p0DataIndex:(d-1)%l,p1DataIndex:d%l,datasetIndex:o}))),h)&&f(c,d-1,t.loop,h),s=a,h=e}c<d-1&&f(c,d-1,t.loop,h)}return u}(t,e,i,n):e}function is(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function io(t,e,i){return t.options.clip?t[i]:e[i]}function ia(t,e){let i=e._clip;if(i.disabled)return!1;let n=function(t,e){let{xScale:i,yScale:n}=t;return i&&n?{left:io(i,e,"left"),right:io(i,e,"right"),top:io(n,e,"top"),bottom:io(n,e,"bottom")}:e}(e,t.chartArea);return{left:!1===i.left?0:n.left-(!0===i.left?0:i.left),right:!1===i.right?t.width:n.right+(!0===i.right?0:i.right),top:!1===i.top?0:n.top-(!0===i.top?0:i.top),bottom:!1===i.bottom?t.height:n.bottom+(!0===i.bottom?0:i.bottom)}}},1508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},1539:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},2082:(t,e,i)=>{i.d(e,{xQ:()=>s});var n=i(2115),r=i(845);function s(t=!0){let e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,n.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},2713:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2885:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(2115);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},3109:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3311:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},3904:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4065:(t,e,i)=>{i.d(e,{N1:()=>h,yP:()=>c});var n=i(2115),r=i(2502);let s="label";function o(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function a(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s,n=[];t.datasets=e.map(e=>{let r=t.datasets.find(t=>t[i]===e[i]);return!r||!e.data||n.includes(r)?{...e}:(n.push(r),Object.assign(r,e),r)})}let l=(0,n.forwardRef)(function(t,e){let{height:i=150,width:l=300,redraw:u=!1,datasetIdKey:h,type:c,data:d,options:f,plugins:p=[],fallbackContent:m,updateMode:g,...y}=t,v=(0,n.useRef)(null),x=(0,n.useRef)(null),b=()=>{v.current&&(x.current=new r.t1(v.current,{type:c,data:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,i={labels:[],datasets:[]};return i.labels=t.labels,a(i,t.datasets,e),i}(d,h),options:f&&{...f},plugins:p}),o(e,x.current))},w=()=>{o(e,null),x.current&&(x.current.destroy(),x.current=null)};return(0,n.useEffect)(()=>{!u&&x.current&&f&&function(t,e){let i=t.options;i&&e&&Object.assign(i,e)}(x.current,f)},[u,f]),(0,n.useEffect)(()=>{!u&&x.current&&(x.current.config.data.labels=d.labels)},[u,d.labels]),(0,n.useEffect)(()=>{!u&&x.current&&d.datasets&&a(x.current.config.data,d.datasets,h)},[u,d.datasets]),(0,n.useEffect)(()=>{x.current&&(u?(w(),setTimeout(b)):x.current.update(g))},[u,f,d.labels,d.datasets,g]),(0,n.useEffect)(()=>{x.current&&(w(),setTimeout(b))},[c]),(0,n.useEffect)(()=>(b(),()=>w()),[]),n.createElement("canvas",{ref:v,role:"img",height:i,width:l,...y},m)});function u(t,e){return r.t1.register(e),(0,n.forwardRef)((e,i)=>n.createElement(l,{...e,ref:i,type:t}))}let h=u("line",r.ZT),c=u("bar",r.A6)},4229:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4357:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},5196:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6408:(t,e,i)=>{let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,n){if("function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}return e}function a(t,e,i){let n=t.getProps();return o(n,e,void 0!==i?i:n.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sA});let u=t=>t,h={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],d={value:null,addProjectionMetrics:null};function f(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=c.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&d.value&&d.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:f,update:p,preRender:m,render:g,postRender:y}=o,v=()=>{let s=h.useManualTiming?r.timestamp:performance.now();i=!1,h.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),f.process(r),p.process(r),m.process(r),g.process(r),y.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(v))},x=()=>{i=!0,n=!0,r.isProcessing||t(v)};return{schedule:c.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||x(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<c.length;e++)o[c[e]].cancel(t)},state:r,steps:o}}let{schedule:p,cancel:m,state:g,steps:y}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),b=new Set(["width","height","top","left","right","bottom",...v]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function T(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class M{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>T(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function P(){n=void 0}let k={now:()=>(void 0===n&&k.set(g.isProcessing||h.useManualTiming?g.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(P)}},S=t=>!isNaN(parseFloat(t)),A={current:void 0};class E{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=k.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new M);let i=this.events[t].add(e);return"change"===t?()=>{i(),p.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return A.current&&A.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function C(t,e){return new E(t,e)}let V=t=>Array.isArray(t),R=t=>!!(t&&t.getVelocity);function D(t,e){let i=t.getValue("willChange");if(R(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let j=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),O="data-"+j("framerAppearId"),L=(t,e)=>i=>e(t(i)),F=(...t)=>t.reduce(L),B=(t,e,i)=>i>e?e:i<t?t:i,I=t=>1e3*t,_=t=>t/1e3,N={layout:0,mainThread:0,waapi:0},W=()=>{},U=()=>{},$=t=>e=>"string"==typeof e&&e.startsWith(t),Y=$("--"),H=$("var(--"),z=t=>!!H(t)&&X.test(t.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,q={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},K={...q,transform:t=>B(0,1,t)},Z={...q,default:1},G=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(Q);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>B(0,255,t),tn={...q,transform:t=>Math.round(ti(t))},tr={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(i)+", "+G(K.transform(n))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tr.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),tc=to("vw"),td={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tf={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tl.transform(G(e))+", "+tl.transform(G(i))+", "+G(K.transform(n))+")"},tp={test:t=>tr.test(t)||ts.test(t)||tf.test(t),parse:t=>tr.test(t)?tr.parse(t):tf.test(t)?tf.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):tf.transform(t)},tm=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",ty="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tx(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tv,t=>(tp.test(t)?(n.color.push(s),r.push(ty),i.push(tp.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tg),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tb(t){return tx(t).values}function tw(t){let{split:e,types:i}=tx(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tg?r+=G(t[s]):e===ty?r+=tp.transform(t[s]):r+=t[s]}return r}}let tT=t=>"number"==typeof t?0:t,tM={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tm)?.length||0)>0},parse:tb,createTransformer:tw,getAnimatableNone:function(t){let e=tb(t);return tw(t)(e.map(tT))}};function tP(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tk(t,e){return i=>i>0?e:t}let tS=(t,e,i)=>t+(e-t)*i,tA=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tE=[ts,tr,tf],tC=t=>tE.find(e=>e.test(t));function tV(t){let e=tC(t);if(W(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tf&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=tP(a,n,t+1/3),s=tP(a,n,t),o=tP(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let tR=(t,e)=>{let i=tV(t),n=tV(e);if(!i||!n)return tk(t,e);let r={...i};return t=>(r.red=tA(i.red,n.red,t),r.green=tA(i.green,n.green,t),r.blue=tA(i.blue,n.blue,t),r.alpha=tS(i.alpha,n.alpha,t),tr.transform(r))},tD=new Set(["none","hidden"]);function tj(t,e){return i=>tS(t,e,i)}function tO(t){return"number"==typeof t?tj:"string"==typeof t?z(t)?tk:tp.test(t)?tR:tB:Array.isArray(t)?tL:"object"==typeof t?tp.test(t)?tR:tF:tk}function tL(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tO(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tF(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tO(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tB=(t,e)=>{let i=tM.createTransformer(e),n=tx(t),r=tx(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tD.has(t)&&!r.values.length||tD.has(e)&&!n.values.length?function(t,e){return tD.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):F(tL(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):(W(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tk(t,e))};function tI(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tS(t,e,i):tO(t)(t,e)}let t_=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>p.update(e,t),stop:()=>m(e),now:()=>g.isProcessing?g.timestamp:k.now()}},tN=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=t(e/(r-1))+", ";return`linear(${n.substring(0,n.length-2)})`};function tW(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tU(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let t$={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tY(t,e){return t*Math.sqrt(1-e*e)}let tH=["duration","bounce"],tz=["stiffness","damping","mass"];function tX(t,e){return e.some(e=>void 0!==t[e])}function tq(t=t$.visualDuration,e=t$.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:d,velocity:f,isResolvedFromDuration:p}=function(t){let e={velocity:t$.velocity,stiffness:t$.stiffness,damping:t$.damping,mass:t$.mass,isResolvedFromDuration:!1,...t};if(!tX(t,tz)&&tX(t,tH))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*B(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:t$.mass,stiffness:n,damping:r}}else{let i=function({duration:t=t$.duration,bounce:e=t$.bounce,velocity:i=t$.velocity,mass:n=t$.mass}){let r,s;W(t<=I(t$.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=B(t$.minDamping,t$.maxDamping,o),t=B(t$.minDuration,t$.maxDuration,_(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/tY(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=tY(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=I(t),isNaN(a))return{stiffness:t$.stiffness,damping:t$.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:t$.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-_(n.velocity||0)}),m=f||0,g=h/(2*Math.sqrt(u*c)),y=a-o,v=_(Math.sqrt(u/c)),x=5>Math.abs(y);if(r||(r=x?t$.restSpeed.granular:t$.restSpeed.default),s||(s=x?t$.restDelta.granular:t$.restDelta.default),g<1){let t=tY(v,g);i=e=>a-Math.exp(-g*v*e)*((m+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(m+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return a-i*((m+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let b={calculatedDuration:p&&d||null,next:t=>{let e=i(t);if(p)l.done=t>=d;else{let n=0===t?m:0;g<1&&(n=0===t?I(m):tU(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tW(b),2e4),e=tN(e=>b.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return b}function tK({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d,f=t[0],p={done:!1,value:f},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=f+y,x=void 0===o?v:o(v);x!==v&&(y=x-f);let b=t=>-y*Math.exp(-t/n),w=t=>x+b(t),T=t=>{let e=b(t),i=w(t);p.done=Math.abs(e)<=u,p.value=p.done?x:i},M=t=>{m(p.value)&&(c=t,d=tq({keyframes:[p.value,g(p.value)],velocity:tU(w,t,p.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return M(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,T(t),M(t)),void 0!==c&&t>=c)?d.next(t-c):(e||T(t),p)}}}tq.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(tW(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:_(r)}}(t,100,tq);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let tZ=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tG(t,e,i,n){if(t===e&&i===n)return u;let r=e=>(function(t,e,i,n,r){let s,o,a=0;do(s=tZ(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tZ(r(t),e,n)}let tQ=tG(.42,0,1,1),tJ=tG(0,0,.58,1),t0=tG(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t5=t=>e=>1-t(1-e),t9=tG(.33,1.53,.69,.99),t3=t5(t9),t4=t2(t3),t8=t=>(t*=2)<1?.5*t3(t):.5*(2-Math.pow(2,-10*(t-1))),t6=t=>1-Math.sin(Math.acos(t)),t7=t5(t6),et=t2(t6),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t6,circInOut:et,circOut:t7,backIn:t3,backInOut:t4,backOut:t9,anticipate:t8},en=t=>"string"==typeof t,er=t=>{if(ee(t)){U(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return tG(e,i,n,r)}return en(t)?(U(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function eo({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let s=t1(n)?n.map(er):er(n),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(U(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||h.mix||tI,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=F(Array.isArray(e)?e[i]||u:e,s)),n.push(s)}return n}(e,n,r),l=a.length,c=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=es(t[n],t[n+1],i);return a[n](r)};return i?e=>c(B(t[0],t[s-1],e)):c}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=es(0,e,n);t.push(tS(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(ea),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let eu={decay:tK,inertia:tK,tween:eo,keyframes:eo,spring:tq};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ec{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ed=t=>t/100;class ef extends ec{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==k.now()&&this.tick(k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},N.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=F(ed,tI(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tW(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:d,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(i=1-i,d&&(i-=d/o)):"mirror"===c&&(x=s)),v=B(0,1,i)*o}let b=y?{done:!1,value:u[0]}:x.next(v);r&&(b.value=r(b.value));let{done:w}=b;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&f!==tK&&(b.value=el(u,this.options,m,this.speed)),p&&p(b.value),T&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return _(this.calculatedDuration)}get time(){return _(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=_(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=t_,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,N.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ep=t=>180*t/Math.PI,em=t=>ey(ep(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:em,rotateZ:em,skewX:t=>ep(Math.atan(t[1])),skewY:t=>ep(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ex=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:ex,scale:t=>(ev(t)+ex(t))/2,rotateX:t=>ey(ep(Math.atan2(t[6],t[5]))),rotateY:t=>ey(ep(Math.atan2(-t[2],t[0]))),rotateZ:em,rotate:em,skewX:t=>ep(Math.atan(t[4])),skewY:t=>ep(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ew(t){return+!!t.includes("scale")}function eT(t,e){let i,n;if(!t||"none"===t)return ew(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=eb,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eg,n=e}if(!n)return ew(e);let s=i[e],o=n[1].split(",").map(eP);return"function"==typeof s?s(o):o[s]}let eM=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eT(i,e)};function eP(t){return parseFloat(t.trim())}let ek=t=>t===q||t===tu,eS=new Set(["x","y","z"]),eA=v.filter(t=>!eS.has(t)),eE={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eT(e,"x"),y:(t,{transform:e})=>eT(e,"y")};eE.translateX=eE.x,eE.translateY=eE.y;let eC=new Set,eV=!1,eR=!1,eD=!1;function ej(){if(eR){let t=Array.from(eC).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eA.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eR=!1,eV=!1,eC.forEach(t=>t.complete(eD)),eC.clear()}function eO(){eC.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eR=!0)})}class eL{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eC.add(this),eV||(eV=!0,p.read(eO),p.resolveKeyframes(ej))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eC.delete(this)}cancel(){"scheduled"===this.state&&(eC.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eF=t=>t.startsWith("--");function eB(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eB(()=>void 0!==window.ScrollTimeline),e_={},eN=function(t,e){let i=eB(t);return()=>e_[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eW=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,eU={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eW([0,.65,.55,1]),circOut:eW([.55,0,1,.45]),backIn:eW([.31,.01,.66,-.59]),backOut:eW([.33,1.53,.69,.99])};function e$(t){return"function"==typeof t&&"applyToOptions"in t}class eY extends ec{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,U("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return e$(t)&&eN()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?eN()?tN(e,i):"ease-out":ee(e)?eW(e):Array.isArray(e)?e.map(e=>t(e,i)||eU.easeOut):eU[e]}(a,r);Array.isArray(c)&&(h.easing=c),d.value&&N.waapi++;let f={delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(f.pseudoElement=u);let p=t.animate(h,f);return d.value&&p.finished.finally(()=>{N.waapi--}),p}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eF(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return _(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return _(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,u):e(this)}}let eH={anticipate:t8,backInOut:t4,circInOut:et};class ez extends eY{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eH&&(t.ease=eH[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ef({...s,autoplay:!1}),a=I(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eX=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tM.test(t)||"0"===t)&&!t.startsWith("url("));var eq,eK,eZ=i(7351);let eG=new Set(["opacity","clipPath","filter","transform"]),eQ=eB(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ec{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=k.now();let c={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},d=u?.KeyframeResolver||eL;this.keyframeResolver=new d(o,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:c}=i;this.resolvedAt=k.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eX(r,e),a=eX(s,e);return W(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||e$(i))&&n)}(t,r,s,o)&&((h.instantAnimations||!a)&&c?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let d={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},f=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(0,eZ.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&eG.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(d)?new ez({...d,element:d.motionValue.owner.current}):new ef(d);f.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eD=!0,eO(),ej(),eD=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e5={type:"keyframes",duration:.8},e9={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e3=(t,{keyframes:e})=>e.length>2?e5:x.has(t)?t.startsWith("scale")?e2(e[1]):e1:e9,e4=(t,e,i,n={},r,s)=>o=>{let a=l(n,t)||{},u=a.delay||n.delay||0,{elapsed:c=0}=n;c-=I(u);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(d,e3(t,d)),d.duration&&(d.duration=I(d.duration)),d.repeatDelay&&(d.repeatDelay=I(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let f=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(f=!0)),(h.instantAnimations||h.skipAnimations)&&(f=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,f&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(e0),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(d.keyframes,a);if(void 0!==t)return void p.update(()=>{d.onUpdate(t),d.onComplete()})}return a.isSync?new ef(d):new eJ(d)};function e8(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;n&&(s=n);let h=[],c=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||c&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(c,e))continue;let o={delay:i,...l(s||{},e)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(r)&&r===a&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[O];if(i){let t=window.MotionHandoffAnimation(i,e,p);null!==t&&(o.startTime=t,d=!0)}}D(t,e),n.start(e4(e,n,r,t.shouldReduceMotion&&b.has(e)?{type:!1}:o,t,d));let f=n.animation;f&&h.push(f)}return o&&Promise.all(h).then(()=>{p.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=a(t,e)||{};for(let e in r={...r,...i}){var s;let i=V(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,C(i))}}(t,o)})}),h}function e6(t,e,i={}){let n=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(e8(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(e7).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(e6(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+n,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,io=[...ii].reverse(),ia=ii.length;function il(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iu(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class ih{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ic extends ih{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>e6(t,e,i)));else if("string"==typeof e)n=e6(t,e,i);else{let r="function"==typeof e?a(t,e,i.custom):e;n=Promise.all(e8(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iu(),n=!0,s=e=>(i,n)=>{let r=a(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let n=ir[t],r=e.props[n];(ie(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],c=new Set,d={},f=1/0;for(let e=0;e<ia;e++){var p,m;let a=io[e],g=i[a],y=void 0!==l[a]?l[a]:u[a],v=ie(y),x=a===o?g.isActive:null;!1===x&&(f=e);let b=y===u[a]&&y!==l[a]&&v;if(b&&n&&t.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...d},!g.isActive&&null===x||!y&&!g.prevProp||r(y)||"boolean"==typeof y)continue;let w=(p=g.prevProp,"string"==typeof(m=y)?m!==p:!!Array.isArray(m)&&!it(m,p)),T=w||a===o&&g.isActive&&!b&&v||e>f&&v,M=!1,P=Array.isArray(y)?y:[y],k=P.reduce(s(a),{});!1===x&&(k={});let{prevResolvedValues:S={}}=g,A={...S,...k},E=e=>{T=!0,c.has(e)&&(M=!0,c.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in A){let e=k[t],i=S[t];if(d.hasOwnProperty(t))continue;let n=!1;(V(e)&&V(i)?it(e,i):e===i)?void 0!==e&&c.has(t)?E(t):g.protectedKeys[t]=!0:null!=e?E(t):c.add(t)}g.prevProp=y,g.prevResolvedValues=k,g.isActive&&(d={...d,...k}),n&&t.blockInitialAnimation&&(T=!1);let C=!(b&&w)||M;T&&C&&h.push(...P.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let g=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),n=!1,g?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=o(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iu(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let id=0;class ip extends ih{constructor(){super(...arguments),this.id=id++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let im={x:!1,y:!1};function ig(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let iy=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iv(t){return{point:{x:t.pageX,y:t.pageY}}}let ix=t=>e=>iy(e)&&t(e,iv(e));function ib(t,e,i,n){return ig(t,e,ix(i),n)}function iw({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iT(t){return t.max-t.min}function iM(t,e,i,n=.5){t.origin=n,t.originPoint=tS(e.min,e.max,t.origin),t.scale=iT(i)/iT(e),t.translate=tS(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iP(t,e,i,n){iM(t.x,e.x,i.x,n?n.originX:void 0),iM(t.y,e.y,i.y,n?n.originY:void 0)}function ik(t,e,i){t.min=i.min+e.min,t.max=t.min+iT(e)}function iS(t,e,i){t.min=e.min-i.min,t.max=t.min+iT(e)}function iA(t,e,i){iS(t.x,e.x,i.x),iS(t.y,e.y,i.y)}let iE=()=>({translate:0,scale:1,origin:0,originPoint:0}),iC=()=>({x:iE(),y:iE()}),iV=()=>({min:0,max:0}),iR=()=>({x:iV(),y:iV()});function iD(t){return[t("x"),t("y")]}function ij(t){return void 0===t||1===t}function iO({scale:t,scaleX:e,scaleY:i}){return!ij(t)||!ij(e)||!ij(i)}function iL(t){return iO(t)||iF(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iF(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iB(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iI(t,e=0,i=1,n,r){t.min=iB(t.min,e,i,n,r),t.max=iB(t.max,e,i,n,r)}function i_(t,{x:e,y:i}){iI(t.x,e.translate,e.scale,e.originPoint),iI(t.y,i.translate,i.scale,i.originPoint)}function iN(t,e){t.min=t.min+e,t.max=t.max+e}function iW(t,e,i,n,r=.5){let s=tS(t.min,t.max,r);iI(t,e,i,s,n)}function iU(t,e){iW(t.x,e.x,e.scaleX,e.scale,e.originX),iW(t.y,e.y,e.scaleY,e.scale,e.originY)}function i$(t,e){return iw(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iY=({current:t})=>t?t.ownerDocument.defaultView:null;function iH(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iz=(t,e)=>Math.abs(t-e);class iX{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iZ(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iz(t.x,e.x)**2+iz(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=g;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iq(e,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iZ("pointercancel"===t.type?this.lastMoveEventInfo:iq(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!iy(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=iq(iv(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=g;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iZ(s,this.history)),this.removeListeners=F(ib(this.contextWindow,"pointermove",this.handlePointerMove),ib(this.contextWindow,"pointerup",this.handlePointerUp),ib(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function iq(t,e){return e?{point:e(t.point)}:t}function iK(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iZ({point:t},e){return{point:t,delta:iK(t,iG(e)),offset:iK(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iG(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>I(.1)));)i--;if(!n)return{x:0,y:0};let s=_(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iG(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i2=new WeakMap;class i5{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iR(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iX(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iv(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iD(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=iT(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&p.postRender(()=>r(t,e)),D(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iD(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:iY(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&p.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!i9(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tS(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tS(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&iH(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:iQ(t.x,i,r),y:iQ(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iD(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iH(e))return!1;let n=e.current;U(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=i$(t,i),{scroll:r}=e;return r&&(iN(n.x,r.offset.x),iN(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:iJ(t.x,s.x),y:iJ(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iw(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iD(o=>{if(!i9(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return D(this.visualElement,t),i.start(e4(t,i,0,e,this.visualElement,!1))}stopAnimation(){iD(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iD(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iD(e=>{let{drag:i}=this.getProps();if(!i9(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-tS(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iH(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iD(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iT(t),r=iT(e);return r>n?i=es(e.min,e.max-n,t.min):n>r&&(i=es(t.min,t.max-r,e.min)),B(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iD(e=>{if(!i9(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(tS(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;i2.set(this.visualElement,this);let t=ib(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iH(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),p.read(e);let r=ig(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iD(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function i9(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i3 extends ih{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i5(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i4=t=>(e,i)=>{t&&p.postRender(()=>t(e,i))};class i8 extends ih{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iX(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iY(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i4(t),onStart:i4(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&p.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=ib(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i6=i(5155);let{schedule:i7}=f(queueMicrotask,!1);var nt=i(2115),ne=i(2082),ni=i(869);let nn=(0,nt.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ns(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let no={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=ns(t,e.target.x),n=ns(t,e.target.y);return`${i}% ${n}%`}},na={};class nl extends nt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in nh)na[t]=nh[t],Y(t)&&(na[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||p.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i7.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nu(t){let[e,i]=(0,ne.xQ)(),n=(0,nt.useContext)(ni.L);return(0,i6.jsx)(nl,{...t,layoutGroup:n,switchLayoutGroup:(0,nt.useContext)(nn),isPresent:e,safeToRemove:i})}let nh={borderRadius:{...no,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:no,borderTopRightRadius:no,borderBottomLeftRadius:no,borderBottomRightRadius:no,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tM.parse(t);if(n.length>5)return t;let r=tM.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=tS(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var nc=i(6983);function nd(t){return(0,nc.G)(t)&&"ownerSVGElement"in t}let nf=(t,e)=>t.depth-e.depth;class np{constructor(){this.children=[],this.isDirty=!1}add(t){w(this.children,t),this.isDirty=!0}remove(t){T(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nf),this.isDirty=!1,this.children.forEach(t)}}function nm(t){return R(t)?t.get():t}let ng=["TopLeft","TopRight","BottomLeft","BottomRight"],ny=ng.length,nv=t=>"string"==typeof t?parseFloat(t):t,nx=t=>"number"==typeof t||tu.test(t);function nb(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nw=nM(0,.5,t7),nT=nM(.5,.95,u);function nM(t,e,i){return n=>n<t?0:n>e?1:i(es(t,e,n))}function nP(t,e){t.min=e.min,t.max=e.max}function nk(t,e){nP(t.x,e.x),nP(t.y,e.y)}function nS(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nA(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nE(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tS(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tS(s.min,s.max,n);t===s&&(a-=e),t.min=nA(t.min,e,i,a,r),t.max=nA(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nC=["x","scaleX","originX"],nV=["y","scaleY","originY"];function nR(t,e,i,n){nE(t.x,e,nC,i?i.x:void 0,n?n.x:void 0),nE(t.y,e,nV,i?i.y:void 0,n?n.y:void 0)}function nD(t){return 0===t.translate&&1===t.scale}function nj(t){return nD(t.x)&&nD(t.y)}function nO(t,e){return t.min===e.min&&t.max===e.max}function nL(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nF(t,e){return nL(t.x,e.x)&&nL(t.y,e.y)}function nB(t){return iT(t.x)/iT(t.y)}function nI(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class n_{constructor(){this.members=[]}add(t){w(this.members,t),t.scheduleRender()}remove(t){if(T(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nN={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nW=["","X","Y","Z"],nU={visibility:"hidden"},n$=0;function nY(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nH({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=n$++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,d.value&&(nN.nodes=nN.calculatedTargetDeltas=nN.calculatedProjections=0),this.nodes.forEach(nq),this.nodes.forEach(n1),this.nodes.forEach(n2),this.nodes.forEach(nK),d.addProjectionMetrics&&d.addProjectionMetrics(nN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new np)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new M),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=nd(e)&&!(nd(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=k.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(m(n),t(s-e))};return p.setup(n,!0),()=>m(n)}(n,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(n0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||n6,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),u=!this.targetLayout||!nF(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||n0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n5),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[O];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",p,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nG);return}this.isUpdating||this.nodes.forEach(nQ),this.isUpdating=!1,this.nodes.forEach(nJ),this.nodes.forEach(nz),this.nodes.forEach(nX),this.clearAllSnapshots();let t=k.now();g.delta=B(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nZ),this.sharedNodes.forEach(n9)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iT(this.snapshot.measuredBox.x)||iT(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iR(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nj(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||iL(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),re((e=n).x),re(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iR();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rn))){let{scroll:t}=this.root;t&&(iN(e.x,t.offset.x),iN(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iR();if(nk(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nk(e,t),iN(e.x,r.offset.x),iN(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=iR();nk(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iU(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iL(n.latestValues)&&iU(i,n.latestValues)}return iL(this.latestValues)&&iU(i,this.latestValues),i}removeTransform(t){let e=iR();nk(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iL(i.latestValues))continue;iO(i.latestValues)&&i.updateSnapshot();let n=iR();nk(n,i.measurePageBox()),nR(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iL(this.latestValues)&&nR(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iR(),this.relativeTargetOrigin=iR(),iA(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nk(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iR(),this.targetWithTransforms=iR()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,ik(s.x,o.x,a.x),ik(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nk(this.target,this.layout.layoutBox),i_(this.target,this.targetDelta)):nk(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iR(),this.relativeTargetOrigin=iR(),iA(this.relativeTargetOrigin,this.target,t.target),nk(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}d.value&&nN.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iO(this.parent.latestValues)||iF(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nk(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iU(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,i_(t,s)),n&&iL(r.latestValues)&&iU(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iR());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nS(this.prevProjectionDelta.x,this.projectionDelta.x),nS(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iP(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nI(this.projectionDelta.x,this.prevProjectionDelta.x)&&nI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),d.value&&nN.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iC(),this.projectionDelta=iC(),this.projectionDeltaWithTransform=iC()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iC();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iR(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(n8));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(n3(o.x,t.x,n),n3(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,f,p,m,g;iA(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=a,g=n,n4(f.x,p.x,m.x,g),n4(f.y,p.y,m.y,g),i&&(u=this.relativeTarget,d=i,nO(u.x,d.x)&&nO(u.y,d.y))&&(this.isProjectionDirty=!1),i||(i=iR()),nk(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=tS(0,i.opacity??1,nw(n)),t.opacityExit=tS(e.opacity??1,0,nT(n))):s&&(t.opacity=tS(e.opacity??1,i.opacity??1,n));for(let r=0;r<ny;r++){let s=`border${ng[r]}Radius`,o=nb(e,s),a=nb(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nx(o)===nx(a)?(t[s]=Math.max(tS(nv(o),nv(a),n),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tS(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{nr.hasAnimatedSinceResize=!0,N.layout++,this.motionValue||(this.motionValue=C(0)),this.currentAnimation=function(t,e,i){let n=R(t)?t:C(t);return n.start(e4("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{N.layout--},onComplete:()=>{N.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&ri(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iR();let e=iT(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iT(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nk(e,i),iU(e,r),iP(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new n_),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nY("z",t,n,this.animationValues);for(let e=0;e<nW.length;e++)nY(`rotate${nW[e]}`,t,n,this.animationValues),nY(`skew${nW[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return nU;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=nm(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nm(t?.pointerEvents)||""),this.hasProjected&&!iL(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,na){if(void 0===r[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=na[t],a="none"===e.transform?r[t]:i(r[t],n);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=n===this?nm(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nG),this.root.sharedNodes.clear()}}}function nz(t){t.updateLayout()}function nX(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?iD(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=iT(n);n.min=i[t].min,n.max=n.min+r}):ri(r,e.layoutBox,i)&&iD(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=iT(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iC();iP(o,i,e.layoutBox);let a=iC();s?iP(a,t.applyTransform(n,!0),e.measuredBox):iP(a,i,e.layoutBox);let l=!nj(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=iR();iA(o,e.layoutBox,r.layoutBox);let a=iR();iA(a,i,s.layoutBox),nF(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nq(t){d.value&&nN.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nK(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nZ(t){t.clearSnapshot()}function nG(t){t.clearMeasurements()}function nQ(t){t.isLayoutDirty=!1}function nJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function n0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function n1(t){t.resolveTargetDelta()}function n2(t){t.calcProjection()}function n5(t){t.resetSkewAndRotation()}function n9(t){t.removeLeadSnapshot()}function n3(t,e,i){t.translate=tS(e.translate,0,i),t.scale=tS(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function n4(t,e,i,n){t.min=tS(e.min,i.min,n),t.max=tS(e.max,i.max,n)}function n8(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n6={duration:.45,ease:[.4,0,.1,1]},n7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rt=n7("applewebkit/")&&!n7("chrome/")?Math.round:u;function re(t){t.min=rt(t.min),t.max=rt(t.max)}function ri(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nB(e)-nB(i)))}function rn(t){return t!==t.root&&t.scroll?.wasRoot}let rr=nH({attachResizeListener:(t,e)=>ig(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},ro=nH({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rs.current){let t=new rr({});t.mount(window),t.setOptions({layoutScroll:!0}),rs.current=t}return rs.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ra(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function rl(t){return!("touch"===t.pointerType||im.x||im.y)}function ru(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&p.postRender(()=>r(e,iv(e)))}class rh extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{if(!rl(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{rl(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(ru(this.node,e,"Start"),t=>ru(this.node,t,"End"))))}unmount(){}}class rc extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=F(ig(this.node.current,"focus",()=>this.onFocus()),ig(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rd=(t,e)=>!!e&&(t===e||rd(t,e.parentElement)),rf=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rp=new WeakSet;function rm(t){return e=>{"Enter"===e.key&&t(e)}}function rg(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ry=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rm(()=>{if(rp.has(i))return;rg(i,"down");let t=rm(()=>{rg(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rg(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rv(t){return iy(t)&&!(im.x||im.y)}function rx(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&p.postRender(()=>r(e,iv(e)))}class rb extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{let n=t.currentTarget;if(!rv(t))return;rp.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rp.has(n)&&rp.delete(n),rv(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||rd(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,eZ.s)(t))&&(t.addEventListener("focus",t=>ry(t,r)),rf.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rx(this.node,e,"Start"),(t,{success:e})=>rx(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rw=new WeakMap,rT=new WeakMap,rM=t=>{let e=rw.get(t.target);e&&e(t)},rP=t=>{t.forEach(rM)},rk={some:0,all:1};class rS extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rk[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rT.has(i)||rT.set(i,{});let n=rT.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rP,{root:t,...e})),n[r]}(e);return rw.set(t,i),n.observe(t),()=>{rw.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rA=(0,nt.createContext)({strict:!1});var rE=i(1508);let rC=(0,nt.createContext)({});function rV(t){return r(t.animate)||ir.some(e=>ie(t[e]))}function rR(t){return!!(rV(t)||t.variants)}function rD(t){return Array.isArray(t)?t.join(" "):t}var rj=i(8972);let rO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rL={};for(let t in rO)rL[t]={isEnabled:e=>rO[t].some(t=>!!e[t])};let rF=Symbol.for("motionComponentSymbol");var rB=i(845),rI=i(7494);function r_(t,{layout:e,layoutId:i}){return x.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!na[t]||"opacity"===t)}let rN=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rW={...q,transform:Math.round},rU={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:Z,scaleX:Z,scaleY:Z,scaleZ:Z,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:K,originX:td,originY:td,originZ:tu,zIndex:rW,fillOpacity:K,strokeOpacity:K,numOctaves:rW},r$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rY=v.length;function rH(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(x.has(t)){o=!0;continue}if(Y(t)){r[t]=i;continue}{let e=rN(i,rU[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rY;s++){let o=v[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rN(a,rU[o]);if(!l){r=!1;let e=r$[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rz=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rX(t,e,i){for(let n in e)R(e[n])||r_(n,i)||(t[n]=e[n])}let rq={offset:"stroke-dashoffset",array:"stroke-dasharray"},rK={offset:"strokeDashoffset",array:"strokeDasharray"};function rZ(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(rH(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rq:rK;t[s.offset]=tu.transform(-n);let o=tu.transform(e),a=tu.transform(i);t[s.array]=`${o} ${a}`}(c,r,s,o,!1)}let rG=()=>({...rz(),attrs:{}}),rQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),rJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rJ.has(t)}let r1=t=>!r0(t);try{!function(t){t&&(r1=e=>e.startsWith("on")?!r0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r5(t){if("string"!=typeof t||t.includes("-"));else if(r2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var r9=i(2885);let r3=t=>(e,i)=>{let n=(0,nt.useContext)(rC),s=(0,nt.useContext)(rB.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},a=n(t,{});for(let t in a)s[t]=nm(a[t]);let{initial:l,animate:u}=t,h=rV(t),c=rR(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let d=!!i&&!1===i.initial,f=(d=d||!1===l)?u:l;if(f&&"boolean"!=typeof f&&!r(f)){let e=Array.isArray(f)?f:[f];for(let i=0;i<e.length;i++){let n=o(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,s);return i?a():(0,r9.M)(a)};function r4(t,e,i){let{style:n}=t,r={};for(let s in n)(R(n[s])||e.style&&R(e.style[s])||r_(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let r8={useVisualState:r3({scrapeMotionValuesFromProps:r4,createRenderState:rz})};function r6(t,e,i){let n=r4(t,e,i);for(let i in t)(R(t[i])||R(e[i]))&&(n[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let r7={useVisualState:r3({scrapeMotionValuesFromProps:r6,createRenderState:rG})},st=t=>e=>e.test(t),se=[q,tu,tl,ta,tc,th,{test:t=>"auto"===t,parse:t=>t}],si=t=>se.find(st(t)),sn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),so=new Set(["brightness","contrast","saturate","opacity"]);function sa(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(Q)||[];if(!n)return t;let r=i.replace(n,""),s=+!!so.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...tM,getAnimatableNone:t=>{let e=t.match(sl);return e?e.map(sa).join(" "):t}},sh={...rU,color:tp,backgroundColor:tp,outlineColor:tp,fill:tp,stroke:tp,borderColor:tp,borderTopColor:tp,borderRightColor:tp,borderBottomColor:tp,borderLeftColor:tp,filter:su,WebkitFilter:su},sc=t=>sh[t];function sd(t,e){let i=sc(t);return i!==su&&(i=tM),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sf=new Set(["auto","none","0"]);class sp extends eL{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&z(n=n.trim())){let r=function t(e,i,n=1){U(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=sr.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return sn(t)?parseFloat(t):t}return z(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!b.has(i)||2!==t.length)return;let[n,r]=t,s=si(n),o=si(r);if(s!==o)if(ek(s)&&ek(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eE[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||ss(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!sf.has(e)&&tx(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=sd(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eE[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=eE[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sm=[...se,tp,tM],sg=t=>sm.find(st(t)),sy={current:null},sv={current:!1},sx=new WeakMap,sb=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sw{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eL,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,p.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rV(e),this.isVariantNode=rR(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&R(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sx.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sv.current||function(){if(sv.current=!0,rj.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sy.current=t.matches;t.addListener(e),e()}else sy.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=x.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&p.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rL){let e=rL[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iR()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sb.length;e++){let i=sb[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(R(r))t.addValue(n,r);else if(R(s))t.addValue(n,C(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,C(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=C(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(sn(i)||ss(i))?i=parseFloat(i):!sg(i)&&tM.test(e)&&(i=sd(t,e)),this.setBaseTarget(t,R(i)?i.get():i)),R(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=o(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||R(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new M),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sT extends sw{constructor(){super(...arguments),this.KeyframeResolver=sp}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;R(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sM(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}class sP extends sT{constructor(){super(...arguments),this.type="html",this.renderInstance=sM}readValueFromInstance(t,e){if(x.has(e))return this.projection?.isProjecting?ew(e):eM(t,e);{let i=window.getComputedStyle(t),n=(Y(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i$(t,e)}build(t,e,i){rH(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r4(t,e,i)}}let sk=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sS extends sT{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iR}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(x.has(e)){let t=sc(e);return t&&t.default||0}return e=sk.has(e)?e:j(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r6(t,e,i)}build(t,e,i){rZ(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in sM(t,e,void 0,n),e.attrs)t.setAttribute(sk.has(i)?i:j(i),e.attrs[i])}mount(t){this.isSVGTag=rQ(t.tagName),super.mount(t)}}let sA=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((eq={animation:{Feature:ic},exit:{Feature:ip},inView:{Feature:rS},tap:{Feature:rb},focus:{Feature:rc},hover:{Feature:rh},pan:{Feature:i8},drag:{Feature:i3,ProjectionNode:ro,MeasureLayout:nu},layout:{ProjectionNode:ro,MeasureLayout:nu}},eK=(t,e)=>r5(t)?new sS(e):new sP(e,{allowProjection:t!==nt.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var i,n,l;let u,h={...(0,nt.useContext)(rE.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,nt.useContext)(ni.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=h,d=function(t){let{initial:e,animate:i}=function(t,e){if(rV(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nt.useContext)(rC));return(0,nt.useMemo)(()=>({initial:e,animate:i}),[rD(e),rD(i)])}(t),f=o(t,c);if(!c&&rj.B){n=0,l=0,(0,nt.useContext)(rA).strict;let t=function(t){let{drag:e,layout:i}=rL;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,d.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,nt.useContext)(rC),o=(0,nt.useContext)(rA),a=(0,nt.useContext)(rB.t),l=(0,nt.useContext)(rE.Q).reducedMotion,u=(0,nt.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,c=(0,nt.useContext)(nn);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&iH(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,c);let d=(0,nt.useRef)(!1);(0,nt.useInsertionEffect)(()=>{h&&d.current&&h.update(i,a)});let f=i[O],p=(0,nt.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,rI.E)(()=>{h&&(d.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),i7.render(h.render),p.current&&h.animationState&&h.animationState.animateChanges())}),(0,nt.useEffect)(()=>{h&&(!p.current&&h.animationState&&h.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),p.current=!1))}),h}(a,f,h,r,t.ProjectionNode)}return(0,i6.jsxs)(rC.Provider,{value:d,children:[u&&d.visualElement?(0,i6.jsx)(u,{visualElement:d.visualElement,...h}):null,s(a,t,(i=d.visualElement,(0,nt.useCallback)(t=>{t&&f.onMount&&f.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iH(e)&&(e.current=t))},[i])),f,c,d.visualElement)]})}n&&function(t){for(let e in t)rL[e]={...rL[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(i=null!=(e=a.displayName)?e:a.name)?i:"",")"));let u=(0,nt.forwardRef)(l);return u[rF]=a,u}({...r5(t)?r7:r8,preloadedFeatures:eq,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(r5(e)?function(t,e,i,n){let r=(0,nt.useMemo)(()=>{let i=rG();return rZ(i,e,rQ(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rX(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return rX(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nt.useMemo)(()=>{let i=rz();return rH(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r1(r)||!0===i&&r0(r)||!e&&!r0(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==nt.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,nt.useMemo)(()=>R(u)?u.get():u,[u]);return(0,nt.createElement)(e,{...l,children:h})}}(e),createVisualElement:eK,Component:t})}))},6785:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},6983:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},7351:(t,e,i)=>{i.d(e,{s:()=>r});var n=i(6983);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},7494:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(2115);let r=i(8972).B?n.useLayoutEffect:n.useEffect},8186:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},8972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},9074:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9946:(t,e,i)=>{i.d(e,{A:()=>c});var n=i(2115);let r=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),o=t=>{let e=s(t);return e.charAt(0).toUpperCase()+e.slice(1)},a=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()},l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,n.forwardRef)((t,e)=>{let{color:i="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:h="",children:c,iconNode:d,...f}=t;return(0,n.createElement)("svg",{ref:e,...u,width:r,height:r,stroke:i,strokeWidth:o?24*Number(s)/Number(r):s,className:a("lucide",h),...!c&&!l(f)&&{"aria-hidden":"true"},...f},[...d.map(t=>{let[e,i]=t;return(0,n.createElement)(e,i)}),...Array.isArray(c)?c:[c]])}),c=(t,e)=>{let i=(0,n.forwardRef)((i,s)=>{let{className:l,...u}=i;return(0,n.createElement)(h,{ref:s,iconNode:e,className:a("lucide-".concat(r(o(t))),"lucide-".concat(t),l),...u})});return i.displayName=o(t),i}}}]);