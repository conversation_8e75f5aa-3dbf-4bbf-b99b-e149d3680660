{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/app/api/lottery-data/route.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\nimport { LotteryResult } from \"@/types/lottery\";\n\n// Cache for storing fetched data\nlet cachedData: LotteryResult[] | null = null;\nlet lastFetchTime = 0;\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\nasync function fetchFromGitHub(): Promise<LotteryResult[]> {\n  try {\n    // Fetch from the vietvudanh/vietlott-data repository\n    const response = await fetch(\n      \"https://raw.githubusercontent.com/vietvudanh/vietlott-data/master/data/power_655.json\",\n      {\n        headers: {\n          \"User-Agent\": \"Vietlott-Analyzer/1.0\",\n        },\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(`GitHub API error: ${response.status}`);\n    }\n\n    const rawData = await response.text();\n    const lines = rawData.trim().split(\"\\n\");\n    const results: LotteryResult[] = [];\n\n    for (const line of lines) {\n      try {\n        const data = JSON.parse(line);\n        if (data.result && Array.isArray(data.result)) {\n          results.push({\n            id: data.id || \"\",\n            date: data.date || \"\",\n            result: data.result.slice(0, 6), // Take first 6 numbers\n            powerNumber: data.result[6], // 7th number is power number\n            processTime: data.process_time,\n          });\n        }\n      } catch (parseError) {\n        console.warn(\"Failed to parse line:\", line);\n      }\n    }\n\n    return results.sort(\n      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()\n    );\n  } catch (error) {\n    console.error(\"Error fetching from GitHub:\", error);\n    throw error;\n  }\n}\n\nasync function fallbackScrapeVietlott(): Promise<LotteryResult[]> {\n  try {\n    // This is a simplified fallback - in a real implementation,\n    // you would scrape the official Vietlott website\n    console.log(\"Fallback scraping not implemented yet\");\n    return [];\n  } catch (error) {\n    console.error(\"Error in fallback scraping:\", error);\n    return [];\n  }\n}\n\nexport async function GET() {\n  try {\n    const now = Date.now();\n\n    // Return cached data if it's still fresh\n    if (cachedData && now - lastFetchTime < CACHE_DURATION) {\n      return NextResponse.json(cachedData);\n    }\n\n    let data: LotteryResult[] = [];\n\n    // For now, use mock data for development\n    // TODO: Implement proper data fetching from reliable source\n    data = generateMockData();\n\n    // Uncomment below when you have a working data source\n    /*\n    try {\n      // Try to fetch from GitHub first\n      data = await fetchFromGitHub();\n    } catch (githubError) {\n      console.error('GitHub fetch failed:', githubError);\n\n      try {\n        // Fallback to scraping\n        data = await fallbackScrapeVietlott();\n      } catch (scrapeError) {\n        console.error('Fallback scraping failed:', scrapeError);\n\n        // If both fail, return mock data for development\n        data = generateMockData();\n      }\n    }\n    */\n\n    // Update cache\n    cachedData = data;\n    lastFetchTime = now;\n\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error(\"API error:\", error);\n    return NextResponse.json(\n      { error: \"Failed to fetch lottery data\" },\n      { status: 500 }\n    );\n  }\n}\n\nfunction generateMockData(): LotteryResult[] {\n  const mockData: LotteryResult[] = [];\n  const today = new Date();\n\n  for (let i = 0; i < 100; i++) {\n    const date = new Date(today);\n    date.setDate(date.getDate() - i * 2); // Every 2 days\n\n    const result = [];\n    const usedNumbers = new Set();\n\n    // Generate 6 unique numbers between 1-55\n    while (result.length < 6) {\n      const num = Math.floor(Math.random() * 55) + 1;\n      if (!usedNumbers.has(num)) {\n        result.push(num);\n        usedNumbers.add(num);\n      }\n    }\n\n    result.sort((a, b) => a - b);\n\n    mockData.push({\n      id: String(1200 - i).padStart(5, \"0\"),\n      date: date.toISOString().split(\"T\")[0],\n      result,\n      powerNumber: Math.floor(Math.random() * 55) + 1,\n      processTime: date.toISOString(),\n    });\n  }\n\n  return mockData;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,iCAAiC;AACjC,IAAI,aAAqC;AACzC,IAAI,gBAAgB;AACpB,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAElD,eAAe;IACb,IAAI;QACF,qDAAqD;QACrD,MAAM,WAAW,MAAM,MACrB,yFACA;YACE,SAAS;gBACP,cAAc;YAChB;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,SAAS,MAAM,EAAE;QACxD;QAEA,MAAM,UAAU,MAAM,SAAS,IAAI;QACnC,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC;QACnC,MAAM,UAA2B,EAAE;QAEnC,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,IAAI,KAAK,MAAM,IAAI,MAAM,OAAO,CAAC,KAAK,MAAM,GAAG;oBAC7C,QAAQ,IAAI,CAAC;wBACX,IAAI,KAAK,EAAE,IAAI;wBACf,MAAM,KAAK,IAAI,IAAI;wBACnB,QAAQ,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG;wBAC7B,aAAa,KAAK,MAAM,CAAC,EAAE;wBAC3B,aAAa,KAAK,YAAY;oBAChC;gBACF;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,IAAI,CAAC,yBAAyB;YACxC;QACF;QAEA,OAAO,QAAQ,IAAI,CACjB,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;IAEnE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEA,eAAe;IACb,IAAI;QACF,4DAA4D;QAC5D,iDAAiD;QACjD,QAAQ,GAAG,CAAC;QACZ,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,EAAE;IACX;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,MAAM,KAAK,GAAG;QAEpB,yCAAyC;QACzC,IAAI,cAAc,MAAM,gBAAgB,gBAAgB;YACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,IAAI,OAAwB,EAAE;QAE9B,yCAAyC;QACzC,4DAA4D;QAC5D,OAAO;QAEP,sDAAsD;QACtD;;;;;;;;;;;;;;;;;IAiBA,GAEA,eAAe;QACf,aAAa;QACb,gBAAgB;QAEhB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA+B,GACxC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS;IACP,MAAM,WAA4B,EAAE;IACpC,MAAM,QAAQ,IAAI;IAElB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,MAAM,OAAO,IAAI,KAAK;QACtB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,IAAI,IAAI,eAAe;QAErD,MAAM,SAAS,EAAE;QACjB,MAAM,cAAc,IAAI;QAExB,yCAAyC;QACzC,MAAO,OAAO,MAAM,GAAG,EAAG;YACxB,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;YAC7C,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM;gBACzB,OAAO,IAAI,CAAC;gBACZ,YAAY,GAAG,CAAC;YAClB;QACF;QAEA,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAE1B,SAAS,IAAI,CAAC;YACZ,IAAI,OAAO,OAAO,GAAG,QAAQ,CAAC,GAAG;YACjC,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC;YACA,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;YAC9C,aAAa,KAAK,WAAW;QAC/B;IACF;IAEA,OAAO;AACT", "debugId": null}}]}