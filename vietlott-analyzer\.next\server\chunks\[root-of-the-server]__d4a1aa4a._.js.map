{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Work/Automation/Draff/vietlott-analyzer/src/app/api/lottery-data/route.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\nimport { LotteryResult, LotteryType } from \"@/types/lottery\";\n\n// Cache for different lottery types\nconst cache = new Map<string, { data: LotteryResult[]; timestamp: number }>();\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\nasync function fetchFromGitHub(\n  lotteryType: LotteryType = \"power655\"\n): Promise<LotteryResult[]> {\n  try {\n    // Determine the correct file based on lottery type\n    const fileName =\n      lotteryType === \"power655\" ? \"power655.jsonl\" : \"power645.jsonl\";\n\n    // Fetch from the vietvudanh/vietlott-data repository\n    const response = await fetch(\n      `https://raw.githubusercontent.com/vietvudanh/vietlott-data/master/data/${fileName}`,\n      {\n        headers: {\n          \"User-Agent\": \"Vietlott-Analyzer/1.0\",\n        },\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(`GitHub API error: ${response.status}`);\n    }\n\n    const rawData = await response.text();\n    const lines = rawData.trim().split(\"\\n\");\n    const results: LotteryResult[] = [];\n\n    for (const line of lines) {\n      try {\n        const data = JSON.parse(line);\n        if (\n          data.result &&\n          Array.isArray(data.result) &&\n          data.result.length >= 6\n        ) {\n          // Validate that all numbers are within valid range based on lottery type\n          const maxNumber = lotteryType === \"power655\" ? 55 : 45;\n          const validNumbers = data.result\n            .slice(0, 6)\n            .every(\n              (num: number) =>\n                typeof num === \"number\" && num >= 1 && num <= maxNumber\n            );\n\n          if (validNumbers && data.date && data.id) {\n            results.push({\n              id: data.id,\n              date: data.date,\n              result: data.result.slice(0, 6), // Take first 6 numbers\n              powerNumber:\n                lotteryType === \"power655\" &&\n                data.result[6] &&\n                data.result[6] >= 1 &&\n                data.result[6] <= 55\n                  ? data.result[6]\n                  : undefined,\n              processTime: data.process_time,\n            });\n          }\n        }\n      } catch (parseError) {\n        console.warn(\"Failed to parse line:\", line);\n      }\n    }\n\n    return results.sort(\n      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()\n    );\n  } catch (error) {\n    console.error(\"Error fetching from GitHub:\", error);\n    throw error;\n  }\n}\n\nasync function fallbackScrapeVietlott(): Promise<LotteryResult[]> {\n  try {\n    // This is a simplified fallback - in a real implementation,\n    // you would scrape the official Vietlott website\n    console.log(\"Fallback scraping not implemented yet\");\n    return [];\n  } catch (error) {\n    console.error(\"Error in fallback scraping:\", error);\n    return [];\n  }\n}\n\nexport async function GET(request: Request) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const lotteryType = (searchParams.get(\"type\") as LotteryType) || \"power655\";\n\n    const now = Date.now();\n    const cacheKey = lotteryType;\n\n    // Return cached data if it's still fresh\n    const cachedEntry = cache.get(cacheKey);\n    if (cachedEntry && now - cachedEntry.timestamp < CACHE_DURATION) {\n      return NextResponse.json(cachedEntry.data);\n    }\n\n    let data: LotteryResult[] = [];\n\n    try {\n      // Try to fetch from GitHub first\n      data = await fetchFromGitHub(lotteryType);\n      console.log(\n        `Fetched ${data.length} results from GitHub for ${lotteryType}`\n      );\n    } catch (githubError) {\n      console.error(\"GitHub fetch failed:\", githubError);\n\n      try {\n        // Fallback to scraping\n        data = await fallbackScrapeVietlott();\n        console.log(`Fetched ${data.length} results from fallback scraping`);\n      } catch (scrapeError) {\n        console.error(\"Fallback scraping failed:\", scrapeError);\n\n        // If both fail, return mock data for development\n        console.log(\"Using mock data as fallback\");\n        data = generateMockData(lotteryType);\n      }\n    }\n\n    // Add lottery type to each result\n    data = data.map((result) => ({ ...result, lotteryType }));\n\n    // Update cache\n    cache.set(cacheKey, { data, timestamp: now });\n\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error(\"API error:\", error);\n    return NextResponse.json(\n      { error: \"Failed to fetch lottery data\" },\n      { status: 500 }\n    );\n  }\n}\n\nfunction generateMockData(\n  lotteryType: LotteryType = \"power655\"\n): LotteryResult[] {\n  const mockData: LotteryResult[] = [];\n  const today = new Date();\n  const maxNumber = lotteryType === \"power655\" ? 55 : 45;\n\n  for (let i = 0; i < 100; i++) {\n    const date = new Date(today);\n    date.setDate(date.getDate() - i * 2); // Every 2 days\n\n    const result = [];\n    const usedNumbers = new Set();\n\n    // Generate 6 unique numbers within the valid range\n    while (result.length < 6) {\n      const num = Math.floor(Math.random() * maxNumber) + 1;\n      if (!usedNumbers.has(num)) {\n        result.push(num);\n        usedNumbers.add(num);\n      }\n    }\n\n    result.sort((a, b) => a - b);\n\n    mockData.push({\n      id: String(1200 - i).padStart(5, \"0\"),\n      date: date.toISOString().split(\"T\")[0],\n      result,\n      powerNumber:\n        lotteryType === \"power655\"\n          ? Math.floor(Math.random() * 55) + 1\n          : undefined,\n      processTime: date.toISOString(),\n      lotteryType,\n    });\n  }\n\n  return mockData;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,oCAAoC;AACpC,MAAM,QAAQ,IAAI;AAClB,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAElD,eAAe,gBACb,cAA2B,UAAU;IAErC,IAAI;QACF,mDAAmD;QACnD,MAAM,WACJ,gBAAgB,aAAa,mBAAmB;QAElD,qDAAqD;QACrD,MAAM,WAAW,MAAM,MACrB,CAAC,uEAAuE,EAAE,UAAU,EACpF;YACE,SAAS;gBACP,cAAc;YAChB;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,SAAS,MAAM,EAAE;QACxD;QAEA,MAAM,UAAU,MAAM,SAAS,IAAI;QACnC,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC;QACnC,MAAM,UAA2B,EAAE;QAEnC,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,IACE,KAAK,MAAM,IACX,MAAM,OAAO,CAAC,KAAK,MAAM,KACzB,KAAK,MAAM,CAAC,MAAM,IAAI,GACtB;oBACA,yEAAyE;oBACzE,MAAM,YAAY,gBAAgB,aAAa,KAAK;oBACpD,MAAM,eAAe,KAAK,MAAM,CAC7B,KAAK,CAAC,GAAG,GACT,KAAK,CACJ,CAAC,MACC,OAAO,QAAQ,YAAY,OAAO,KAAK,OAAO;oBAGpD,IAAI,gBAAgB,KAAK,IAAI,IAAI,KAAK,EAAE,EAAE;wBACxC,QAAQ,IAAI,CAAC;4BACX,IAAI,KAAK,EAAE;4BACX,MAAM,KAAK,IAAI;4BACf,QAAQ,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG;4BAC7B,aACE,gBAAgB,cAChB,KAAK,MAAM,CAAC,EAAE,IACd,KAAK,MAAM,CAAC,EAAE,IAAI,KAClB,KAAK,MAAM,CAAC,EAAE,IAAI,KACd,KAAK,MAAM,CAAC,EAAE,GACd;4BACN,aAAa,KAAK,YAAY;wBAChC;oBACF;gBACF;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,IAAI,CAAC,yBAAyB;YACxC;QACF;QAEA,OAAO,QAAQ,IAAI,CACjB,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;IAEnE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEA,eAAe;IACb,IAAI;QACF,4DAA4D;QAC5D,iDAAiD;QACjD,QAAQ,GAAG,CAAC;QACZ,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,AAAC,aAAa,GAAG,CAAC,WAA2B;QAEjE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,WAAW;QAEjB,yCAAyC;QACzC,MAAM,cAAc,MAAM,GAAG,CAAC;QAC9B,IAAI,eAAe,MAAM,YAAY,SAAS,GAAG,gBAAgB;YAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,YAAY,IAAI;QAC3C;QAEA,IAAI,OAAwB,EAAE;QAE9B,IAAI;YACF,iCAAiC;YACjC,OAAO,MAAM,gBAAgB;YAC7B,QAAQ,GAAG,CACT,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,yBAAyB,EAAE,aAAa;QAEnE,EAAE,OAAO,aAAa;YACpB,QAAQ,KAAK,CAAC,wBAAwB;YAEtC,IAAI;gBACF,uBAAuB;gBACvB,OAAO,MAAM;gBACb,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,+BAA+B,CAAC;YACrE,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,6BAA6B;gBAE3C,iDAAiD;gBACjD,QAAQ,GAAG,CAAC;gBACZ,OAAO,iBAAiB;YAC1B;QACF;QAEA,kCAAkC;QAClC,OAAO,KAAK,GAAG,CAAC,CAAC,SAAW,CAAC;gBAAE,GAAG,MAAM;gBAAE;YAAY,CAAC;QAEvD,eAAe;QACf,MAAM,GAAG,CAAC,UAAU;YAAE;YAAM,WAAW;QAAI;QAE3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA+B,GACxC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,iBACP,cAA2B,UAAU;IAErC,MAAM,WAA4B,EAAE;IACpC,MAAM,QAAQ,IAAI;IAClB,MAAM,YAAY,gBAAgB,aAAa,KAAK;IAEpD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,MAAM,OAAO,IAAI,KAAK;QACtB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,IAAI,IAAI,eAAe;QAErD,MAAM,SAAS,EAAE;QACjB,MAAM,cAAc,IAAI;QAExB,mDAAmD;QACnD,MAAO,OAAO,MAAM,GAAG,EAAG;YACxB,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa;YACpD,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM;gBACzB,OAAO,IAAI,CAAC;gBACZ,YAAY,GAAG,CAAC;YAClB;QACF;QAEA,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAE1B,SAAS,IAAI,CAAC;YACZ,IAAI,OAAO,OAAO,GAAG,QAAQ,CAAC,GAAG;YACjC,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC;YACA,aACE,gBAAgB,aACZ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,IACjC;YACN,aAAa,KAAK,WAAW;YAC7B;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}]}