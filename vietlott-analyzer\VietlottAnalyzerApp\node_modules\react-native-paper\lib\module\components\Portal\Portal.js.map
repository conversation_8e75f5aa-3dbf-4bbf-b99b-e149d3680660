{"version": 3, "names": ["React", "PortalConsumer", "PortalHost", "PortalContext", "Consumer", "SettingsConsumer", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ThemeProvider", "withInternalTheme", "Portal", "Component", "Host", "render", "children", "theme", "props", "createElement", "settings", "manager", "value"], "sourceRoot": "../../../../src", "sources": ["components/Portal/Portal.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAI9B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,UAAU,IAAIC,aAAa,QAAuB,cAAc;AACvE,SACEC,QAAQ,IAAIC,gBAAgB,EAC5BC,QAAQ,IAAIC,gBAAgB,QACvB,qBAAqB;AAC5B,SAASC,aAAa,EAAEC,iBAAiB,QAAQ,oBAAoB;AAarE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,SAASV,KAAK,CAACW,SAAS,CAAQ;EAC1C;EACA,OAAOC,IAAI,GAAGV,UAAU;EAExBW,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC,QAAQ;MAAEC;IAAM,CAAC,GAAG,IAAI,CAACC,KAAK;IAEtC,oBACEhB,KAAA,CAAAiB,aAAA,CAACZ,gBAAgB,QACba,QAAQ,iBACRlB,KAAA,CAAAiB,aAAA,CAACd,aAAa,CAACC,QAAQ,QACnBe,OAAO,iBACPnB,KAAA,CAAAiB,aAAA,CAAChB,cAAc;MAACkB,OAAO,EAAEA;IAAyB,gBAChDnB,KAAA,CAAAiB,aAAA,CAACV,gBAAgB;MAACa,KAAK,EAAEF;IAAS,gBAChClB,KAAA,CAAAiB,aAAA,CAACT,aAAa;MAACO,KAAK,EAAEA;IAAM,GAAED,QAAwB,CACtC,CACJ,CAEI,CAEV,CAAC;EAEvB;AACF;AAEA,eAAeL,iBAAiB,CAACC,MAAM,CAAC", "ignoreList": []}