{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "PortalManager", "PureComponent", "state", "portals", "mount", "key", "children", "setState", "update", "map", "item", "unmount", "filter", "render", "createElement", "View", "collapsable", "pointerEvents", "style", "StyleSheet", "absoluteFill", "exports"], "sourceRoot": "../../../../src", "sources": ["components/Portal/PortalManager.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAgD,SAAAD,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAShD;AACA;AACA;AACe,MAAMkB,aAAa,SAASvB,KAAK,CAACwB,aAAa,CAAY;EACxEC,KAAK,GAAU;IACbC,OAAO,EAAE;EACX,CAAC;EAEDC,KAAK,GAAGA,CAACC,GAAW,EAAEC,QAAyB,KAAK;IAClD,IAAI,CAACC,QAAQ,CAAEL,KAAK,KAAM;MACxBC,OAAO,EAAE,CAAC,GAAGD,KAAK,CAACC,OAAO,EAAE;QAAEE,GAAG;QAAEC;MAAS,CAAC;IAC/C,CAAC,CAAC,CAAC;EACL,CAAC;EAEDE,MAAM,GAAGA,CAACH,GAAW,EAAEC,QAAyB,KAC9C,IAAI,CAACC,QAAQ,CAAEL,KAAK,KAAM;IACxBC,OAAO,EAAED,KAAK,CAACC,OAAO,CAACM,GAAG,CAAEC,IAAI,IAAK;MACnC,IAAIA,IAAI,CAACL,GAAG,KAAKA,GAAG,EAAE;QACpB,OAAO;UAAE,GAAGK,IAAI;UAAEJ;QAAS,CAAC;MAC9B;MACA,OAAOI,IAAI;IACb,CAAC;EACH,CAAC,CAAC,CAAC;EAELC,OAAO,GAAIN,GAAW,IACpB,IAAI,CAACE,QAAQ,CAAEL,KAAK,KAAM;IACxBC,OAAO,EAAED,KAAK,CAACC,OAAO,CAACS,MAAM,CAAEF,IAAI,IAAKA,IAAI,CAACL,GAAG,KAAKA,GAAG;EAC1D,CAAC,CAAC,CAAC;EAELQ,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACX,KAAK,CAACC,OAAO,CAACM,GAAG,CAAC,CAAC;MAAEJ,GAAG;MAAEC;IAAS,CAAC,kBAC9C7B,KAAA,CAAAqC,aAAA,CAAClC,YAAA,CAAAmC,IAAI;MACHV,GAAG,EAAEA,GAAI;MACTW,WAAW,EACT,KAAK,CAAC,wGACP;MACDC,aAAa,EAAC,UAAU;MACxBC,KAAK,EAAEC,uBAAU,CAACC;IAAa,GAE9Bd,QACG,CACP,CAAC;EACJ;AACF;AAACe,OAAA,CAAA9B,OAAA,GAAAS,aAAA", "ignoreList": []}